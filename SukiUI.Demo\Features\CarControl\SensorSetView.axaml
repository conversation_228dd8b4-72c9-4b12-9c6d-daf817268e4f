<UserControl x:Class="SukiUI.Demo.Features.CarControl.SensorSetView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:carControl="clr-namespace:SukiUI.Demo.Features.CarControl"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             d:DesignHeight="480"
             d:DesignWidth="960"
             x:DataType="carControl:SensorSetViewModel"
             mc:Ignorable="d">
    <StackPanel>
        <suki:GlassCard Margin="40"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
            <suki:GroupBox Header="项目">
                <StackPanel Classes="HeaderContent" />

            </suki:GroupBox>

        </suki:GlassCard>
        <suki:GlassCard Margin="40"
                        HorizontalAlignment="Stretch"
                        VerticalAlignment="Stretch">
            <suki:GroupBox Header="录像机/摄像头">

                <StackPanel Classes="HeaderContent" Orientation="Horizontal">
                    <!--<TextBlock Text="IP" />-->
                    <TextBox Width="100"
                             VerticalAlignment="Center"
                             suki:TextBoxExtensions.Prefix="IP" />
                    <TextBlock VerticalAlignment="Center" Text="用户名" />
                    <TextBox suki:TextBoxExtensions.Prefix="用户名" />
                    <!--<TextBlock Text="密码" />-->
                    <TextBox suki:TextBoxExtensions.Prefix="密码" />
                    <!--<TextBlock Width="100" Text="通道号" />-->

                    <NumericUpDown ShowButtonSpinner="True" Value="1" />
                </StackPanel>

            </suki:GroupBox>

        </suki:GlassCard>
    </StackPanel>
</UserControl>
