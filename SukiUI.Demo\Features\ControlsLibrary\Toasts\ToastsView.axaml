<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.Toasts.ToastsView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:toasts="clr-namespace:SukiUI.Demo.Features.ControlsLibrary.Toasts"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="toasts:ToastsViewModel"
             mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Toasts">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        SukiUI contains an API for creating and dismissing 4 kinds of toasts, with the ability to set limits on the number of toasts.
                    </TextBlock>
                    <TextBlock>
                        It is also possible to set the toasts to appear in any of the 4 corners, by default they are displayed in the bottom right.
                    </TextBlock>
                    <TextBlock>
                        A callback can be provided also which will be invoked if a toast is closed by being clicked by a user.
                    </TextBlock>
                    <TextBlock>
                        As the API for this resides entirely in C#, the source for the examples can be viewed on GitHub at the URL below.
                    </TextBlock>
                    <HyperlinkButton
                        NavigateUri="https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/Toasts/ToastsViewModel.cs"
                        Content="Source Here." />
                    <Button Command="{Binding DismissAllToastsCommand}" Content="Click To Dismiss All Active Toasts" HorizontalAlignment="Left" Classes="Flat"/>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard>
                    <suki:GroupBox Header="Info Toast">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowInfoToastCommand}"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Success Toast">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowSuccessToastCommand}"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Warning Toast">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowWarningToastCommand}"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Error Toast">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowErrorToastCommand}"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                
                <suki:GlassCard>
                    <suki:GroupBox Header="Loading Toast">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowLoadingToastCommand}"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="3 Info Toasts">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowThreeInfoToastsCommand}"
                                Content="Show Toasts" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Callback Toast">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowToastWithCallbackCommand}"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Action Toast">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowActionToastCommand}"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Separate Toast Window">
                        <Button Margin="15,10,15,0"
                                Command="{Binding ShowToastWindowCommand}"
                                Content="Open" />
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</UserControl>