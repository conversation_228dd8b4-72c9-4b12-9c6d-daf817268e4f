# Final conversion to GB2312 with proper Chinese character handling
$filePath = "SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs"

Write-Host "Converting to GB2312 with Chinese character preservation..." -ForegroundColor Green

if (-not (Test-Path $filePath)) {
    Write-Host "File not found" -ForegroundColor Red
    exit 1
}

# Create backup with timestamp
$timestamp = Get-Date -Format "yyyyMMdd_HHmmss"
$backupPath = "$filePath.utf8backup_$timestamp"
Copy-Item $filePath $backupPath
Write-Host "UTF-8 backup created: $backupPath" -ForegroundColor Yellow

try {
    # Read as UTF-8 to preserve Chinese characters
    $content = Get-Content $filePath -Raw -Encoding UTF8
    
    # Verify Chinese characters are present
    if ($content -match "[\u4e00-\u9fff]") {
        Write-Host "Chinese characters detected, proceeding with conversion..." -ForegroundColor Green
        
        # Count Chinese characters
        $chineseMatches = [regex]::Matches($content, "[\u4e00-\u9fff]")
        Write-Host "Found $($chineseMatches.Count) Chinese characters" -ForegroundColor Cyan
    } else {
        Write-Host "Warning: No Chinese characters detected" -ForegroundColor Yellow
    }
    
    # Save as ANSI (GB2312 on Chinese systems)
    # Using Out-File with Default encoding which maps to system default (GB2312)
    $content | Out-File $filePath -Encoding Default -NoNewline
    
    Write-Host "File converted to GB2312 successfully" -ForegroundColor Green
    
    # Verify file size
    $originalSize = (Get-Item $backupPath).Length
    $newSize = (Get-Item $filePath).Length
    Write-Host "Original size: $originalSize bytes" -ForegroundColor Cyan
    Write-Host "New size: $newSize bytes" -ForegroundColor Cyan
    
    Write-Host "`nConversion completed!" -ForegroundColor Green
    Write-Host "The file is now in GB2312 encoding." -ForegroundColor White
    Write-Host "UTF-8 backup saved as: $backupPath" -ForegroundColor Yellow
    
} catch {
    Write-Host "Error during conversion: $($_.Exception.Message)" -ForegroundColor Red
    
    # Restore from backup
    if (Test-Path $backupPath) {
        Copy-Item $backupPath $filePath -Force
        Write-Host "File restored from backup" -ForegroundColor Yellow
    }
    exit 1
}

Write-Host "`nNote: The file will appear as garbled text in UTF-8 editors," -ForegroundColor Cyan
Write-Host "but will display correctly in GB2312-compatible editors." -ForegroundColor Cyan
