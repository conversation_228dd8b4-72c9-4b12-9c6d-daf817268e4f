﻿
using System.Runtime.InteropServices;

using System.Text;


namespace SukiUI.Demo.Bll
{
    public class HeMaQiHelper
    {
        /// <summary>
        /// 上传图片
        /// </summary>

        /// <returns></returns>
        public static bool upLoadPic(int m_lUserID, uint picNo, string picName, string picPath)
        {
            CHCNetSDK.NET_DVR_PICTURECFG m_struPicCfg = new CHCNetSDK.NET_DVR_PICTURECFG();

            m_struPicCfg.dwSize = (uint)Marshal.SizeOf(m_struPicCfg);
            m_struPicCfg.byUseType = 4;
            //m_struPicCfg.byWallNo = 1;
            m_struPicCfg.dwVideoWall = 1;

            m_struPicCfg.bySequence = (byte)picNo;
            byte[] bb = new byte[32];
            byte[] bb2 = Encoding.UTF8.GetBytes(picName);
            Array.Copy(bb2, bb, bb2.Length);
            m_struPicCfg.sPicName = bb;

            ////图片必须为 24 位 BMP 格式图片，宽必须是 16 像素的整数倍，高必须是 8 像素的整数倍，图片的分辨
            ////率最小为 32X32 像素，最大为 960X536 像素

            var IupLoadHandle = CHCNetSDK.NET_DVR_PicUpload(m_lUserID, picPath, ref m_struPicCfg);
            if (IupLoadHandle == -1)
            {
                var iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                return false;
            }
            var result = 0;
            var times = 0;
            while (true)
            {
                result = CHCNetSDK.NET_DVR_GetPicUploadState(IupLoadHandle);

                times++;
                // -1 表示失败，其他值定义如下：1- 完成，2- 正在上传，3- 上传失败，4- 未知错误
                if (result != 2 || times > 7)
                {
                    break;
                }
                Thread.Sleep(300);
            }
            //关闭上传句柄
            var closeFlag = CHCNetSDK.NET_DVR_CloseUploadHandle(IupLoadHandle);

            return result == 1;


        }

        /// <summary>
        /// 设置图片窗口参数
        /// </summary>
        /// <param name="m_lUserID">登录用户ID</param>
        /// <param name="winNo">图片窗口号</param>
        /// <param name="picNo">图片序号</param>
        /// <returns></returns>
        public static bool SetPicWinPar(int m_lUserID, uint winNo, uint picNo)
        {
            //var result = true;
            //窗口号(组合)：1字节电视墙号+1字节保留+2字节窗口号
            uint dwWinNum = winNo;

            //窗口相关参数
            CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG m_struWinParamCfg = new CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG();
            Int32 nSize = Marshal.SizeOf(m_struWinParamCfg);
            IntPtr lpOutBuffer = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(m_struWinParamCfg, lpOutBuffer, false);

            UInt32 dwReturn = 0;
            if (
                !CHCNetSDK.NET_DVR_GetDVRConfig(m_lUserID, CHCNetSDK.NET_DVR_GET_OUTPUT_PIC_WIN_CFG, (int)dwWinNum,
                    lpOutBuffer, (UInt32)nSize, ref dwReturn))
            {
                var iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                Marshal.FreeHGlobal(lpOutBuffer);
                return false;
            }
            else
            {
                m_struWinParamCfg =
                    (CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG)
                        Marshal.PtrToStructure(lpOutBuffer, typeof(CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG));
                //MessageBox.Show(Convert.ToString(m_struWinParamCfg.dwOutputPicWinNo + ":" + m_struWinParamCfg.dwOutputPicNo)); 
            }
            // 设置图片参数
            m_struWinParamCfg.dwOutputPicNo = picNo;

            #region 尝试解决图片配置丢失问题。
            m_struWinParamCfg.byEnable = 1;
            if (AppStaticData.siheyiconfig.Zybj.Value)
            {
                if (picNo % 2 == 0)
                {
                    m_struWinParamCfg.struRect.dwXCoordinate = 960;

                }
                else
                {
                    //if (JAJProGroup.WinApp.YuanChengJianKong.Properties.Settings.Default.Zybj)
                    //{
                    //    m_struWinParamCfg.struRect.dwXCoordinate = 960;
                    //}

                    m_struWinParamCfg.struRect.dwXCoordinate = 0;


                }


                int tpgd = AppStaticData.siheyiconfig.Tpgd.Value;

                if (tpgd != 960)
                {
                    int temp = 960 - tpgd;
                    m_struWinParamCfg.struRect.dwYCoordinate = (uint)(960 + temp);
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = (uint)tpgd;
                }
                else
                {
                    m_struWinParamCfg.struRect.dwYCoordinate = 960;
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = 960;
                }

            }
            //图片左右布局
            else
            {
                //if (picNo % 2 == 0)
                //{
                m_struWinParamCfg.struRect.dwXCoordinate = 960;

                //}
                //else
                //{
                //if (JAJProGroup.WinApp.YuanChengJianKong.Properties.Settings.Default.Zybj)
                //{
                //    m_struWinParamCfg.struRect.dwXCoordinate = 960;
                //}

                //m_struWinParamCfg.struRect.dwXCoordinate = 0;


                //}
                if (picNo % 2 == 0)
                {
                    m_struWinParamCfg.struRect.dwYCoordinate = 960;
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = 960;
                }
                else
                {
                    m_struWinParamCfg.struRect.dwYCoordinate = 0;
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = 960;
                }
                //int tpgd = JAJProGroup.WinApp.YuanChengJianKong.Properties.Settings.Default.Tpgd;

                //if (tpgd != 960)
                //{
                //    int temp = 960 - tpgd;
                //    m_struWinParamCfg.struRect.dwYCoordinate = (uint)(960 + temp);
                //    m_struWinParamCfg.struRect.dwWidth = 960;
                //    m_struWinParamCfg.struRect.dwHeight = (uint)tpgd;
                //}
                //else
                //{
                //    m_struWinParamCfg.struRect.dwYCoordinate = 960;
                //    m_struWinParamCfg.struRect.dwWidth = 960;
                //    m_struWinParamCfg.struRect.dwHeight = 960;
                //}
            }

            #endregion

            nSize = Marshal.SizeOf(m_struWinParamCfg);
            lpOutBuffer = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(m_struWinParamCfg, lpOutBuffer, false);
            if (!CHCNetSDK.NET_DVR_SetDVRConfig(m_lUserID, CHCNetSDK.NET_DVR_SET_OUTPUT_PIC_WIN_CFG, (int)dwWinNum,
                lpOutBuffer, (UInt32)nSize))
            {

                Marshal.FreeHGlobal(lpOutBuffer);
                return false;
            }
            else
            {
                m_struWinParamCfg =
                    (CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG)
                        Marshal.PtrToStructure(lpOutBuffer, typeof(CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG));
                Marshal.FreeHGlobal(lpOutBuffer);
                return true;
                
            }

        }
        public static bool SetPicWinPar6816M(int m_lUserID, uint winNo, uint picNo)
        {
            //var result = true;
            //窗口号(组合)：1字节电视墙号+1字节保留+2字节窗口号
            uint dwWinNum = winNo;

            //窗口相关参数
            CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG m_struWinParamCfg = new CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG();
            Int32 nSize = Marshal.SizeOf(m_struWinParamCfg);
            //IntPtr lpOutBuffer = Marshal.AllocHGlobal(nSize);
            //Marshal.StructureToPtr(m_struWinParamCfg, lpOutBuffer, false);
            m_struWinParamCfg.dwSize = (uint)nSize;
            UInt32 dwReturn = 0;
            //if (
            //    !CHCNetSDK.NET_DVR_GetDVRConfig(m_lUserID, CHCNetSDK.NET_DVR_GET_OUTPUT_PIC_WIN_CFG, (int)dwWinNum,
            //        lpOutBuffer, (UInt32)nSize, ref dwReturn))
            //{

            //    Marshal.FreeHGlobal(lpOutBuffer);
            //    return false;
            //}
            //else
            //{
            //    m_struWinParamCfg =
            //        (CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG)
            //            Marshal.PtrToStructure(lpOutBuffer, typeof(CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG));
            //    //MessageBox.Show(Convert.ToString(m_struWinParamCfg.dwOutputPicWinNo + ":" + m_struWinParamCfg.dwOutputPicNo)); 
            //}
            // 设置图片参数
            m_struWinParamCfg.dwOutputPicNo = picNo;

            #region 尝试解决图片配置丢失问题。
            m_struWinParamCfg.byEnable = 1;
            if (!AppStaticData.siheyiconfig.Zybj.Value)
            {
                if (picNo % 2 == 0)
                {
                    m_struWinParamCfg.struRect.dwXCoordinate = 960;

                }
                else
                {
                    //if (JAJProGroup.WinApp.YuanChengJianKong.Properties.Settings.Default.Zybj)
                    //{
                    //    m_struWinParamCfg.struRect.dwXCoordinate = 960;
                    //}

                    m_struWinParamCfg.struRect.dwXCoordinate = 0;


                }


                int tpgd = AppStaticData.siheyiconfig.Tpgd.Value;

                if (tpgd != 960)
                {
                    int temp = 960 - tpgd;
                    m_struWinParamCfg.struRect.dwYCoordinate = (uint)(960 + temp);
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = (uint)tpgd;
                }
                else
                {
                    m_struWinParamCfg.struRect.dwYCoordinate = 960;
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = 960;
                }

            }
            //图片左右布局
            else
            {
                //if (picNo % 2 == 0)
                //{
                    m_struWinParamCfg.struRect.dwXCoordinate = 960;

                //}
                //else
                //{
                //if (JAJProGroup.WinApp.YuanChengJianKong.Properties.Settings.Default.Zybj)
                //{
                //    m_struWinParamCfg.struRect.dwXCoordinate = 960;
                //}

                //m_struWinParamCfg.struRect.dwXCoordinate = 0;


                //}
                if (picNo % 2 == 0)
                {
                    m_struWinParamCfg.struRect.dwYCoordinate = 960;
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = 960;
                }
                else
                {
                    m_struWinParamCfg.struRect.dwYCoordinate = 0;
                    m_struWinParamCfg.struRect.dwWidth = 960;
                    m_struWinParamCfg.struRect.dwHeight = 960;
                }
                //int tpgd = JAJProGroup.WinApp.YuanChengJianKong.Properties.Settings.Default.Tpgd;

                //if (tpgd != 960)
                //{
                //    int temp = 960 - tpgd;
                //    m_struWinParamCfg.struRect.dwYCoordinate = (uint)(960 + temp);
                //    m_struWinParamCfg.struRect.dwWidth = 960;
                //    m_struWinParamCfg.struRect.dwHeight = (uint)tpgd;
                //}
                //else
                //{
                //    m_struWinParamCfg.struRect.dwYCoordinate = 960;
                //    m_struWinParamCfg.struRect.dwWidth = 960;
                //    m_struWinParamCfg.struRect.dwHeight = 960;
                //}
            }
           
            #endregion

            //nSize = Marshal.SizeOf(m_struWinParamCfg);
            IntPtr lpOutBuffer = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(m_struWinParamCfg, lpOutBuffer, false);
            if (!CHCNetSDK.NET_DVR_SetDVRConfig(m_lUserID, CHCNetSDK.NET_DVR_SET_OUTPUT_PIC_WIN_CFG, (int)dwWinNum,
                lpOutBuffer, (UInt32)nSize))
            {
                var iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                Marshal.FreeHGlobal(lpOutBuffer);
                return false;
            }
            else
            {
                m_struWinParamCfg =
                    (CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG)
                        Marshal.PtrToStructure(lpOutBuffer, typeof(CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG));
                Marshal.FreeHGlobal(lpOutBuffer);
                return true;

            }

        }
        /// <summary>
        /// 设置图片窗口参数按开窗设置
        /// </summary>
        /// <param name="m_lUserID">登录用户ID</param>
        /// <param name="winNo">图片窗口号</param>
        /// <param name="picNo">图片序号</param>
        /// <returns></returns>
        public static bool SetPicWinParByDefault(int m_lUserID, uint winNo, uint picNo)
        {
            //var result = true;
            //窗口号(组合)：1字节电视墙号+1字节保留+2字节窗口号
            uint dwWinNum = winNo;
           


            //窗口相关参数
            CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG m_struWinParamCfg = new CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG();
            Int32 nSize = Marshal.SizeOf(m_struWinParamCfg);
            IntPtr lpOutBuffer = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(m_struWinParamCfg, lpOutBuffer, false);

            UInt32 dwReturn = 0;
            if (
                !CHCNetSDK.NET_DVR_GetDVRConfig(m_lUserID, CHCNetSDK.NET_DVR_GET_OUTPUT_PIC_WIN_CFG, (int)dwWinNum,
                    lpOutBuffer, (UInt32)nSize, ref dwReturn))
            {

                Marshal.FreeHGlobal(lpOutBuffer);
                return false;
            }
            else
            {
                m_struWinParamCfg =
                    (CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG)
                        Marshal.PtrToStructure(lpOutBuffer, typeof(CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG));
                //MessageBox.Show(Convert.ToString(m_struWinParamCfg.dwOutputPicWinNo + ":" + m_struWinParamCfg.dwOutputPicNo)); 
            }
            // 设置图片参数
            m_struWinParamCfg.dwOutputPicNo = picNo;
            #region 尝试解决图片配置丢失问题。
            m_struWinParamCfg.byEnable = 1;
            //m_struWinParamCfg.struRect.dwXCoordinate = 960;
            //m_struWinParamCfg.struRect.dwXCoordinate = 960;
            //m_struWinParamCfg.struRect.dwWidth = 960;
            //m_struWinParamCfg.struRect.dwHeight = 960;
            #endregion

            nSize = Marshal.SizeOf(m_struWinParamCfg);
            lpOutBuffer = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(m_struWinParamCfg, lpOutBuffer, false);
            if (!CHCNetSDK.NET_DVR_SetDVRConfig(m_lUserID, CHCNetSDK.NET_DVR_SET_OUTPUT_PIC_WIN_CFG, (int)dwWinNum,
                lpOutBuffer, (UInt32)nSize))
            {

                Marshal.FreeHGlobal(lpOutBuffer);
                return false;
            }
            else
            {
                m_struWinParamCfg =
                    (CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG)
                        Marshal.PtrToStructure(lpOutBuffer, typeof(CHCNetSDK.NET_DVR_OUTPUT_PIC_CFG));
                Marshal.FreeHGlobal(lpOutBuffer);
                return true;
                //MessageBox.Show(Convert.ToString(m_struWinParamCfg.dwOutputPicWinNo + ":" + m_struWinParamCfg.dwOutputPicNo)); //
            }

        }
        /// <summary>
        /// 
        /// </summary>
        /// <param name="userName">摄像头用户名</param>
        /// <param name="password">摄像头密码</param>
        /// <param name="ip">摄像头IP</param>
        /// <param name="port">摄像头端口</param>
        /// <param name="chanNo">通道号，默认1</param>
        /// <param name="transMode">0主码流，1：子码流</param>
        /// <returns></returns>
        public static bool StartDecode(int m_lUserID, uint dwDecChanNum, string ip, string userName, string password,
           int chanNo = 1, ushort port = 8000, int transMode = 0)
        {
            CHCNetSDK.NET_DVR_PU_STREAM_CFG_V41 m_struStreamCfgV41 = new CHCNetSDK.NET_DVR_PU_STREAM_CFG_V41();
            CHCNetSDK.NET_DVR_DEC_STREAM_DEV_EX m_struStreamDev = new CHCNetSDK.NET_DVR_DEC_STREAM_DEV_EX();
            CHCNetSDK.NET_DVR_IPC_PROTO_LIST m_struProtoList = new CHCNetSDK.NET_DVR_IPC_PROTO_LIST();
            m_struStreamCfgV41.dwSize = (uint)Marshal.SizeOf(m_struStreamCfgV41);
            m_struStreamCfgV41.byStreamMode = (byte)1;

            //通过IP地址或者域名从设备或者流媒体服务器取流
            if (m_struStreamCfgV41.byStreamMode == 1)
            {
                
                m_struStreamDev.struDevChanInfo.dwChannel = (uint)chanNo;
                m_struStreamDev.struDevChanInfo.byChannel = (byte)chanNo;


                m_struStreamDev.struDevChanInfo.byAddress = ip;
                m_struStreamDev.struDevChanInfo.wDVRPort = port;
                m_struStreamDev.struDevChanInfo.byTransProtocol = 0; //0tcp,udp
                m_struStreamDev.struDevChanInfo.byTransMode = (byte)transMode; //0主码流、1子码流
                m_struStreamDev.struDevChanInfo.byFactoryType = (byte)0; //海康
                m_struStreamDev.struDevChanInfo.sUserName = userName;
                m_struStreamDev.struDevChanInfo.sPassword = password;
               
                m_struStreamDev.struStreamMediaSvrCfg.byValid = 0;
              

                uint dwUnionSize = (uint)Marshal.SizeOf(m_struStreamCfgV41.uDecStreamMode);
                IntPtr ptrStreamUnion = Marshal.AllocHGlobal((Int32)dwUnionSize);
                Marshal.StructureToPtr(m_struStreamDev, ptrStreamUnion, false);
                m_struStreamCfgV41.uDecStreamMode =
                    (CHCNetSDK.NET_DVR_DEC_STREAM_MODE)
                        Marshal.PtrToStructure(ptrStreamUnion, typeof(CHCNetSDK.NET_DVR_DEC_STREAM_MODE));
                Marshal.FreeHGlobal(ptrStreamUnion);
            }
            if (!CHCNetSDK.NET_DVR_MatrixStartDynamic_V41(m_lUserID, dwDecChanNum, ref m_struStreamCfgV41))
            {
                //iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                //strErr = "NET_DVR_MatrixStartDynamic_V41 failed, error code= " + iLastErr;
                ////启动动态解码失败，输出错误号 Failed to start dynamic decoding and output the error code
                //MessageBox.Show(strErr);
                return false;
            }
            else
            {
                //MessageBox.Show("Successful to start dynamic decoding!");
                return true;
            }

        }

        public static bool StopDecode(int m_lUserID, uint dwDecChanNum)
        {
            if (!CHCNetSDK.NET_DVR_MatrixStopDynamic(m_lUserID, dwDecChanNum))
            {
                //iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                //strErr = "NET_DVR_MatrixStopDynamic failed, error code= " + iLastErr;
                ////停止动态解码失败，输出错误号 Failed to stop dynamic decoding and output the error code
                //MessageBox.Show(strErr);
                return false;
            }
            else
            {
                return true;
                //MessageBox.Show("Successful to stop dynamic decoding!");
            }
        }

        public static List<string> GetAllPicWinPar(int m_lUserID)
        {
            var result = new List<string>();
            CHCNetSDK.NET_DVR_DISPLAYCFG m_struDispNoCfg = new CHCNetSDK.NET_DVR_DISPLAYCFG();
            //批量获取显示输出口位置配置
            uint uDisplayCount = 0;
            Int32 nWallDisplaySize = Marshal.SizeOf(typeof(CHCNetSDK.NET_DVR_VIDEOWALLDISPLAYPOSITION));
            int outBufSize = 4 + 512 * (int)nWallDisplaySize;
            byte[] pTemp = new byte[outBufSize];
            IntPtr lpOutBuf = Marshal.AllocHGlobal(outBufSize);
            Marshal.Copy(pTemp, 0, lpOutBuf, outBufSize);

            //每次获取一个显示输出口位置参数
            if (
                !CHCNetSDK.NET_DVR_GetDeviceConfig(m_lUserID, CHCNetSDK.NET_DVR_GET_VIDEOWALLDISPLAYPOSITION, 0xffffffff,
                    IntPtr.Zero, 0, IntPtr.Zero, lpOutBuf, (UInt32)outBufSize))
            {
                //iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                //strErr = "NET_DVR_GET_VIDEOWALLDISPLAYPOSITION failed, error code= " + iLastErr;
                ////获取电视墙屏幕参数失败，输出错误号 Failed to get the wall parameters of device and output the error code
                //MessageBox.Show(strErr);
            }
            else
            {
                Marshal.Copy(lpOutBuf, pTemp, 0, (int)outBufSize);
                uDisplayCount = ((uint)pTemp[0] & 0xff)
                                | (((uint)pTemp[1] << 8) & 0xff00)
                                | (((uint)pTemp[2] << 16) & 0xff0000)
                                | (((uint)pTemp[3] << 24) & 0xff000000);
            }
            //获取显示输出口号信息
            Int32 nSize = Marshal.SizeOf(m_struDispNoCfg);
            IntPtr lpOutBuffer = Marshal.AllocHGlobal(nSize);
            Marshal.StructureToPtr(m_struDispNoCfg, lpOutBuffer, false);

            UInt32 dwReturn = 0;
            if (
                !CHCNetSDK.NET_DVR_GetDVRConfig(m_lUserID, CHCNetSDK.NET_DVR_GET_VIDEOWALLDISPLAYNO, 0, lpOutBuffer,
                    (UInt32)nSize, ref dwReturn))
            {
                //iLastErr = CHCNetSDK.NET_DVR_GetLastError();
                //strErr = "NET_DVR_WALLWINPARAM_GET failed, error code= " + iLastErr;
                ////获取窗口相关参数失败，输出错误号 Failed to get the window parameters of device and output the error code
                //MessageBox.Show(strErr);
            }
            else
            {
                m_struDispNoCfg =
                    (CHCNetSDK.NET_DVR_DISPLAYCFG)
                        Marshal.PtrToStructure(lpOutBuffer, typeof(CHCNetSDK.NET_DVR_DISPLAYCFG));
            }
            Marshal.FreeHGlobal(lpOutBuffer);
            for (int i = 0; i < uDisplayCount; i++)
            {
                uint dwDisplayNo = m_struDispNoCfg.struDisplayParam[i].dwDisplayNo;

                if (dwDisplayNo > 0)
                {
                    string displayno = Convert.ToString(dwDisplayNo);
                    //string displaynoCom = Convert.ToString(dwDisplayNo >> 24) + "_" +
                    //                      Convert.ToString((dwDisplayNo >> 16) & 0xff) + "_" +
                    //                      Convert.ToString(dwDisplayNo & 0xffff); //显示输出口号组合方式显示
                    string displaynoCom = Convert.ToString(dwDisplayNo >> 24) + "_" +

                                          Convert.ToString(dwDisplayNo & 0xffff); //显示输出口号组合方式显示
                    result.Add(displaynoCom);
                }
            }
            return result;
        }

        public static bool DelPic(int m_lUserID, uint picno)
        {
            IntPtr lpInBuffer = Marshal.AllocHGlobal(4);
            Marshal.StructureToPtr(picno, lpInBuffer, false);
            return CHCNetSDK.NET_DVR_RemoteControl(m_lUserID, CHCNetSDK.NET_DVR_DELETE_OUPUT_PIC, lpInBuffer, 4);
        }

        public static object uploadlockobj = new object();
        public static bool upLoadPic6816M(int m_lUserID, uint picNo, string picName, string picPath, uint dwVideoWallNo,int uploadhandle)
        {
           
            if (upLoadPic(m_lUserID, picNo, picName, picPath))
            {
                if (AppStaticData.siheyiconfig.DefalutPicPos.Value)
                {
                    if (SetPicWinParByDefault(m_lUserID, dwVideoWallNo, picNo))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
                else
                {
                    if (SetPicWinPar6816M(m_lUserID, dwVideoWallNo, picNo))
                    {
                        return true;
                    }
                    else
                    {
                        return false;
                    }
                }
               

            }
            else
            {
                return false;
            }

        }
        /// <summary>
        /// 长连接，会导致内存上升
        /// </summary>
        /// <param name="m_lUserID"></param>
        /// <param name="picNo"></param>
        /// <param name="picName"></param>
        /// <param name="picPath"></param>
        /// <param name="dwVideoWallNo"></param>
        /// <returns></returns>
        public static bool upLoadPic6816M2(int m_lUserID, uint picNo, string picName, string picPath, uint dwVideoWallNo, int uploadhandle)
        {

            CHCNetSDK.NET_DVR_SEND_PARAM_IN m_send_in = new CHCNetSDK.NET_DVR_SEND_PARAM_IN();
            //Int32 nInSize = Marshal.SizeOf(m_send_in);
            //IntPtr lpInBuffer = Marshal.AllocHGlobal(nInSize);
            //Marshal.StructureToPtr(m_send_in, lpInBuffer, false);
            var uploadHandle = CHCNetSDK.NET_DVR_UploadFile_V40(m_lUserID,
                (uint)CHCNetSDK.NET_SDK_UPLOAD_TYPE.UPLOAD_PIC_BY_BUF, IntPtr.Zero,
                (uint) 0, IntPtr.Zero, IntPtr.Zero, 0);
            //Marshal.FreeHGlobal(lpInBuffer);

            CHCNetSDK.NET_DVR_PICTURECFG m_struPicCfg = new CHCNetSDK.NET_DVR_PICTURECFG();

            m_struPicCfg.dwSize = (uint)Marshal.SizeOf(m_struPicCfg);
            m_struPicCfg.byUseType = 4;
            //这个值最大为96，超过96报错
            m_struPicCfg.bySequence = (byte)picNo;           
            m_struPicCfg.struBasemapCfg.byMapNum = 1;
            m_struPicCfg.struBasemapCfg.byScreenIndex = 1;
            byte[] bb = new byte[32];
            byte[] bb2 = Encoding.UTF8.GetBytes(picName);
            Array.Copy(bb2, bb, bb2.Length);
            m_struPicCfg.dwVideoWall = dwVideoWallNo;
            m_struPicCfg.sPicName = bb;
            m_struPicCfg.byOverlayEnabled = 1;
            m_struPicCfg.byPictureType = 2; //图片类型，1-bmp，2-jpg，3-png，……
            m_struPicCfg.byShowEnabled = 1;
            m_struPicCfg.byFlash = 0;
            m_struPicCfg.byTranslucent = 0;
            //图片
            var imageByte = GetPictureData(picPath);
            var dwSendDataLen = imageByte.Length;
            IntPtr pSendData = ArrayToIntptr(imageByte);
            m_send_in.pSendData = pSendData;
            m_send_in.dwSendDataLen = (uint)dwSendDataLen;
            m_send_in.struTime = new CHCNetSDK.NET_DVR_TIME_V30();
            var pictime = DateTime.Now;
            m_send_in.struTime.wYear = (ushort) pictime.Year;
            m_send_in.struTime.byMonth = (byte) pictime.Day;
            m_send_in.struTime.byHour = (byte)pictime.Hour;
            m_send_in.struTime.byMinute = (byte)pictime.Minute;
            m_send_in.struTime.bySecond = (byte)pictime.Second;
            m_send_in.struTime.wMilliSec = (ushort)pictime.Millisecond;
            m_send_in.byPicType = 1; //图片格式,1-jpg,2-bmp,3-png,4-SWF,5-GIF
            m_send_in.byPicURL = 0;
            m_send_in.dwPicMangeNo = picNo;
            m_send_in.sPicName = bb;
            m_send_in.dwPicDisplayTime = 2;
            var psize = Marshal.SizeOf(m_struPicCfg);
            IntPtr pSendAppendData = Marshal.AllocHGlobal(psize);
            Marshal.StructureToPtr(m_struPicCfg, pSendAppendData, true);
            m_send_in.pSendAppendData = pSendAppendData;
            m_send_in.dwSendAppendDataLen = (uint)psize;
            var result = CHCNetSDK.NET_DVR_UploadSend(uploadHandle, ref m_send_in, IntPtr.Zero);
            if (result == -1)
            {
                CHCNetSDK.NET_DVR_UploadClose(uploadHandle);
                Marshal.FreeHGlobal(pSendData);
                Marshal.FreeHGlobal(pSendAppendData);
                return false;
            }
            var upReslut = 0;
            uint process = 0;
            int i = 0;
            try
            {
                while (i < 7)
                {
                    //-1表示失败，其他值为上传的状态值，如下：
                    // 1:上传成功
                    // 2:正在上传
                    // 3:上传失败
                    // 4:网络断开，状态未知
                    upReslut = CHCNetSDK.NET_DVR_GetUploadState(uploadHandle, ref process);
                    if (upReslut == 1)
                    {
                        break;
                    }

                    i++;
                    Thread.Sleep(300);
                }
                return upReslut == 1;
            }
            catch (Exception e)
            {
                return false;
            }
            finally
            {
                var closeResult= CHCNetSDK.NET_DVR_UploadClose(uploadHandle);
                Marshal.FreeHGlobal(pSendData);
                Marshal.FreeHGlobal(pSendAppendData);
            }

        }
        /// <summary>
        /// 为每个图片窗口创建一个UPloadHandle，避免内存上升
        /// </summary>
        /// <param name="m_lUserID"></param>
        /// <param name="picNo"></param>
        /// <param name="picName"></param>
        /// <param name="picPath"></param>
        /// <param name="dwVideoWallNo"></param>
        /// <returns></returns>
        public static bool upLoadPic6816M1(int m_lUserID, uint picNo, string picName, string picPath, uint dwVideoWallNo,int handel=-1)
        {
            var uploadHandle = handel;
            if (uploadHandle == -1)
            {

                uploadHandle = CHCNetSDK.NET_DVR_UploadFile_V40(m_lUserID,
                    (uint)CHCNetSDK.NET_SDK_UPLOAD_TYPE.UPLOAD_PIC_BY_BUF, IntPtr.Zero,
                    0, IntPtr.Zero, IntPtr.Zero, 0);

            }

            if (uploadHandle==-1)
            {
                return false;
            }

            CHCNetSDK.NET_DVR_PICTURECFG m_struPicCfg = new CHCNetSDK.NET_DVR_PICTURECFG();

            m_struPicCfg.dwSize = (uint)Marshal.SizeOf(m_struPicCfg);
            m_struPicCfg.byUseType = 4;
            //这个值最大为96，超过96报错
            m_struPicCfg.bySequence = (byte)picNo;
            m_struPicCfg.struBasemapCfg.byMapNum = 1;
            m_struPicCfg.struBasemapCfg.byScreenIndex = 1;
            //m_struPicCfg.struBasemapCfg.wSourHeight = 960;
            //m_struPicCfg.struBasemapCfg.wSourWidth = 960;

            byte[] bb = new byte[32];
            byte[] bb2 = Encoding.UTF8.GetBytes(picName);
            Array.Copy(bb2, bb, bb2.Length);
            m_struPicCfg.dwVideoWall = dwVideoWallNo;

            m_struPicCfg.sPicName = bb;
            m_struPicCfg.byOverlayEnabled = 1;
            m_struPicCfg.byPictureType = 2; //图片类型，1-bmp，2-jpg，3-png，……
            m_struPicCfg.byShowEnabled = 1;
            m_struPicCfg.byFlash = 0;
            m_struPicCfg.byTranslucent = 0;
            CHCNetSDK.NET_DVR_SEND_PARAM_IN m_send_in = new CHCNetSDK.NET_DVR_SEND_PARAM_IN();
            //图片
            var imageByte = GetPictureData(picPath);
            var dwSendDataLen = imageByte.Length;
            IntPtr pSendData = ArrayToIntptr(imageByte);


            m_send_in.pSendData = pSendData;
            m_send_in.dwSendDataLen = (uint)dwSendDataLen;
            m_send_in.struTime = new CHCNetSDK.NET_DVR_TIME_V30();
            var pictime = DateTime.Now;
            m_send_in.struTime.wYear = (ushort)pictime.Year;
            m_send_in.struTime.byMonth = (byte)pictime.Day;
            m_send_in.struTime.byHour = (byte)pictime.Hour;
            m_send_in.struTime.byMinute = (byte)pictime.Minute;
            m_send_in.struTime.bySecond = (byte)pictime.Second;
            m_send_in.struTime.wMilliSec = (ushort)pictime.Millisecond;

            m_send_in.byPicType = 1; //图片格式,1-jpg,2-bmp,3-png,4-SWF,5-GIF
            m_send_in.byPicURL = 0;
            m_send_in.dwPicMangeNo = picNo;
            m_send_in.sPicName = bb;
            m_send_in.dwPicDisplayTime = 2;
            var psize = Marshal.SizeOf(m_struPicCfg);
            IntPtr pSendAppendData = Marshal.AllocHGlobal(psize);
            Marshal.StructureToPtr(m_struPicCfg, pSendAppendData, true);
            m_send_in.pSendAppendData = pSendAppendData;
            m_send_in.dwSendAppendDataLen = (uint)psize;


            var result = CHCNetSDK.NET_DVR_UploadSend(uploadHandle, ref m_send_in, IntPtr.Zero);

            if (result == -1)
            {
                if (handel == -1)
                {
                    var closeResult = CHCNetSDK.NET_DVR_UploadClose(uploadHandle);
                }
                Marshal.FreeHGlobal(pSendData);
                Marshal.FreeHGlobal(pSendAppendData);
                return false;
            }

            var upReslut = 0;
            uint process = 0;
            int i = 0;
            try
            {
                while (i < 7)
                {
                    //-1表示失败，其他值为上传的状态值，如下：
                    // 1:上传成功
                    // 2:正在上传
                    // 3:上传失败
                    // 4:网络断开，状态未知
                    upReslut = CHCNetSDK.NET_DVR_GetUploadState(uploadHandle, ref process);
                    if (upReslut == 1)
                    {
                       
                        break;
                    }

                    i++;
                    Thread.Sleep(300);
                }

                return upReslut == 1;
                #region 测试
                //Random rd = new Random();

                //var tempResult = rd.Next(0, 3);
                //return tempResult == 1;
                #endregion


            }
            catch (Exception e)
            {
                return false;
            }
            finally
            {
                if (handel == -1)
                {
                    var closeResult = CHCNetSDK.NET_DVR_UploadClose(uploadHandle);
                }


                Marshal.FreeHGlobal(pSendData);
                Marshal.FreeHGlobal(pSendAppendData);


            }





        }
        /// <summary>
        /// 图片转二进制
        /// </summary>
        /// <param name="imagepath">图片地址</param>
        /// <returns>二进制</returns>
        public static byte[] GetPictureData(string imagepath)
        {
            //根据图片文件的路径使用文件流打开，并保存为byte[] 
            FileStream fs = new FileStream(imagepath, FileMode.Open);//可以是其他重载方法 
            byte[] byData = new byte[fs.Length];
            fs.Read(byData, 0, byData.Length);
            fs.Close();
            return byData;
        }
        public static IntPtr ArrayToIntptr(byte[] source)
        {
            if (source == null)
                return IntPtr.Zero;
            byte[] da = source;
            IntPtr ptr = Marshal.AllocHGlobal(da.Length);
            try
            {
                Marshal.Copy(da, 0, ptr, da.Length);
                return ptr;
            }
            finally
            {
                //Marshal.FreeHGlobal(ptr);
            }

        }
    }
}
