# CheckBox

收集用户多项选择的控件

## 展示

<img src="/controls/inputs/checkbox.gif"/>

## 示例

```xml
<CheckBox Content="Option One" IsChecked="True" />
<CheckBox Content="Option Two" />
<CheckBox IsThreeState="True" Content="Option Three" />
```

## 参阅

[Demo: SukiUI.Demo/Features/ControlsLibrary/TogglesView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/TogglesView.axaml)