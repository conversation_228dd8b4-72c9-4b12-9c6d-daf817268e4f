﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="using:System"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI"
                    x:ClassModifier="internal">

    <Design.PreviewWith>
        <Border Width="800"
                Height="800"
                Padding="50">
            <CalendarDatePicker  />
        </Border>
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type CalendarDatePicker}" TargetType="CalendarDatePicker">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Height" Value="38" />
        <Setter Property="BorderThickness" Value="0,0,0,1" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <ControlTemplate>
                <suki:GlassCard Height="{TemplateBinding Height}" CornerRadius="{DynamicResource SmallCornerRadius}" Classes="Discrete" >
                <Grid ColumnDefinitions="*,Auto">
                    <Grid.Styles>
                        <Style Selector="Button.CalendarDropDown">
                            <Setter Property="Template">
                                <ControlTemplate>
                                    
                                    <Grid Width="22"
                                          Height="20"
                                          Margin="0,0,16,0"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Background="Transparent"
                                          ClipToBounds="False"
                                          ColumnDefinitions="*,*,*,*"
                                          RowDefinitions="23*,19*,19*,19*">

                                        <Border Name="Background"
                                                Grid.Row="1"
                                                Grid.RowSpan="3"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="4"
                                                Margin="0,-1,0,0"
                                                BorderBrush="{DynamicResource SukiControlBorderBrush}"
                                                BorderThickness="1"
                                                CornerRadius="0,0,2,2" />

                                        <Border Grid.Row="0"
                                                Grid.Column="0"
                                                Grid.ColumnSpan="4"
                                                Background="{DynamicResource SukiPrimaryColor}"
                                                BorderBrush="{DynamicResource SukiControlBorderBrush}"
                                                BorderThickness="1"
                                                CornerRadius="3,3,0,0" />

                                        <TextBlock Grid.Row="1"
                                                   Grid.RowSpan="3"
                                                   Grid.Column="0"
                                                   Grid.ColumnSpan="4"
                                                   Margin="0,-1,0,0"
                                                   HorizontalAlignment="Center"
                                                   VerticalAlignment="Center"
                                                   FontSize="{DynamicResource FontSizeSmall}"
                                                   Foreground="{DynamicResource SukiLowText}"
                                                   Text="{Binding Source={x:Static sys:DateTime.Today}, Path=Day}" />

                                        <Ellipse Grid.Row="0"
                                                 Grid.Column="0"
                                                 Grid.ColumnSpan="4"
                                                 Width="2"
                                                 Height="2"
                                                 HorizontalAlignment="Center"
                                                 VerticalAlignment="Center"
                                                 Fill="{DynamicResource SukiControlBorderBrush}"
                                                 StrokeThickness="0" />
                                    </Grid>
                                 
                                </ControlTemplate>
                            </Setter>
                        </Style>

                        <Style Selector="Button.CalendarDropDown:pointerover /template/ Border#Background">
                            <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor5}" />
                        </Style>
                    </Grid.Styles>

                    <TextBox Name="PART_TextBox" Classes="NoShadow"
                             Grid.Column="0"
                             Padding="{TemplateBinding Padding}"
                             HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                             VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                             Background="{TemplateBinding Background}"
                             BorderBrush="{TemplateBinding BorderBrush}"
                             BorderThickness="{TemplateBinding BorderThickness}"
                             CornerRadius="{TemplateBinding CornerRadius}"
                             DataValidationErrors.Errors="{TemplateBinding (DataValidationErrors.Errors)}"
                             UseFloatingWatermark="{TemplateBinding UseFloatingWatermark}"
                             Watermark="{TemplateBinding Watermark}" />

                    <Button Name="PART_Button"
                            Grid.Column="1"
                            Width="20"
                            Margin="6,0"
                            Padding="0"
                            Background="Transparent"
                            BorderThickness="0"
                            Classes="CalendarDropDown"
                            ClipToBounds="False"
                            Focusable="False"
                            Foreground="{TemplateBinding Foreground}" />

                    <Popup Name="PART_Popup"
                           Grid.Column="0" Topmost="False"
                           IsLightDismissEnabled="True"
                           PlacementTarget="{TemplateBinding}">
                        
                            <Border Margin="15"
                                   
                                    Background="{DynamicResource SukiStrongBackground}"
                                    BoxShadow="{DynamicResource SukiPopupShadow}"
                                    CornerRadius="14">
                                <Border CornerRadius="14" ClipToBounds="True">
                                <Panel  Background="{DynamicResource PopupGradientBrush}">
                                <Calendar  Margin="15,8,8,12" Name="PART_Calendar"
                                           DisplayDate="{TemplateBinding DisplayDate}"
                                           DisplayDateEnd="{TemplateBinding DisplayDateEnd}"
                                           DisplayDateStart="{TemplateBinding DisplayDateStart}"
                                           FirstDayOfWeek="{TemplateBinding FirstDayOfWeek}"
                                           Foreground="{DynamicResource SukiText}"
                                           IsTodayHighlighted="{TemplateBinding IsTodayHighlighted}"
                                           SelectedDate="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=SelectedDate, Mode=TwoWay}" />
                                </Panel>
                                </Border>
                            </Border>
                        
                    </Popup>
                </Grid>
                    </suki:GlassCard>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:focus /template/ TextBox#PART_TextBox">
            <Setter Property="BorderBrush" Value="{DynamicResource ThemeBorderHighBrush}" />
        </Style>
    </ControlTheme>
</ResourceDictionary>