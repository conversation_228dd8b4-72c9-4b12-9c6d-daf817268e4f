<UserControl x:Class="SukiUI.Controls.GroupBox"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:suki="clr-namespace:SukiUI.Controls"
             d:DesignHeight="450"
             d:DesignWidth="800"
             mc:Ignorable="d">
    <UserControl.Styles>
        <Style Selector="suki|GroupBox">
            <Setter Property="Template">
                <ControlTemplate>
                    <Grid RowDefinitions="Auto, Auto, *">
                        <ContentControl Margin="0,0,0,0" Content="{TemplateBinding Header}">
                            <ContentControl.Styles>
                                <Style Selector="TextBlock">
                                    <Setter Property="Foreground" Value="{DynamicResource SukiLowText}" />
                                </Style>
                                <Style Selector="PathIcon">
                                    <Setter Property="Foreground" Value="{DynamicResource SukiLowText}" />
                                </Style>
                            </ContentControl.Styles>
                        </ContentControl>

                        <Border Grid.Row="1" Height="1"
                                Margin="0,10,0,10"
                                Background="{DynamicResource SukiControlBorderBrush}"
                                BorderThickness="0" />
                        <ContentControl Grid.Row="2" Content="{TemplateBinding Content}" />
                    </Grid>
                </ControlTemplate>
            </Setter>
        </Style>
    </UserControl.Styles>
</UserControl>
