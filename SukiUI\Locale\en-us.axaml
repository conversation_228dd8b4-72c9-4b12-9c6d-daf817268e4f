<ResourceDictionary x:Class="SukiUI.Locale.en_US"
                    xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <!--  DatePicker  -->
    <x:String x:Key="STRING_DATEPICKER_DAY_TEXT">day</x:String>
    <x:String x:Key="STRING_DATEPICKER_MONTH_TEXT">month</x:String>
    <x:String x:Key="STRING_DATEPICKER_YEAR_TEXT">year</x:String>
    <x:String x:Key="STRING_DATEPICKER_APPLY">Apply</x:String>
    <!--  TimePicker  -->
    <x:String x:Key="STRING_TIMEPICKER_HOUR_TEXT">hour</x:String>
    <x:String x:Key="STRING_TIMEPICKER_MINUTE_TEXT">minute</x:String>
    <x:String x:Key="STRING_TIMEPICKER_SECOND_TEXT">second</x:String>
    <x:String x:Key="STRING_TIMEPICKER_APPLY">Apply</x:String>
    <!--  TextBox  -->
    <x:String x:Key="STRING_MENU_CUT">Cut</x:String>
    <x:String x:Key="STRING_MENU_COPY">Copy</x:String>
    <x:String x:Key="STRING_MENU_PASTE">Paste</x:String>
</ResourceDictionary>