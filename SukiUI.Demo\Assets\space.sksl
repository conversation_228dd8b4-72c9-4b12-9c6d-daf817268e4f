const int iterations = 17;
const float formuparam = 0.53;

const int volsteps = 25;
const float stepsize = 0.1;

const float zoom  = 0.100;
const float tile  = 0.850;
const float speed =0.010 ;

const float brightness =0.0015;
const float darkmatter =0.300;
const float distfading =0.730;
const float saturation =0.850;

float mod(float x, float y) {
    return x - y * floor(x / y);
}

vec3 mod(vec3 x, vec3 y) {
    return vec3(mod(x.x,y.x), mod(x.y,y.y), mod(x.z,y.z));
}

half4 main( vec2 fragCoord )
{
    //get coords and direction
    vec2 uv=fragCoord.xy/iResolution.xy-.5;
    uv.y*=iResolution.y/iResolution.x;
    vec3 dir=vec3(uv*zoom,1.);
    float time=iTime*speed+.25;

    //mouse rotation
    float a1=.5/iResolution.x*2.;
    float a2=.8/iResolution.y*2.;
    mat2 rot1=mat2(cos(a1),sin(a1),-sin(a1),cos(a1));
    mat2 rot2=mat2(cos(a2),sin(a2),-sin(a2),cos(a2));
    dir.xz*=rot1;
    dir.xy*=rot2;
    vec3 from=vec3(1.,.5,0.5);
    from+=vec3(time*2.,time,-2.);
    from.xz*=rot1;
    from.xy*=rot2;

    //volumetric rendering
    float s=0.1,fade=1.;
    vec3 v=vec3(0.);
    for (int r=0; r<volsteps; r++) {
        vec3 p=from+s*dir*.5;
        p = abs(vec3(tile)-mod(p,vec3(tile*2.))); // tiling fold
        float pa,a=pa=0.;
    for (int i=0; i<iterations; i++) {
    p=abs(p)/dot(p,p)-formuparam; // the magic formula
    a+=abs(length(p)-pa); // absolute sum of average change
    pa=length(p);
    }
    float dm=max(0.,darkmatter-a*a*.001); //dark matter
    a*=a*a; // add contrast
    if (r>6) fade*=1.-dm; // dark matter, don't render near
    //v+=vec3(dm,dm*.5,0.);
    v+=fade;
    v+=vec3(s,s*s,s*s*s*s)*a*brightness*fade; // coloring based on distance
    fade*=distfading; // distance fading
    s+=stepsize;
    }
    v=mix(vec3(length(v)),v,saturation); //color adjust
    return vec4(v*.01,iAlpha);
}