<UserControl x:Class="SukiUI.Demo.Features.Dashboard.DialogView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dashboard="clr-namespace:SukiUI.Demo.Features.Dashboard"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="dashboard:DialogViewModel"
             mc:Ignorable="d">
    <StackPanel Width="450" Margin="5">
        <TextBlock FontSize="18"
                   FontWeight="DemiBold"
                   Text="Create Link" />

        <TextBlock Margin="0,27,0,0"
                   FontWeight="DemiBold"
                   Text="Expiry date" />
        <DatePicker Margin="0,10,0,0" />

        <DockPanel>
            <TextBlock Margin="0,20,0,0"
                       VerticalAlignment="Top"
                       DockPanel.Dock="Left"
                       FontWeight="DemiBold"
                       Text="Temporary access" />
            <ToggleSwitch Margin="0,15,-8,0"
                          VerticalAlignment="Bottom"
                          Classes="Switch"
                          DockPanel.Dock="Right"
                          IsChecked="True" />
            <Grid />
        </DockPanel>
        <TextBlock Width="320"
                   Margin="0,1,0,0"
                   HorizontalAlignment="Left"
                   FontSize="13"
                   Foreground="DarkGray"
                   Text="Members are automatically removed from campaign after closing tab or log out."
                   TextWrapping="Wrap" />

        <StackPanel Margin="0,35,0,0"
                    HorizontalAlignment="Right"
                    Orientation="Horizontal"
                    Spacing="15">
            <Button Classes="Rounded"
                    Command="{Binding CloseDialogCommand}"
                    Content="Cancel" />
            <Button Classes="Flat Rounded"
                    Command="{Binding CloseDialogCommand}"
                    Content="Generate" />
        </StackPanel>

    </StackPanel>
</UserControl>
