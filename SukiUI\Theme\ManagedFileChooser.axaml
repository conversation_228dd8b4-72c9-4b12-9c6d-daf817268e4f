﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:content="clr-namespace:SukiUI.Content"
                    xmlns:controls="clr-namespace:SukiUI.Controls"
                    xmlns:converters="clr-namespace:Avalonia.Controls.Converters;assembly=Avalonia.Controls"
                    xmlns:dialogs="clr-namespace:Avalonia.Dialogs;assembly=Avalonia.Dialogs"
                    xmlns:internal="clr-namespace:Avalonia.Dialogs.Internal;assembly=Avalonia.Dialogs"
                    xmlns:theme="clr-namespace:SukiUI.Theme">
    <Design.PreviewWith>
        <dialogs:ManagedFileChooser Width="600" Height="600" />
    </Design.PreviewWith>
    <DrawingGroup x:Key="LevelUp">
        <GeometryDrawing Brush="#00FFFFFF" Geometry="F1M16,16L0,16 0,0 16,0z" />
        <GeometryDrawing Brush="#FFF6F6F6" Geometry="F1M14.5,0L6.39,0 5.39,2 2.504,2C1.677,2,1,2.673,1,3.5L1,10.582 1,10.586 1,15.414 3,13.414 3,16 7,16 7,13.414 9,15.414 9,13 14.5,13C15.327,13,16,12.327,16,11.5L16,1.5C16,0.673,15.327,0,14.5,0" />
        <GeometryDrawing Brush="#FFDCB679" Geometry="F1M14,3L7.508,3 8.008,2 8.012,2 14,2z M14.5,1L7.008,1 6.008,3 2.504,3C2.227,3,2,3.224,2,3.5L2,9.582 4.998,6.586 9,10.586 9,12 14.5,12C14.775,12,15,11.776,15,11.5L15,1.5C15,1.224,14.775,1,14.5,1" />
        <GeometryDrawing Brush="#FF00529C" Geometry="F1M8,11L5,8 2,11 2,13 4,11 4,15 6,15 6,11 8,13z" />
        <GeometryDrawing Brush="#FFF0EFF1" Geometry="F1M8.0001,1.9996L7.5001,3.0006 14.0001,3.0006 14.0001,1.9996z" />
    </DrawingGroup>
    <DrawingGroup x:Key="Refresh">
        <GeometryDrawing Brush="#FFF6F6F6" Geometry="F1M13.5049,7.3896L13.2339,6.2646 9.9299,7.5886 10.0729,8.3896C10.0909,8.4906 10.1079,8.5926 10.1079,8.6976 10.1079,9.8596 9.1619,10.8046 7.9999,10.8046 6.8369,10.8046 5.8909,9.8596 5.8909,8.6976 5.8909,7.8966 6.3399,7.1996 6.9999,6.8426L6.9999,9.4986 12.6789,4.8156 8.0569,0.9996 6.9999,0.9996 6.9999,3.1266C4.3539,3.6006 2.3389,5.9176 2.3389,8.6976 2.3389,11.8186 4.8789,14.3586 7.9999,14.3586 11.1209,14.3586 13.6609,11.8186 13.6609,8.6976 13.6609,8.2626 13.6089,7.8226 13.5049,7.3896" />
        <GeometryDrawing Brush="#FF414141" Geometry="F1M12.5322,7.623L11.0572,8.214C11.0862,8.372 11.1072,8.533 11.1072,8.697 11.1072,10.415 9.7172,11.805 8.0002,11.805 6.2852,11.805 4.8912,10.415 4.8912,8.697 4.8912,6.983 6.2852,5.59 8.0002,5.59L8.0002,7.378 11.1072,4.815 8.0002,2.25 8.0002,4.039C5.4262,4.039 3.3392,6.123 3.3392,8.697 3.3392,11.27 5.4262,13.358 8.0002,13.358 10.5762,13.358 12.6612,11.27 12.6612,8.697 12.6612,8.327 12.6152,7.969 12.5322,7.623" />
    </DrawingGroup>
    <internal:ResourceSelectorConverter x:Key="Icons">
        <DrawingGroup x:Key="Icon_Folder">
            <GeometryDrawing Brush="#00FFFFFF" Geometry="F1M0,0L16,0 16,16 0,16z" />
            <GeometryDrawing Brush="#FFF6F6F6" Geometry="F1M1.5,1L9.61,1 10.61,3 13.496,3C14.323,3,14.996,3.673,14.996,4.5L14.996,12.5C14.996,13.327,14.323,14,13.496,14L1.5,14C0.673,14,0,13.327,0,12.5L0,2.5C0,1.673,0.673,1,1.5,1" />
            <GeometryDrawing Brush="#FFDCB67A" Geometry="F1M2,3L8.374,3 8.874,4 2,4z M13.496,4L10,4 9.992,4 8.992,2 1.5,2C1.225,2,1,2.224,1,2.5L1,12.5C1,12.776,1.225,13,1.5,13L13.496,13C13.773,13,13.996,12.776,13.996,12.5L13.996,4.5C13.996,4.224,13.773,4,13.496,4" />
            <GeometryDrawing Brush="#FFEFEFF0" Geometry="F1M2,3L8.374,3 8.874,4 2,4z" />
        </DrawingGroup>
        <DrawingGroup x:Key="Icon_File">
            <GeometryDrawing Brush="#00FFFFFF" Geometry="F1M16,16L0,16 0,0 16,0z" />
            <GeometryDrawing Brush="#FFF6F6F6" Geometry="F1M4,15C3.03,15,2,14.299,2,13L2,3C2,1.701,3.03,1,4,1L10.061,1 14,4.556 14,13C14,13.97,13.299,15,12,15z" />
            <GeometryDrawing Brush="#FF9B4E96" Geometry="F1M12,13L4,13 4,3 9,3 9,6 12,6z M9.641,2L3.964,2C3.964,2,3,2,3,3L3,13C3,14,3.964,14,3.964,14L11.965,14C12.965,14,13,13,13,13L13,5z" />
            <GeometryDrawing Brush="#FFF0EFF1" Geometry="F1M4,3L9,3 9,6 12,6 12,13 4,13z" />
        </DrawingGroup>
        <DrawingGroup x:Key="Icon_Volume">
            <GeometryDrawing Brush="#00FFFFFF" Geometry="F1M16,16L0,16 0,0 16,0z" />
            <GeometryDrawing Brush="#FFF6F6F6" Geometry="F1M0,12L0,6.5C0,5.122,1.122,4,2.5,4L13.5,4C14.879,4,16,5.122,16,6.5L16,12z" />
            <GeometryDrawing Brush="#FFEFEFF0" Geometry="F1M13,8L12,8 12,7 13,7z M11,8L10,8 10,7 11,7z M13.5,6L2.5,6C2.224,6,2,6.224,2,6.5L2,10 14,10 14,6.5C14,6.224,13.775,6,13.5,6" />
            <GeometryDrawing Brush="#FF424242" Geometry="F1M13,7L12,7 12,8 13,8z M11,7L10,7 10,8 11,8z M2,10L14,10 14,6.5C14,6.224,13.775,6,13.5,6L2.5,6C2.224,6,2,6.224,2,6.5z M15,11L1,11 1,6.5C1,5.673,1.673,5,2.5,5L13.5,5C14.327,5,15,5.673,15,6.5z" />
        </DrawingGroup>
    </internal:ResourceSelectorConverter>

    <ControlTheme x:Key="{x:Type dialogs:ManagedFileChooser}" TargetType="dialogs:ManagedFileChooser">

        <Setter Property="Background" Value="{DynamicResource ThemeBackgroundBrush}" />
        <Setter Property="Template">
            <ControlTemplate x:DataType="internal:ManagedFileChooserViewModel">
                <VisualLayerManager Name="PART_VisualLayerManager" IsHitTestVisible="True">
                    <Border Padding="{TemplateBinding Padding}"
                            x:DataType="internal:ManagedFileChooserViewModel"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Grid>
                            <Grid.Resources>
                                <theme:WindowManagedConverter x:Key="WindowManaged" />
                            </Grid.Resources>
                            <TextBlock Text="{Binding RelativeSource={RelativeSource AncestorType=Window}, Converter={StaticResource WindowManaged}}" />
                            <controls:SukiBackground AnimationEnabled="False" Style="Flat" />
                            <Button Name="PART_CloseButton"
                                    Margin="13,10"
                                    HorizontalAlignment="Right"
                                    VerticalAlignment="Top"
                                    Classes="Basic Rounded WindowControlsButton Close"
                                    Command="{Binding Cancel}">
                                <PathIcon Width="13"
                                          Height="13"
                                          Data="{x:Static content:Icons.WindowClose}" />
                            </Button>
                            <StackPanel Margin="15,12,0,0" Orientation="Horizontal">
                                <Image Width="24"
                                       Height="24"
                                       Margin="2"
                                       VerticalAlignment="Top"
                                       Source="/Content/Images/icons8-file-explorer-new-48.png" />
                                <TextBlock Margin="12,0,0,0"
                                           FontSize="25"
                                           FontWeight="{DynamicResource DefaultDemiBold}"
                                           Text="{Binding Title}">
                                    <TextBlock.Foreground>
                                        <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
                                            <GradientStop Offset="0" Color="{DynamicResource SukiText}" />
                                            <GradientStop Offset="1" Color="{DynamicResource SukiLowText}" />
                                        </LinearGradientBrush>
                                    </TextBlock.Foreground>
                                </TextBlock>

                            </StackPanel>
                            <DockPanel Margin="12,40,12,12">

                                <DockPanel Margin="0,15,0,0" DockPanel.Dock="Bottom">
                                    <StackPanel Margin="30,0,0,0"
                                                HorizontalAlignment="Right"
                                                DockPanel.Dock="Right"
                                                Orientation="Horizontal"
                                                Spacing="10">
                                        <StackPanel.Styles>
                                            <Style Selector="Button">
                                                <Setter Property="Margin" Value="4" />
                                            </Style>
                                        </StackPanel.Styles>

                                        <Button MinWidth="120"
                                                Command="{Binding Cancel}"
                                                Content="Cancel" />
                                        <Button MinWidth="120"
                                                Classes="Flat"
                                                Command="{Binding Ok}"
                                                Content="Choose" />
                                    </StackPanel>
                                    <ComboBox MaxWidth="200"
                                              Margin="0,0,0,0"
                                              DockPanel.Dock="Left"
                                              IsVisible="{Binding ShowFilters}"
                                              ItemsSource="{Binding Filters}"
                                              SelectedItem="{Binding SelectedFilter}" />



                                    <!-- <CheckBox IsChecked="{Binding ShowHiddenFiles}">
                  <TextBlock Text="{DynamicResource StringManagedFileChooserShowHiddenFilesText}" />
                </CheckBox>-->
                                    <TextBox IsVisible="{Binding !SelectingFolder}"
                                             Text="{Binding FileName}"
                                             Watermark="{DynamicResource StringManagedFileChooserFileNameWatermark}" />

                                </DockPanel>




                                <ListBox x:Name="PART_QuickLinks"
                                         MaxWidth="200"
                                         Margin="0,30,5,5"
                                         BorderBrush="Transparent"
                                         Classes="Void"
                                         DockPanel.Dock="Left"
                                         Focusable="False"
                                         ItemsSource="{Binding QuickLinks}"
                                         SelectedIndex="{Binding QuickLinksSelectedIndex}">
                                    <ListBox.ItemTemplate>
                                        <DataTemplate>
                                            <StackPanel Margin="2"
                                                        Background="Transparent"
                                                        Orientation="Horizontal"
                                                        Spacing="6">
                                                <Image Width="18" Height="18">
                                                    <DrawingImage Drawing="{Binding IconKey, Converter={StaticResource Icons}}" />
                                                </Image>
                                                <TextBlock Margin="7,0,0,0"
                                                           FontSize="17"
                                                           FontWeight="{DynamicResource DefaultDemiBold}"
                                                           Text="{Binding DisplayName}" />
                                            </StackPanel>
                                        </DataTemplate>
                                    </ListBox.ItemTemplate>
                                </ListBox>
                                <controls:GlassCard Margin="0,10,-50,0">

                                    <DockPanel Margin="0,0,50,0" Grid.IsSharedSizeScope="True">
                                        <DockPanel.Resources>
                                            <theme:TextToPathConverter x:Key="TextToPathConverter" />
                                        </DockPanel.Resources>
                                        <Button Margin="0,2"
                                                Padding="8,1"
                                                HorizontalContentAlignment="Left"
                                                Classes="NoPressedAnimation"
                                                Command="{Binding GoUp}"
                                                DockPanel.Dock="Top">
                                            <ContentControl Content="{Binding Location, Converter={StaticResource TextToPathConverter}}" />
                                        </Button>

                                        <Grid Margin="15,15,0,0"
                                              HorizontalAlignment="Stretch"
                                              DockPanel.Dock="Top">
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition Width="20" SharedSizeGroup="Icon" />
                                                <ColumnDefinition Width="400" SharedSizeGroup="Name" />
                                                <ColumnDefinition Width="16" SharedSizeGroup="Splitter" />
                                                <ColumnDefinition Width="200" SharedSizeGroup="Modified" />
                                                <ColumnDefinition Width="16" SharedSizeGroup="Splitter" />
                                                <ColumnDefinition Width="150" SharedSizeGroup="Type" />
                                                <ColumnDefinition Width="16" SharedSizeGroup="Splitter" />
                                                <ColumnDefinition Width="200" SharedSizeGroup="Size" />
                                                <ColumnDefinition Width="16" SharedSizeGroup="Splitter" />
                                            </Grid.ColumnDefinitions>
                                            <TextBlock Grid.Column="1"
                                                       Foreground="{DynamicResource SukiLowText}"
                                                       Text="Name" />
                                            <GridSplitter Grid.Column="2"
                                                          Background="Transparent"
                                                          ResizeDirection="Columns" />
                                            <Rectangle Grid.Column="2"
                                                       Width="1"
                                                       HorizontalAlignment="Left"
                                                       VerticalAlignment="Stretch"
                                                       Fill="{DynamicResource SukiControlBorderBrush}" />
                                            <TextBlock Grid.Column="3"
                                                       Foreground="{DynamicResource SukiLowText}"
                                                       Text="Date Modified" />
                                            <GridSplitter Grid.Column="4"
                                                          Background="Transparent"
                                                          ResizeDirection="Columns" />
                                            <Rectangle Grid.Column="4"
                                                       Width="1"
                                                       HorizontalAlignment="Left"
                                                       VerticalAlignment="Stretch"
                                                       Fill="{DynamicResource SukiControlBorderBrush}" />

                                            <TextBlock Grid.Column="5"
                                                       Foreground="{DynamicResource SukiLowText}"
                                                       Text="Type" />
                                            <GridSplitter Grid.Column="6"
                                                          Background="Transparent"
                                                          ResizeDirection="Columns" />
                                            <Rectangle Grid.Column="6"
                                                       Width="1"
                                                       HorizontalAlignment="Left"
                                                       VerticalAlignment="Stretch"
                                                       Fill="{DynamicResource SukiControlBorderBrush}" />

                                            <TextBlock Grid.Column="7"
                                                       Foreground="{DynamicResource SukiLowText}"
                                                       Text="Size" />
                                            <GridSplitter Grid.Column="8"
                                                          Background="Transparent"
                                                          ResizeDirection="Columns" />
                                            <Rectangle Grid.Column="8"
                                                       Width="1"
                                                       HorizontalAlignment="Left"
                                                       VerticalAlignment="Stretch"
                                                       Fill="{DynamicResource SukiControlBorderBrush}" />
                                        </Grid>


                                        <DockPanel Margin="0,0,0,5">



                                            <ListBox x:Name="PART_Files"
                                                     Margin="0,5"
                                                     Classes="Stack"
                                                     ItemsSource="{Binding Items}"
                                                     ScrollViewer.HorizontalScrollBarVisibility="Disabled"
                                                     SelectedItems="{Binding SelectedItems}"
                                                     SelectionMode="{Binding SelectionMode}">
                                                <ListBox.ItemTemplate>
                                                    <DataTemplate>
                                                        <Grid Background="Transparent">
                                                            <Grid.ColumnDefinitions>
                                                                <ColumnDefinition SharedSizeGroup="Icon" />
                                                                <ColumnDefinition SharedSizeGroup="Name" />
                                                                <ColumnDefinition SharedSizeGroup="Splitter" />
                                                                <ColumnDefinition SharedSizeGroup="Modified" />
                                                                <ColumnDefinition SharedSizeGroup="Splitter" />
                                                                <ColumnDefinition SharedSizeGroup="Type" />
                                                                <ColumnDefinition SharedSizeGroup="Splitter" />
                                                                <ColumnDefinition SharedSizeGroup="Size" />
                                                                <ColumnDefinition SharedSizeGroup="Splitter" />
                                                            </Grid.ColumnDefinitions>
                                                            <Image Grid.Column="0"
                                                                   Width="16"
                                                                   Height="16">
                                                                <DrawingImage Drawing="{Binding IconKey, Converter={StaticResource Icons}}" />
                                                            </Image>
                                                            <TextBlock Grid.Column="1" Text="{Binding DisplayName}" />
                                                            <TextBlock Grid.Column="3" Text="{Binding Modified}" />
                                                            <TextBlock Grid.Column="5" Text="{Binding Type}" />
                                                            <TextBlock Grid.Column="7" HorizontalAlignment="Right">
                                                                <TextBlock.Text>
                                                                    <Binding Path="Size">
                                                                        <Binding.Converter>
                                                                            <internal:FileSizeStringConverter />
                                                                        </Binding.Converter>
                                                                    </Binding>
                                                                </TextBlock.Text>
                                                            </TextBlock>
                                                        </Grid>
                                                    </DataTemplate>
                                                </ListBox.ItemTemplate>
                                            </ListBox>
                                        </DockPanel>

                                    </DockPanel>
                                </controls:GlassCard>
                            </DockPanel>
                        </Grid>
                    </Border>
                </VisualLayerManager>
            </ControlTemplate>
        </Setter>
    </ControlTheme>


</ResourceDictionary>
