﻿<UserControl x:Class="SukiUI.Controls.BusyArea"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:suki="clr-namespace:SukiUI.Controls"
             xmlns:converters="https://github.com/kikipoulet/SukiUI"
             d:DesignHeight="450"
             d:DesignWidth="800"
             mc:Ignorable="d">
    <UserControl.Styles>
        
        
        <Style Selector="suki|BusyArea DockPanel#LoadingBusyArea">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Opacity" Duration="0:0:0.3" />
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="suki|BusyArea[IsBusy=True]  /template/ DockPanel#LoadingBusyArea">
            <Setter Property="Opacity" Value="1" />
        </Style>

        <Style Selector="suki|BusyArea[IsBusy=False] /template/ DockPanel#LoadingBusyArea">
            <Setter Property="Opacity" Value="0" />
        </Style>

        <Style Selector="suki|BusyArea ContentControl">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Property="Opacity" Duration="0:0:0.3" />
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="suki|BusyArea[IsBusy=True]  /template/ ContentControl#BusyContent">
            <Setter Property="Opacity" Value="0.1" />
        </Style>

        <Style Selector="suki|BusyArea[IsBusy=False]  /template/ ContentControl#BusyContent">
            <Setter Property="Opacity" Value="1" />
        </Style>

        
        <Style Selector="suki|BusyArea">
            <Setter Property="Template">
                <ControlTemplate>
                    <Panel>
                        <ContentControl Name="BusyContent" Content="{TemplateBinding Content}" />
                        <DockPanel Name="LoadingBusyArea"
                                   HorizontalAlignment="Center"
                                   VerticalAlignment="Center"
                                   LastChildFill="True">
                            <TextBlock Margin="3"
                                       DockPanel.Dock="Bottom"
                                       FontWeight="{DynamicResource DefaultDemiBold}"
                                       IsVisible="{TemplateBinding BusyText,
                                                                   Converter={x:Static StringConverters.IsNotNullOrEmpty}}"
                                       Text="{TemplateBinding BusyText}" />
                            
                            <ContentPresenter HorizontalAlignment="Center"
                                              VerticalAlignment="Center"
                                              IsHitTestVisible="False" Content="{TemplateBinding IsBusy, Converter={x:Static converters:ProgressToContentConverter.Instance}}"></ContentPresenter>

                            
                        </DockPanel>
                    </Panel>
                </ControlTemplate>
            </Setter>
        </Style>

    </UserControl.Styles>
</UserControl>