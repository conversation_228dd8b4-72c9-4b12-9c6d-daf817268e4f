<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:content="clr-namespace:SukiUI.Content"
                    xmlns:suki="clr-namespace:SukiUI.Controls"
                    xmlns:theme="clr-namespace:SukiUI.Theme">
    <Design.PreviewWith>
        <suki:SukiSideMenu></suki:SukiSideMenu>
    </Design.PreviewWith>
    <ControlTheme x:Key="SukiSideMenuStyle" TargetType="suki:SukiSideMenu">
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="Template">
            <ControlTemplate>
                <SplitView DisplayMode="CompactInline"
                           IsPaneOpen="{TemplateBinding IsMenuExpanded}"
                           OpenPaneLength="{TemplateBinding OpenPaneLength}">
                    <SplitView.Pane>
                        <Border Margin="{Binding ShowTitlebarBackground, Converter={x:Static suki:WindowBackgroundToMarginConverter.Instance}, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type suki:SukiWindow}}}"
                        >
                            <Grid Background="Transparent">
                                <Grid.Styles>
                                    <Style Selector="suki|GlassCard">
                                        <!-- in case someone use sukisidemenu without sukiwindow -->
                                        <Setter Property="CornerRadius" Value="0"></Setter>
                                    </Style>
                                </Grid.Styles>
                                <suki:GlassCard Name="Glass"
                                                BorderThickness="0,0,1,0"
                                                CornerRadius="{Binding ShowTitlebarBackground, Converter={x:Static suki:WindowBackgroundToCornerRadiusConverter.Instance}, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type suki:SukiWindow}}}"
                                                IsAnimated="False" />
                                <DockPanel>
                                    <Button Name="PART_SidebarToggleButton"
                                            Margin="7"
                                            HorizontalAlignment="{TemplateBinding TogglePaneButtonPosition}"
                                            VerticalAlignment="Top"
                                            Classes="Basic"
                                            Cursor="Hand"
                                            DockPanel.Dock="Top"
											IsVisible="{TemplateBinding SidebarToggleEnabled}">
                                        <Panel>
                                            <PathIcon Name="PART_ExpandIcon"
                                                      Classes="Flippable"
                                                        Width="12"
                                                        Height="12"
                                                        Data="{x:Static content:Icons.ChevronLeft}"
                                                        Foreground="{DynamicResource SukiText}">
                                                <PathIcon.Transitions>
                                                    <Transitions>
                                                        <TransformOperationsTransition Property="RenderTransform" Duration="{StaticResource MediumAnimationDuration}" />
                                                    </Transitions>
                                                </PathIcon.Transitions>
                                            </PathIcon>
                                        </Panel>
                                    </Button>
									<Grid MinHeight="{TemplateBinding HeaderMinHeight}"
                                          DockPanel.Dock="Top"
                                          IsVisible="{TemplateBinding IsMenuExpanded}">
                                        <ContentPresenter Content="{TemplateBinding HeaderContent}" />
                                    </Grid>
                                    <ContentControl Margin="0,0,0,12"
                                                    Content="{TemplateBinding FooterContent}"
                                                    DockPanel.Dock="Bottom"
                                                    IsVisible="{TemplateBinding IsMenuExpanded}" />
                                    <Grid Name="PART_Spacer" 
                                          Height="15"
                                          DockPanel.Dock="Top" />
                                    <DockPanel>
                                        <StackPanel IsVisible="{TemplateBinding IsSearchEnabled}" DockPanel.Dock="Top">
                                            <StackPanel.Styles>
                                                <Style Selector="suki|GlassCard.Search:pointerover, suki|GlassCard.Search:focus-within">
                                                    <Setter Property="Width" Value="190"></Setter>
                                                    <Setter Property="CornerRadius" Value="12" />
                                                </Style>
                                            </StackPanel.Styles>
                                            <suki:GlassCard IsVisible="{TemplateBinding IsMenuExpanded}" Classes="Search" HorizontalAlignment="Right" IsAnimated="False" Width="42" Margin="15,10,15,15" CornerRadius="22" Height="42">
                                                <suki:GlassCard.Transitions>
                                                    <Transitions>
                                                        <CornerRadiusTransition Property="CornerRadius" Duration="0:0:0.35"></CornerRadiusTransition>
                                                        <DoubleTransition Property="Width" Duration="0:0:0.35"></DoubleTransition>
                                                    </Transitions>
                                                </suki:GlassCard.Transitions>
                                                <DockPanel Margin="-3,-1,0,0">
                                                <PathIcon Width="13" DockPanel.Dock="Left" VerticalAlignment="Center" Foreground="{DynamicResource SukiLowText}"
                                                          Height="13"
                                                          Data="{x:Static content:Icons.Search}" />
                                                <TextBox theme:TextBoxExtensions.AddDeleteButton="True" Classes="NoShadow" Text="{TemplateBinding SearchText, Mode=TwoWay}" BorderThickness="0" Watermark="Search"/>
                                            </DockPanel>
                                        </suki:GlassCard>
                                            
                                          <!--  <Border Height="1" Margin="20,0,20,15" Background="{DynamicResource SukiBorderBrush}"/> -->
                                            </StackPanel>
                                        <ScrollViewer Name="PART_ScrollViewer"
                                                      AllowAutoHide="{TemplateBinding (ScrollViewer.AllowAutoHide)}"
                                                      Background="{TemplateBinding Background}"
                                                      BringIntoViewOnFocusChange="{TemplateBinding (ScrollViewer.BringIntoViewOnFocusChange)}"
                                                      Classes="Stack"
                                                      HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                                      HorizontalSnapPointsType="{TemplateBinding (ScrollViewer.HorizontalSnapPointsType)}"
                                                      IsScrollChainingEnabled="{TemplateBinding (ScrollViewer.IsScrollChainingEnabled)}"
                                                      VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}"
                                                      VerticalSnapPointsType="{TemplateBinding (ScrollViewer.VerticalSnapPointsType)}">
                                        <StackPanel theme:StackPanelExtensions.AnimatedScroll="True">
                                            <ItemsPresenter Name="PART_ItemsPresenter"
                                                            Margin="{TemplateBinding Padding}"
                                                            theme:ItemsPresenterExtensions.AnimatedScroll="True"
                                                            ItemsPanel="{TemplateBinding ItemsPanel}" />
                                        </StackPanel>
                                    </ScrollViewer>
                                        </DockPanel>
                                </DockPanel>
                            </Grid>
                        </Border>
                    </SplitView.Pane>
                    <Border Name="ContentBorder"
                            Margin="0,0,0,0"
                            Background="{DynamicResource SukiBackground}"
                            BorderBrush="{DynamicResource SukiBorderBrush}"
                            BorderThickness="0,0,0,0">
                        <suki:SukiTransitioningContentControl Name="PART_TransitioningContentControl" />
                    </Border>
                </SplitView>
            </ControlTemplate>
        </Setter>
        <Style Selector="^[IsToggleButtonVisible=False]">
            <Style Selector="^[IsMenuExpanded=False]">
                <Style Selector="^ /template/ Button#PART_SidebarToggleButton">
                    <Setter Property="Opacity" Value="0" />
                    <Setter Property="IsVisible" Value="False" />
                </Style>
            </Style>
            <Style Selector="^[IsMenuExpanded=True]">
                <Style Selector="^ /template/ Button#PART_SidebarToggleButton">
                    <Setter Property="Opacity" Value="0" />
                    <Setter Property="IsHitTestVisible" Value="False" />
                </Style>
            </Style>
        </Style>
        <Style Selector="^[IsMenuExpanded=False]">
            <Style Selector="^ /template/ PathIcon#PART_ExpandIcon">
                <Setter Property="RenderTransform" Value="rotate(-180deg)" />
            </Style>
            <Style Selector="^ /template/ Grid#PART_Spacer">
                <Setter Property="Height" Value="0" />
            </Style>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type suki:SukiSideMenu}"
                  BasedOn="{StaticResource SukiSideMenuStyle}"
                  TargetType="suki:SukiSideMenu" />
</ResourceDictionary>