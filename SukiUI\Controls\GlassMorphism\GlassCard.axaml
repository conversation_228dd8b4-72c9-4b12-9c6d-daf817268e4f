﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:suki="clr-namespace:SukiUI.Controls">
    <ControlTheme x:Key="SukiGlassCardStyle" TargetType="suki:GlassCard">
        <Setter Property="BorderBrush" Value="{DynamicResource GlassBorderBrush}" />
        <Setter Property="Background" Value="{DynamicResource SukiGlassCardBackground}" />
        <Setter Property="Padding" Value="20" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel Name="RootPanel" Opacity="0">
                    <Border Name="PART_BorderCard" RenderTransformOrigin="50%,50%"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            ClipToBounds="{TemplateBinding ClipToBounds}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            Opacity="{DynamicResource GlassOpacity}">
                        <Border.Transitions>
                            <Transitions>
                                <DoubleTransition Property="Width"
                                                  Duration="0:0:0.5"
                                                  Easing="QuadraticEaseOut" />
                                <DoubleTransition Property="Height"
                                                  Duration="0:0:0.5"
                                                  Easing="QuadraticEaseOut" />
                            </Transitions>
                        </Border.Transitions>
                        <Border.Transitions>
                            <Transitions>
                                <!-- <BrushTransition Property="Background" Duration="0:0:0.15" /> -->
                                <BrushTransition Property="BorderBrush" Duration="0:0:0.15" />
                                <DoubleTransition Property="Opacity" Duration="0:0:0.15" />
                            </Transitions>
                        </Border.Transitions>
                    </Border>
                    <Border Name="PART_ClipBorder"
                            Background="{DynamicResource SukiPrimaryColor0}"
                            BorderBrush="{DynamicResource SukiPrimaryColor0}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            ClipToBounds="{TemplateBinding ClipToBounds}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Border.Transitions>
                            <Transitions>
                                <BrushTransition Property="BorderBrush" Duration="0:0:0.15" />
                            </Transitions>
                        </Border.Transitions>
                        <ContentPresenter Name="PART_CP" Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding Content}" />
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>
        <Style Selector="^.Control">
            <Setter Property="IsAnimated" Value="False"></Setter>
            <Setter Property="Padding" Value="0"></Setter>
        </Style>
        <Style Selector="^.Discrete">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
            <Setter Property="Padding" Value="0"></Setter>
        </Style>
        <Style Selector="^.Control /template/ Border#PART_BorderCard">
            <Setter Property="Background" Value="{DynamicResource ControlSukiGlassCardBackground}" />
            <Setter Property="Opacity" Value="{DynamicResource ControlGlassOpacity}" />
        </Style>
        <Style Selector="^.Discrete /template/ Border#PART_BorderCard">
            <Setter Property="Background" Value="{DynamicResource SukiGlassCardBackground}" />
            <Setter Property="Opacity" Value="{DynamicResource DiscreteControlGlassOpacity}" />
        </Style>
        <Style Selector="^.Primary /template/ Border#PART_ClipBorder">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiAccentColor0}" />
        </Style>
        <Style Selector="^[IsOpaque=True] /template/ Border#PART_BorderCard">
            <Setter Property="Opacity" Value="1" />
            <Setter Property="Background" Value="{DynamicResource SukiGlassCardOpaqueBackground}" />
        </Style>
        <Style Selector="^[IsInteractive=True]:pointerover /template/ Border#PART_ClipBorder">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor50}" />
        </Style>
        <Style Selector="^.Primary[IsInteractive=True]:pointerover /template/ Border#PART_ClipBorder">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiAccentColor50}" />
        </Style>
        <Style Selector="^[IsInteractive=True]:pointerdown /template/ Border#PART_ClipBorder">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />
        </Style>
        <Style Selector="^.Primary[IsInteractive=True]:pointerdown /template/ Border#PART_ClipBorder">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiAccentColor}" />
        </Style>
        <Style Selector="^.Accent /template/ Border#PART_BorderCard">
            <Setter Property="Background" Value="{DynamicResource SukiAccentColor25}" />
        </Style>
        <Style Selector="^.Primary /template/ Border#PART_BorderCard">
            <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor25}" />
        </Style>
        <Style Selector="^.PrimaryOpaque /template/ Border#PART_BorderCard">
            <Setter Property="Opacity" Value="1" />
            <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor}" />
        </Style>
        <Style Selector="^[IsVisible=True] /template/ Panel#RootPanel">
            <Setter Property="Opacity" Value="1" />
        </Style>
        <Style Selector="^[IsVisible=False] /template/ Panel#RootPanel">
            <Setter Property="Opacity" Value="0" />
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type suki:GlassCard}"
                  BasedOn="{StaticResource SukiGlassCardStyle}"
                  TargetType="suki:GlassCard" />
</ResourceDictionary>