﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SukiUI.Demo.Bll
{
    /// <summary>
    /// 变色区域
    /// </summary>
    public  class JudgeArea
    {
        /// <summary>
        /// 名称
        /// </summary>
        public string mc { get; set; }
        /// <summary>
        /// 项目编号
        /// </summary>
        public string xmbh { get; set; }
        /// <summary>
        /// 评判区域编号
        /// </summary>
        public string ppqybh { get; set; }
        /// <summary>
        ///扣分项
        /// </summary>
        public string kfx { get; set; }
        /// <summary>
        /// 是否变色
        /// </summary>
        public bool sfbs  { get; set; }
        /// <summary>
        /// 考试项目序号，从0开始一共18项。主要与配置文件中的考试项目对应，方便后期将扣分的项目变色
        /// </summary>
        public bool xmxh  { get; set; }
    }
}
