﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:suki="https://github.com/kikipoulet/SukiUI"
        xmlns:theme="clr-namespace:SukiUI.Theme">
    <Design.PreviewWith>
        <Border Padding="20">
            <StackPanel Orientation="Horizontal" Spacing="5">
                <NumericUpDown Value="4" />
                <TextBox Classes="NoShadow" Text="test" />
            </StackPanel>
        </Border>
    </Design.PreviewWith>
    <Style Selector="NumericUpDown">
        <Setter Property="BorderThickness" Value="0,0,0,0" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
        <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{DynamicResource SukiBackground}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border   Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="{TemplateBinding CornerRadius}">
                <suki:GlassCard Padding="0"
                                VerticalAlignment="Center"
                                Classes="Discrete"
                                CornerRadius="{TemplateBinding CornerRadius}">
                    <ButtonSpinner Name="PART_Spinner"
                                   Height="26"
                                   Margin="5,4"
                                   HorizontalContentAlignment="Stretch"
                                   VerticalContentAlignment="Stretch"
                                   AllowSpin="{TemplateBinding AllowSpin}"
                                   BorderThickness="0"
                                   ButtonSpinnerLocation="{TemplateBinding ButtonSpinnerLocation}"
                                   CornerRadius="{TemplateBinding CornerRadius}"
                                   ShowButtonSpinner="{TemplateBinding ShowButtonSpinner}">
                        <Grid ColumnDefinitions="*,Auto">
                            <TextBox Name="PART_TextBox"
                                     MinWidth="20"
                                     Margin="-5,-5,-5,-6"
                                     Padding="{TemplateBinding Padding}"
                                     HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                     VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                     theme:TextBoxExtensions.AddDeleteButton="False"
                                     AcceptsReturn="False"
                                     Background="Transparent"
                                     BorderThickness="0"
                                     Classes="NoShadow"
                                     CornerRadius="0"
                                     DataValidationErrors.Errors="{TemplateBinding (DataValidationErrors.Errors)}"
                                     IsReadOnly="{TemplateBinding IsReadOnly}"
                                     Text="{TemplateBinding Text}"
                                     TextWrapping="NoWrap"
                                     Watermark="{TemplateBinding Watermark}" />
                            <TextBlock Grid.Column="1"
                                       Margin="0,1,9,0"
                                       VerticalAlignment="Center"
                                       FontSize="13"
                                       Foreground="{DynamicResource SukiLowText}"
                                       Text="{TemplateBinding theme:NumericUpDownExtensions.Unit}" />
                        </Grid>

                    </ButtonSpinner>
                </suki:GlassCard>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>
    <Style Selector="NumericUpDown /template/ ButtonSpinner /template/ Border">
        <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
    </Style>
    <Style Selector="NumericUpDown /template/ Border">
        <Setter Property="BorderThickness" Value="0" />

    </Style>
    <Style Selector="NumericUpDown:pointerover /template/ TextBox">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
    </Style>
    <Style Selector="NumericUpDown:pressed /template/ TextBox">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
    </Style>
    <Style Selector="NumericUpDown:disabled">
        <Setter Property="Foreground" Value="{DynamicResource SukiDisabledText}" />
    </Style>

    <Style Selector="RepeatButton:pointerover">
        <Setter Property="BorderBrush" Value="{DynamicResource SukiBorderBrush}" />
    </Style>
    <Style Selector="ButtonSpinner">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="{DynamicResource ThemeBorderThickness}" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Focusable" Value="True" />
        <Setter Property="Template">
            <ControlTemplate>
                <DataValidationErrors>
                    <Border Name="border"
                            Margin="{TemplateBinding Padding}"
                            HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                            VerticalAlignment="{TemplateBinding VerticalAlignment}"
                            Background="{TemplateBinding Background}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <DockPanel>
                            <UniformGrid Name="PART_SpinnerPanel"
                                         DockPanel.Dock="Right"
                                         IsVisible="{TemplateBinding ShowButtonSpinner}"
                                         Rows="2">
                                <RepeatButton Name="PART_IncreaseButton"
                                              BorderThickness="0"
                                              Theme="{StaticResource SimpleButtonSpinnerRepeatButton}">
                                    <Path Width="8"
                                          Height="4"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Data="M0,5 L4.5,.5 9,5 6,5 4.5,3.5 3,5 z"
                                          Fill="{DynamicResource SukiText}"
                                          Stretch="Uniform" />
                                </RepeatButton>
                                <RepeatButton Name="PART_DecreaseButton"
                                              BorderThickness="0"
                                              Theme="{StaticResource SimpleButtonSpinnerRepeatButton}">
                                    <Path Width="8"
                                          Height="4"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Data="M0,0 L3,0 4.5,1.5 6,0 9,0 4.5,4.5 z"
                                          Fill="{DynamicResource SukiText}"
                                          Stretch="Uniform" />
                                </RepeatButton>
                            </UniformGrid>
                            <ContentPresenter Name="PART_ContentPresenter"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}" />
                        </DockPanel>
                    </Border>
                </DataValidationErrors>
            </ControlTemplate>
        </Setter>
        <Style Selector="^:left">
            <Style Selector="^ /template/ StackPanel#PART_SpinnerPanel">
                <Setter Property="DockPanel.Dock" Value="Left" />
            </Style>
        </Style>

        <Style Selector="^:pointerover /template/ Border#border">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiBorderBrush}" />
        </Style>
        <Style Selector="^:focus /template/ Border#border">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiBorderBrush}" />
        </Style>
        <Style Selector="^:error /template/ Border#border">
            <Setter Property="BorderBrush" Value="{DynamicResource SukiBorderBrush}" />
        </Style>
    </Style>

</Styles>