using Avalonia;
using Avalonia.Controls;
using Avalonia.Markup.Xaml;
using SukiUI.Demo.Bll;
using System.Net;
using System.Text;

namespace SukiUI.Demo.Features.CarControl;

public partial class CarListView : UserControl
{
    
   
    public CarListView()
    {
        InitializeComponent();
        Loaded += (s, e) => {
            if (DataContext is CarListViewModel vm)
            {
                vm?.DoInitLoadData();
            }
        };
        
    }

    //private void InitializeComponent()
    //{
    //    AvaloniaXamlLoader.Load(this);
    //}
    
}