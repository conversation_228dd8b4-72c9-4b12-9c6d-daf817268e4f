using System;
using System.Threading.Tasks;
using Avalonia;
using Avalonia.Controls;
using Avalonia.Controls.ApplicationLifetimes;
using Avalonia.Controls.Templates;
using Avalonia.Markup.Xaml;
using Avalonia.Markup.Xaml.Styling;
using Avalonia.Media;
using Avalonia.Styling;
using Microsoft.Extensions.DependencyInjection;
using SukiUI.Demo.Common;
using SukiUI.Demo.Features.ControlsLibrary;
using SukiUI.Demo.Features.ControlsLibrary.Colors;
using SukiUI.Demo.Features.ControlsLibrary.Dialogs;
using SukiUI.Demo.Features.ControlsLibrary.DockControls;
using SukiUI.Demo.Features.ControlsLibrary.StackPage;
using SukiUI.Demo.Features.ControlsLibrary.TabControl;
using SukiUI.Demo.Features.ControlsLibrary.Toasts;
using SukiUI.Demo.Features.CustomTheme;
using SukiUI.Demo.Features.Dashboard;
using SukiUI.Demo.Features.Effects;
using SukiUI.Demo.Features.Playground;
using SukiUI.Demo.Features.Splash;
using SukiUI.Demo.Features.Theming;
using SukiUI.Demo.Services;
using SukiUI.Dialogs;
using SukiUI.Models;
using SukiUI.Theme.Shadcn;
using SukiUI.Toasts;
using SukiUI.Demo.Features.Login;
using SukiUI.Demo.Features.CarControl;
using Serilog;
using SukiUI.Demo.Bll;
using CommunityToolkit.Mvvm.Messaging;
using Newtonsoft.Json;
using Microsoft.Extensions.Configuration;
using System.Text;
using SukiUI.Demo.Infrastructure;


namespace SukiUI.Demo;

public class App : Application
{
    public static IServiceProvider provider { get; private set; }
    public static IConfiguration Configuration { get; private set; }
    public override void Initialize()
    {
        // 首先初始化崩溃日志记录器
        try
        {
            CrashLogger.Initialize();
            CrashLogger.CleanupOldLogs(); // 清理旧日志
        }
        catch (Exception ex)
        {
            // 如果崩溃日志初始化失败，记录到控制台
            Console.WriteLine($"Failed to initialize CrashLogger: {ex.Message}");
        }

        // 初始化Linux信号处理器
        try
        {
            SignalHandler.Initialize();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to initialize SignalHandler: {ex.Message}");
        }

        // 启动进程监控
        try
        {
            ProcessMonitor.StartMonitoring();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Failed to start ProcessMonitor: {ex.Message}");
        }

        // 设置全局异常处理器
        SetupGlobalExceptionHandlers();

        AvaloniaXamlLoader.Load(this);
    }

    public override void OnFrameworkInitializationCompleted()
    {
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            // 构建配置
            BuildConfiguration();

            // 初始化原生库管理器
            try
            {
                SukiUI.Demo.Infrastructure.NativeLibraryManager.Initialize();
                Log.Information("原生库管理器初始化成功");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "原生库管理器初始化失败");
            }

            // 初始化SDK资源管理器
            try
            {
                var sdkManager = SukiUI.Demo.Infrastructure.SDKResourceManager.Instance;
                Log.Information("SDK资源管理器已准备就绪");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "SDK资源管理器初始化失败");
            }

            var services = new ServiceCollection();

            services.AddSingleton(desktop);

            var views = ConfigureViews(services);
            provider = ConfigureServices(services);

            DataTemplates.Add(new ViewLocator(views));

            #region 获取全局配置
            Log.Information("全局配置");
            
            GetConfig();
            #endregion 
            #region 直接加载主窗体
            var mainWindow = views.CreateView<SukiUIDemoViewModel>(provider) as Window;

            desktop.MainWindow = mainWindow;
            #endregion
            //#region 登录窗口
            //var loginWindow = new LoginView();
            //desktop.MainWindow = loginWindow;


            //var loginViewModel = new LoginViewModel( );

            //loginWindow.DataContext = loginViewModel;//这里直接给Window设置DataContext，不就应LoginWindow中的loginView设置model吗？
            //loginViewModel.LoginSuccessful += () =>
            //{
            //    // Switch to the main window after successful login
            //    var mainWindow = views.CreateView<SukiUIDemoViewModel>(provider) as Window;
            //    desktop.MainWindow = mainWindow;
            //    mainWindow?.Show();
            //    loginWindow.Close();
            //};

            //loginViewModel.LoginCancelled += () =>
            //{
            //    //loginWindow.Close();
            //    if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
            //    {
            //        desktop.Shutdown();
            //    }
            //};
            //#endregion
        }

        base.OnFrameworkInitializationCompleted();

        //项目结束时清关闭日志
        AppDomain.CurrentDomain.ProcessExit += (sender, e) => {
            OnProcessExit(sender, e);
        };

        // 如果是桌面应用，也注册应用程序退出事件
        if (ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktopApp)
        {
            desktopApp.Exit += OnApplicationExit;
        }

        //默认样式
        Shadcn.Configure(Application.Current, ThemeVariant.Dark);
    }
    private void BuildConfiguration()
    {
        var str = System.AppDomain.CurrentDomain.BaseDirectory;
        // 创建配置构建器
        var builder = new ConfigurationBuilder()
            .SetBasePath(System.AppDomain.CurrentDomain.BaseDirectory)
            //.AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);
            .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true);

        // 构建配置
        Configuration = builder.Build();
        AppStaticData.config = Configuration;
    }
    private static void GetConfig()
    {
        GetSiheyiConfig();
        GetSensorConfig();
        GetIpsConfig();
    }
    public static void GetIpsConfig()
    {
        #region 读取IPS（场地摄像头IP）配置参数
        if (AppStaticData.siheyiconfig.SfIps.Value != 1)
        {
            return;
        }
        var sxtList=new List<sxtIP>();
        var filepath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory,"map", "ips.txt");
        var sr = new StreamReader(filepath, Encoding.Default);
        string line;
        while ((line = sr.ReadLine()) != null)
        {
            try
            {
                if (string.IsNullOrEmpty(line) || line.StartsWith(";") || line.StartsWith("；")) continue;
                var lines = line.Split(',');
                var sxt = new sxtIP();
                sxt.SxtZB = new DoublePoint(double.Parse(lines[0]), double.Parse(lines[1]));
                sxt.SxtTDH = int.Parse(lines[2]);
                sxt.SxtUser = lines[3];
                sxt.SxtPassWord = lines[4];
                sxt.SxtJL = lines[6];
                sxt.yplxj = lines[5];
                sxtList.Add(sxt);
            }
            catch (Exception e)
            {
                //FileTools.WriteLog("IPsError.txt", $"读取IPS出错{line}");
                Log.Error($"读取IPS出错{line}");
                throw;
            }

        }

        AppStaticData.IpsSxtList=sxtList;
        sr.Close();
        sr.Dispose();

        #endregion
    }
    private static void GetSiheyiConfig()
    {
        try
        {
            var url = Configuration["ApplicationSettings:Url"] ?? "";
            var configName = Configuration["ApplicationSettings:ConfigName"] ?? "";
            var conifg = JKTrans.GetConfig(configName);
            if (string.IsNullOrEmpty(conifg))
            {
                Log.Information($"获取配置失败:{conifg}");


            }
            //WeakReferenceMessenger.Default.Send($"获取配置成功", "XiaoXi");
            Log.Information($"获取配置成功:{conifg}");
            AppStaticData.siheyiconfig = JsonConvert.DeserializeObject<SiheyiConfig>(conifg);
        }
        catch (Exception ex)
        {
            Log.Error($"获取配置失败");

            //WeakReferenceMessenger.Default.Send($"获取配置失败:{ex.Message}", "XiaoXi");
        }
    }
    private static void GetSensorConfig()
    {
        if (AppStaticData.siheyiconfig.SfIps.Value==1)
        {
            return;
        }
        try
        {
            var url = Configuration["ApplicationSettings:Url"] ?? "";
            var configName = Configuration["ApplicationSettings:SensorConfigName"] ?? "";
            var conifg = JKTrans.GetConfig(configName);
            if (string.IsNullOrEmpty(conifg))
            {
                Log.Information($"获取切换配置失败:{conifg}");


            }
            //WeakReferenceMessenger.Default.Send($"获取配置成功", "XiaoXi");
            Log.Information($"获取切换配置成功:{conifg}");
            AppStaticData.SensorsSxtList = JsonConvert.DeserializeObject<List<Sensor>>(conifg);
        }
        catch (Exception ex)
        {
            Log.Error($"获取切换配置失败");

            //WeakReferenceMessenger.Default.Send($"获取配置失败:{ex.Message}", "XiaoXi");
        }
    }

    private static SukiViews ConfigureViews(ServiceCollection services)
    {
        return new SukiViews()

            // Add main view
            .AddView<SukiUIDemoView, SukiUIDemoViewModel>(services)

        // Add pages

        .AddView<SplashView, SplashViewModel>(services)
        .AddView<ThemingView, ThemingViewModel>(services)
        .AddView<PlaygroundView, PlaygroundViewModel>(services)
        .AddView<EffectsView, EffectsViewModel>(services)
        .AddView<DashboardView, DashboardViewModel>(services)
        .AddView<ButtonsView, ButtonsViewModel>(services)
        .AddView<CardsView, CardsViewModel>(services)
        .AddView<CollectionsView, CollectionsViewModel>(services)
        .AddView<ContextMenusView, ContextMenusViewModel>(services)
        .AddView<DockView, DockViewModel>(services)
        .AddView<DockMvvmView, DockMvvmViewModel>(services)
        .AddView<ExpanderView, ExpanderViewModel>(services)
        .AddView<IconsView, IconsViewModel>(services)
        .AddView<InfoBarView, InfoBarViewModel>(services)
        .AddView<MiscView, MiscViewModel>(services)
        .AddView<ProgressView, ProgressViewModel>(services)
        .AddView<PropertyGridView, PropertyGridViewModel>(services)
        .AddView<TextView, TextViewModel>(services)
        .AddView<TogglesView, TogglesViewModel>(services)
        .AddView<ToastsView, ToastsViewModel>(services)
        .AddView<TabControlView, TabControlViewModel>(services)
        .AddView<StackPageView, StackPageViewModel>(services)
        .AddView<DialogsView, DialogsViewModel>(services)
        .AddView<ColorsView, ColorsViewModel>(services)
        .AddView<ExperimentalView, ExperimentalViewModel>(services)

        // Add docks view for DockMvvvm
        .AddView<DocumentText, DocumentTextViewModel>(services)
        .AddView<ErrorList, ErrorListViewModel>(services)
        .AddView<OutputView, OutputViewModel>(services)
        .AddView<PropertiesView, PropertiesViewModel>(services)
        .AddView<SolutionExplore, SolutionExploreViewModel>(services)

        // Add additional views
        .AddView<DialogView, DialogViewModel>(services)
        .AddView<VmDialogView, VmDialogViewModel>(services)
        .AddView<RecursiveView, RecursiveViewModel>(services)
        .AddView<CustomThemeDialogView, CustomThemeDialogViewModel>(services)

        // Add login view
        //.AddView<LoginView, LoginViewModel>(services)

        // Add CarControl view
        .AddView<CarListView, CarListViewModel>(services);
        //.AddView<SensorSetView, SensorSetViewModel>(services);
    }

    private static ServiceProvider ConfigureServices(ServiceCollection services)
    {
        // 注册配置
        services.AddSingleton(Configuration);
        //  注册服务
        services.AddSingleton<ClipboardService>();
        services.AddSingleton<PageNavigationService>();
        services.AddSingleton<ISukiToastManager, SukiToastManager>();
        services.AddSingleton<ISukiDialogManager, SukiDialogManager>();
        //退出服务
        services.AddSingleton<AvaloniaApplicationService>();

        return services.BuildServiceProvider();
    }

    /// <summary>
    /// 进程退出事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnProcessExit(object? sender, EventArgs e)
    {
        Log.Information("应用程序开始优雅关闭流程");
        CrashLogger.LogProcessExit("ProcessExit event triggered");
        ProcessMonitor.LogCurrentSnapshot("ProcessExit");
        PerformGracefulShutdown();
    }

    /// <summary>
    /// 应用程序退出事件处理
    /// </summary>
    /// <param name="sender">发送者</param>
    /// <param name="e">事件参数</param>
    private void OnApplicationExit(object? sender, ControlledApplicationLifetimeExitEventArgs e)
    {
        Log.Information($"桌面应用程序退出事件触发，退出码: {e.ApplicationExitCode}");
        CrashLogger.LogProcessExit($"ApplicationExit event triggered with exit code: {e.ApplicationExitCode}");
        ProcessMonitor.LogCurrentSnapshot("ApplicationExit");
        PerformGracefulShutdown();
    }

    /// <summary>
    /// 执行优雅关闭流程
    /// </summary>
    private void PerformGracefulShutdown()
    {
        try
        {
            Log.Information("开始执行优雅关闭流程");
            CrashLogger.LogApplicationException(null, "Starting graceful shutdown process");

            // 0. 停止进程监控
            try
            {
                ProcessMonitor.StopMonitoring();
                Log.Information("进程监控已停止");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "停止进程监控时发生异常");
            }

            // 1. 关闭SDK资源管理器
            try
            {
                var sdkManager = SukiUI.Demo.Infrastructure.SDKResourceManager.Instance;
                sdkManager.Shutdown();
                Log.Information("SDK资源管理器已关闭");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "关闭SDK资源管理器时发生异常");
            }

            // 2. 释放注册服务
            try
            {
                if (provider is IDisposable disposableServiceProvider)
                {
                    disposableServiceProvider.Dispose();
                    Log.Information("依赖注入容器已释放");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "释放依赖注入容器时发生异常");
            }

            // 3. 关闭日志系统
            Log.Information("优雅关闭流程完成");
            CrashLogger.LogProcessExit("Graceful shutdown completed successfully");
            Log.CloseAndFlush();
        }
        catch (Exception ex)
        {
            // 最后的异常处理，确保日志能记录
            try
            {
                Log.Fatal(ex, "优雅关闭流程中发生严重异常");
                CrashLogger.LogUnhandledException(ex, "PerformGracefulShutdown");
                Log.CloseAndFlush();
            }
            catch
            {
                // 如果连日志都无法记录，则忽略
            }
        }
    }

    /// <summary>
    /// 设置全局异常处理器
    /// </summary>
    private void SetupGlobalExceptionHandlers()
    {
        try
        {
            // 1. AppDomain未处理异常
            AppDomain.CurrentDomain.UnhandledException += OnAppDomainUnhandledException;

            // 2. Task未观察异常
            TaskScheduler.UnobservedTaskException += OnTaskUnobservedException;

            // 3. Avalonia应用程序异常（如果支持）
            if (this != null)
            {
                // 注意：某些版本的Avalonia可能不支持UnhandledException事件
                try
                {
                    // 尝试订阅Avalonia的未处理异常事件
                    var unhandledExceptionEvent = this.GetType().GetEvent("UnhandledException");
                    if (unhandledExceptionEvent != null)
                    {
                        var handler = new EventHandler<System.UnhandledExceptionEventArgs>(OnAvaloniaUnhandledException);
                        unhandledExceptionEvent.AddEventHandler(this, handler);
                    }
                }
                catch
                {
                    // 如果不支持，忽略
                }
            }

            CrashLogger.LogApplicationException(null, "Global exception handlers setup completed");
        }
        catch (Exception ex)
        {
            CrashLogger.LogApplicationException(ex, "Failed to setup global exception handlers");
        }
    }

    /// <summary>
    /// 处理AppDomain未处理异常
    /// </summary>
    private void OnAppDomainUnhandledException(object sender, UnhandledExceptionEventArgs e)
    {
        try
        {
            var exception = e.ExceptionObject as Exception;
            var isTerminating = e.IsTerminating;

            // 记录到崩溃日志
            CrashLogger.LogUnhandledException(exception ?? new Exception("Unknown exception object"), "AppDomain");

            // 记录到Serilog（如果可用）
            try
            {
                if (isTerminating)
                {
                    Log.Fatal(exception, "应用程序即将终止 - AppDomain未处理异常");
                }
                else
                {
                    Log.Error(exception, "AppDomain未处理异常");
                }
            }
            catch
            {
                // 忽略Serilog记录失败
            }

            // 如果程序即将终止，执行清理
            if (isTerminating)
            {
                try
                {
                    PerformEmergencyCleanup();
                }
                catch
                {
                    // 忽略清理失败
                }
            }
        }
        catch
        {
            // 确保异常处理器本身不会抛出异常
        }
    }

    /// <summary>
    /// 处理Task未观察异常
    /// </summary>
    private void OnTaskUnobservedException(object sender, UnobservedTaskExceptionEventArgs e)
    {
        try
        {
            var exception = e.Exception;

            // 记录到崩溃日志
            CrashLogger.LogTaskException(exception, "UnobservedTask");

            // 记录到Serilog（如果可用）
            try
            {
                Log.Error(exception, "Task未观察异常");
            }
            catch
            {
                // 忽略Serilog记录失败
            }

            // 标记异常为已观察，防止程序崩溃
            e.SetObserved();
        }
        catch
        {
            // 确保异常处理器本身不会抛出异常
        }
    }

    /// <summary>
    /// 处理Avalonia应用程序异常
    /// </summary>
    private void OnAvaloniaUnhandledException(object sender, System.UnhandledExceptionEventArgs e)
    {
        try
        {
            var exception = e.ExceptionObject as Exception;

            // 记录到崩溃日志
            CrashLogger.LogApplicationException(exception ?? new Exception("Unknown exception object"), "Avalonia Application");

            // 记录到Serilog（如果可用）
            try
            {
                Log.Error(exception, "Avalonia应用程序未处理异常");
            }
            catch
            {
                // 忽略Serilog记录失败
            }

            // 注意：System.UnhandledExceptionEventArgs 没有 Handled 属性
            // 这是因为它用于AppDomain级别的异常，无法被标记为已处理
        }
        catch
        {
            // 确保异常处理器本身不会抛出异常
        }
    }

    /// <summary>
    /// 执行紧急清理
    /// </summary>
    private void PerformEmergencyCleanup()
    {
        try
        {
            CrashLogger.LogProcessExit("Emergency cleanup due to fatal exception");

            // 1. 尝试释放关键资源
            try
            {
                if (provider is IDisposable disposableServiceProvider)
                {
                    disposableServiceProvider.Dispose();
                }
            }
            catch
            {
                // 忽略清理失败
            }

            // 2. 尝试关闭日志
            try
            {
                Log.Fatal("应用程序紧急退出");
                Log.CloseAndFlush();
            }
            catch
            {
                // 忽略日志关闭失败
            }

            // 3. 记录最终的崩溃信息
            CrashLogger.LogProcessExit("Emergency cleanup completed");
        }
        catch
        {
            // 忽略紧急清理失败
        }
    }
}