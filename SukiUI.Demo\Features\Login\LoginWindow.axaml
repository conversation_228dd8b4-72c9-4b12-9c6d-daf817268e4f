<Window x:Class="SukiUI.Demo.Features.Login.LoginWindow"
        xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:login="clr-namespace:SukiUI.Demo.Features.Login"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        Title="登录"
        Width="450"
        Height="600"
        d:DesignHeight="450"
        d:DesignWidth="800"
        Background="Transparent"
        CanResize="False"
        ExtendClientAreaToDecorationsHint="True"
        ShowInTaskbar="False"
        SystemDecorations="BorderOnly"
        TransparencyLevelHint="AcrylicBlur"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        mc:Ignorable="d">

    <login:LoginView />
</Window>