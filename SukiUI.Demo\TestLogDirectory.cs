using System;
using System.IO;
using SukiUI.Demo.Infrastructure;
using Serilog;

namespace SukiUI.Demo
{
    /// <summary>
    /// 测试日志目录创建的工具类
    /// </summary>
    public static class TestLogDirectory
    {
        /// <summary>
        /// 测试并显示日志目录信息
        /// </summary>
        public static void TestAndShowLogDirectories()
        {
            Console.WriteLine("=== 日志目录测试 ===");
            Console.WriteLine();

            // 显示基本路径信息
            Console.WriteLine("基本路径信息:");
            Console.WriteLine($"AppDomain.CurrentDomain.BaseDirectory: {AppDomain.CurrentDomain.BaseDirectory}");
            Console.WriteLine($"Assembly.GetExecutingAssembly().Location: {System.Reflection.Assembly.GetExecutingAssembly().Location}");
            Console.WriteLine($"Directory.GetCurrentDirectory(): {Directory.GetCurrentDirectory()}");
            Console.WriteLine($"Environment.CurrentDirectory: {Environment.CurrentDirectory}");
            Console.WriteLine();

            // 测试Serilog日志目录
            var serilogLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            Console.WriteLine("Serilog日志目录:");
            Console.WriteLine($"路径: {serilogLogPath}");
            Console.WriteLine($"存在: {Directory.Exists(serilogLogPath)}");
            
            try
            {
                if (!Directory.Exists(serilogLogPath))
                {
                    Directory.CreateDirectory(serilogLogPath);
                    Console.WriteLine("✓ Serilog日志目录创建成功");
                }
                else
                {
                    Console.WriteLine("✓ Serilog日志目录已存在");
                }

                // 列出现有日志文件
                var logFiles = Directory.GetFiles(serilogLogPath, "*.txt");
                Console.WriteLine($"现有日志文件数量: {logFiles.Length}");
                foreach (var file in logFiles)
                {
                    var fileInfo = new FileInfo(file);
                    Console.WriteLine($"  - {Path.GetFileName(file)} ({fileInfo.Length} bytes)");
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ Serilog日志目录处理失败: {ex.Message}");
            }
            Console.WriteLine();

            // 测试CrashLogger
            Console.WriteLine("CrashLogger测试:");
            Console.WriteLine($"初始化前状态: {CrashLogger.IsInitialized}");
            
            try
            {
                CrashLogger.Initialize();
                Console.WriteLine($"初始化后状态: {CrashLogger.IsInitialized}");
                Console.WriteLine($"日志目录: {CrashLogger.GetCurrentLogDirectory()}");
                Console.WriteLine();
                Console.WriteLine("详细信息:");
                Console.WriteLine(CrashLogger.GetLogDirectoryInfo());
                
                // 测试写入日志
                CrashLogger.LogTestEvent("TestLogDirectory", "测试日志目录创建功能");
                Console.WriteLine("✓ 测试日志写入完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ CrashLogger测试失败: {ex.Message}");
            }

            Console.WriteLine();
            Console.WriteLine("=== 测试完成 ===");
        }

        /// <summary>
        /// 创建测试日志文件
        /// </summary>
        public static void CreateTestLogFiles()
        {
            try
            {
                // 测试Serilog日志
                Log.Information("测试Serilog日志写入 - {Timestamp}", DateTime.Now);
                
                // 测试CrashLogger
                CrashLogger.LogTestEvent("CreateTestLogFiles", "创建测试日志文件");
                CrashLogger.LogApplicationException(new Exception("这是一个测试异常"), "TestLogDirectory");
                
                Console.WriteLine("✓ 测试日志文件创建完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 创建测试日志文件失败: {ex.Message}");
            }
        }
    }
}
