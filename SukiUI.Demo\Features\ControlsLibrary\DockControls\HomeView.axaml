﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:dockControls="clr-namespace:SukiUI.Demo.Features.ControlsLibrary.DockControls"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:DataType="dockControls:HomeViewModel"
             x:Class="SukiUI.Demo.Features.ControlsLibrary.DockControls.HomeView">
    <Grid
        RowDefinitions="*,25">
        <ContentControl
            Content="{Binding ActiveDockable}"
            Margin="4"
            Grid.Row="0"/>
        <TextBlock
            Text="{Binding FocusedDockable}"
            Margin="4"
            Grid.Row="1"/>
    </Grid>
</UserControl>
