﻿

using Newtonsoft.Json;


namespace SukiUI.Demo.Bll
{
    public class AppManager
    {
        /// <summary>
        /// 获取成绩
        /// </summary>
        /// <param name="kfxms"></param>
        /// <returns></returns>
        public static int GetKscjFromKfxms(string kfxms)
        {
            if (string.IsNullOrEmpty(kfxms)) return 100;
            int kscj = 100;
            string[] xms = kfxms.Split(',');
            for (int i = 0; i < xms.Length; i++)
            {
                BaseDataModel data = AppStaticData.KfxmList.FirstOrDefault(d => d.GetValueStr("j_code") == xms[i]);
                if (data == null)
                {
                    continue;
                }
                kscj += Convert.ToInt32(data.GetValueStr("j_value"));
            }
            if (kscj < 0) kscj = 0;
            return kscj;
        }
        public static string GetKfxmMCs(string kfxms)
        {
            if (kfxms == "") return "";
            string[] xms = kfxms.Split(',');
            for (int i = 0; i < xms.Length; i++)
            {
                BaseDataModel data = AppStaticData.KfxmList.Find(d => d.GetValueStr("j_code") == xms[i]);
                string str = "代码：" + xms[i].Split('_')[0];
                if (data != null)
                {
                    str += " 分值：" + data.GetValueStr("j_value") + " 描述：" + data.GetValueStr("j_name");
                }
                str += "；";
                xms[i] = str;
            }
            //return string.Join("\r\n", xms);
            return string.Join(Environment.NewLine, xms);
        }
        public static List<BaseDataModel> GetKfxmMCs2(string kfxms)
        {
            var data = new List<BaseDataModel>();
            if (kfxms != "")
            {  data = AppStaticData.KfxmList.Where(d => kfxms.Contains(d.GetValueStr("j_code"))).ToList(); }

            return data;
        }

        public static string GetKsyXmBySfzmhm(string ksysfzmhm)
        {
            BaseDataModel data = AppStaticData.KsyList.Find(d => d.GetValueStr("sfzmhm") == ksysfzmhm);
            if (data == null) return "";
            return data.GetValueStr("xm");
        }

        /// <summary>
        /// 上传视频文件
        /// </summary>
        /// <param name="dir"></param>




        public static BaseDataModel Get4In1XYXX(string sfzmhm, DateTime ykrq)
        {

            SiHeYiListParam param = new SiHeYiListParam();

            param.JKID = APIS.xueYuanByXinHao;
            var jsondata = new
            {
                sfzmhm = sfzmhm
            };
            param.DATA = JsonConvert.SerializeObject(jsondata);
            var list = APIS.GetListDataFromApi(param);

            if (list.Count == 0) return null;

            BaseDataModel data = list[0];
            //兼容摩托车
            if (string.IsNullOrEmpty(data.GetValueStr("extendedproperty1")))
            {
                data.SetColumnValue("jxjc", data.GetValueStr("dlr"));
            }
            //var baseUrl = Properties.Settings.Default.BaseURL;
            //var destdir = baseUrl + "/" + data.GetValueStr("t_mjzp").Replace("\\", "/");
            data.SetColumnValue("t_mjzp_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_mjzp")));
            data.SetColumnValue("t_zw_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_zw")));
            data.SetColumnValue("t_photo_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_photo")));
            data.SetColumnValue("t_zxzp_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_zxzp")));
            return data;
        }

        public static string getKscs(string sfzmhm)
        {

            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.kaoShiCiShu;
            param.DATA = JsonConvert.SerializeObject(new { sfzmhm = sfzmhm });
            var result = APIS.GetStringDataFromApi(param);
            return result;
        }

        public static void createSoftCheck()
        {
            //var sql = @"create table T_SOFT_CHECK
            //(
            //    softname     NVARCHAR2(200) not null,
            //    softversion  NVARCHAR2(200),
            //    keeplivetime DATE,
            //    errorcode    VARCHAR2(10) default '0',
            //    errormessage VARCHAR2(4000),
            //    constraint SOFTNAME primary key(SOFTNAME)
            //    )";

            //try
            //{

            //    var result = OracleHelper.ExecuteNonQuery(sql);
            //}
            //catch (Exception e)
            //{
            //    FileTools.WriteLog("SqlError.txt", $"{DateTime.Now}:升级库出错{e.Message}语句{sql}");
            //}
        }
        /// <summary>
        ///
        /// </summary>
        /// <param name="errorcode"> 0 正常,1预警,2已处理3硬盘问题4信号问题5四合一问题;</param>
        /// <param name="errorMsg"></param>
        public static void AddKeepLiveData(string errorcode = "", string errorMsg = "")
        {
            //暂时不再新建类，字段名不对应后面再说
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.softCheck;
            var data = new
            {

                //SOFTNAME = Application.ProductName,
                SOFTNAME = "4IN1",
                ERRORCODE = errorcode?.ToString(),
                ERRORMESSAGE = errorMsg?.ToString(),
                KEEPLIVETIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
                SOFTVERSION = "3.3.0.0"
                //SOFTVERSION = Application.ProductVersion
            };
            var jsonStr = JsonConvert.SerializeObject(data);
            param.DATA = jsonStr;
            var rest = APIS.GetStringDataFromApi(param);




        }

    }
}
