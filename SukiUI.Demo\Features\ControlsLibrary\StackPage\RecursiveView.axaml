<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.StackPage.RecursiveView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:stackPage="clr-namespace:SukiUI.Demo.Features.ControlsLibrary.StackPage"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="stackPage:RecursiveViewModel"
             mc:Ignorable="d">
    <suki:GlassCard HorizontalAlignment="Center" VerticalAlignment="Center">
        <suki:GroupBox Header="{Binding Title}">
            <StackPanel Spacing="10">
                <TextBlock>This is a recursive viewmodel, click the button to go deeper.</TextBlock>
                <Button Command="{Binding RecurseCommand}" Content="Click Me" />
            </StackPanel>
        </suki:GroupBox>
    </suki:GlassCard>
</UserControl>
