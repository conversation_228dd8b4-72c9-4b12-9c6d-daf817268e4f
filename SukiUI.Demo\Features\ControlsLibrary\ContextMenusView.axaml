<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.ContextMenusView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:ContextMenusViewModel"
             mc:Ignorable="d">
    <suki:GlassCard HorizontalAlignment="Center"
                    VerticalAlignment="Center"
                    IsInteractive="True">
        <suki:GlassCard.ContextMenu>
            <ContextMenu>
                <!--  Ignore your IDE, x:False is a valid intrinsic  -->
                <MenuItem Command="{Binding OptionClickedCommand}"
                          CommandParameter="{x:False}"
                          Header="Option" />
                <MenuItem Header="Disabled Option" IsEnabled="False" />
                <!--  Ignore your IDE, x:True is a valid intrinsic  -->
                <MenuItem Command="{Binding OptionClickedCommand}"
                          CommandParameter="{x:True}"
                          Header="Option With Icon">
                    <MenuItem.Icon>
                        <PathIcon Width="15"
                                  Height="15"
                                  Data="{x:Static suki:Icons.Star}" />
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="Disabled With Icon" IsEnabled="False">
                    <MenuItem.Icon>
                        <PathIcon Width="13"
                                  Height="13"
                                  Data="{x:Static suki:Icons.Cross}" />
                    </MenuItem.Icon>
                </MenuItem>
                <MenuItem Header="Separator Next" />
                <Separator/>
                <MenuItem Header="Submenu">
                    <MenuItem Header="Sub-Submenu">
                        <MenuItem Command="{Binding NestedOptionClickedCommand}" Header="Nested Option" />
                        <MenuItem Header="-" />
                        <MenuItem Header="Disabled Nested Option" IsEnabled="False" />
                    </MenuItem>
                </MenuItem>
            </ContextMenu>
        </suki:GlassCard.ContextMenu>
        <suki:GroupBox Header="Context Menu Demo">
            <TextBlock HorizontalAlignment="Center"
                       VerticalAlignment="Center"
                       Classes="h3">
                Right click anywhere in this card to open a context menu.
            </TextBlock>
        </suki:GroupBox>
    </suki:GlassCard>
</UserControl>
