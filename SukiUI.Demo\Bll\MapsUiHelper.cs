﻿using Mapsui.Styles;
using System.Runtime.CompilerServices;

namespace SukiUI.Demo.Bll
{
    public class MapsUiHelper
    {
        /// <summary>
        /// 车轮点样式
        /// </summary>
        /// <returns></returns>
        public static IStyle   CreateClStyle()
        {
            var style = new SymbolStyle();
            style.SymbolType = SymbolType.Ellipse;
            
            
            style.SymbolScale = 0.1;
            style.Fill = new Brush(Color.Black);
            return style;
        }
        /// <summary>
        /// 车辆样式
        /// </summary>
        /// <returns></returns>
        public static VectorStyle CreateCarStyle()
        {
            #region 车模样式
            return new VectorStyle
            {
                Fill = new Brush(new Color(150, 150, 30, 128)),
                Outline = new Mapsui.Styles.Pen
                {
                    Color = Color.Red,
                    Width = 1,
                    PenStyle = PenStyle.Solid,
                    PenStrokeCap = PenStrokeCap.Round
                }
            };
            #endregion
        }

    }
}
