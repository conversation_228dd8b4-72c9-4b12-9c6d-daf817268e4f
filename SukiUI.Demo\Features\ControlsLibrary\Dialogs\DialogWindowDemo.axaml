<suki:SukiWindow x:Class="SukiUI.Demo.Features.ControlsLibrary.Dialogs.DialogWindowDemo"
                 xmlns="https://github.com/avaloniaui"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:suki="https://github.com/kikipoulet/SukiUI"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 Title="Dialog Window Demo"
                 Width="760"
                 Height="400"
                 d:DesignHeight="450"
                 d:DesignWidth="800"
                 mc:Ignorable="d">
    <suki:SukiWindow.Hosts>
        <suki:SukiDialogHost Name="DialogHost"/>
    </suki:SukiWindow.Hosts>
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Dialogs">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        Demos the ability for individual windows (or the main window) to be addressed by the dialog system.
                    </TextBlock>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard>
                    <suki:GroupBox Header="Show In This Window">
                        <Button Margin="15,10,15,0"
                                Click="ShowDialogInThisWindowClicked"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Show In MainWindow">
                        <Button Margin="15,10,15,0"
                                Click="ShowDialogInMainWindowClicked"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Show In Both Windows">
                        <Button Margin="15,10,15,0"
                                Click="ShowDialogInBothWindowsClicked"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</suki:SukiWindow>
