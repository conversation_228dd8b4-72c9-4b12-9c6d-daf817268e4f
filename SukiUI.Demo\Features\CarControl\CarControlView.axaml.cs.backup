using Avalonia;
using Avalonia.Controls;
using System.Text.RegularExpressions;
using System.Text;
using SukiUI.Demo.Bll;
using Avalonia.Threading;
using Mapsui.Layers;
using Mapsui.Nts.Extensions;
using Mapsui.Providers;
using Mapsui.Styles;
using Mapsui;
using NetTopologySuite.Geometries;
using Serilog;
using CommunityToolkit.Mvvm.Messaging;
using System.Diagnostics;
using Avalonia.Controls.ApplicationLifetimes;
using System.ComponentModel;
using SkiaSharp;
using Mapsui.Utilities;



namespace SukiUI.Demo.Features.CarControl;

public partial class CarControlView : UserControl
{
    public CarControlView()
    {

        InitializeComponent();
        
        this.Loaded += (s, e) =>
        {
            WeakReferenceMessenger.Default.Register<CarControlView, string, string>(
                   this,
                  "Udp",
                  (recipient, message) => ReceiveMessageUdp(message));
            try
            {
                GetDataContext();
                // 初始化车辆模型
                InitCarModel();
                // 初始化地图
                InitMap();
                // 初始化时也要调用一次
                model?.InitKsxhs(PanelKsxhs.Bounds.Width);
                model?.InitExamItems(PanelExamItems.Bounds.Height);
                // 初始化信号
                InitCarXh();
                // 上传图片
                StartUpload();
                Log.Information("界面初始化成功");

            }
            catch (Exception ex)
            {

                WeakReferenceMessenger.Default.Send($"Dialog-Exit{ex.Message}{ex.StackTrace}", "XiaoXi");
            }






        };
        this.Unloaded += (s, e) =>
        {

            _cts?.Cancel();
            //_cts?.Dispose(); 
            WeakReferenceMessenger.Default.UnregisterAll(this);
        };
    }
    protected override void OnAttachedToVisualTree(VisualTreeAttachmentEventArgs e)
    {
        base.OnAttachedToVisualTree(e);
        // 要先执行获取数据上下文模型，获取车牌号等信息，才能初始化车辆模型等操作

    }
    private void Exit()
    {
        if (Application.Current?.ApplicationLifetime is IClassicDesktopStyleApplicationLifetime desktop)
        {
            desktop.Shutdown();
        }
    }
    private void ReceiveMessageUdp(string message)
    {
        xhstr = message;
        Debug.WriteLine($"[ModelC] 收到来自ModelB的消息: {message}");

    }
    public static readonly StyledProperty<CarModel?> CarModelProperty = AvaloniaProperty.Register<CarControlView, CarModel?>(nameof(CarModel), defaultValue: new CarModel());
    public CarModel? CarModel
    {
        get => GetValue(CarModelProperty);
        set => SetValue(CarModelProperty, value);
    }
    public int A2bitmapId { get; private set; }

    private void InitMap()
    {
        var smapFileName = AppStaticData.siheyiconfig.Dtjztc.Value;
        var mapFileName = $@"{clxh}map";
        var mapFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, mapFileName, smapFileName);
        if (!File.Exists(mapFilePath))
        {
            mapFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "map", smapFileName);
        }
        if (!File.Exists(mapFilePath))
        {
            WeakReferenceMessenger.Default.Send($"没有找到地图文件{mapFilePath}", "XiaoXi");
            //Log.Error($"没有找到对应的地图文件{mapFilePath}");
            WeakReferenceMessenger.Default.Send($"Dialog-Exit没有找到地图文件{mapFilePath}", "XiaoXi");
            return;
        }

        var list = InintXdXyz(mapFilePath);
        
        mapControl.Map = SharpMapHelper.InitializeMapXyz(list, 0.0f);
        var couont = mapControl.Map.Layers.Count;
        // 是否允许鼠标滚轮缩放
        mapControl.Map.Navigator.ZoomLock = !AppStaticData.siheyiconfig.IsMouseWheel.Value;
        //mapControl.Map.Navigator.ViewportChanged += Map_MapViewOnChange();
        //mapControl.Map.PropertyChanged += Map_MapViewOnChange();
        //mapControl.Map.Layers.Changed+= Map_MapViewOnChange1();
        //mapControl.PropertyChanged += Map_MapViewOnChange();
        // 先执行一次,
        Map_MapViewOnChange1(null, null);
        // 暂时没有找到地图的变化事件，所以使用地图刷新事件来代替实现。
        if (AppStaticData.siheyiconfig.IsMouseWheel.Value && AppStaticData.siheyiconfig.SfImg.Value == 1)
        {
            mapControl.Map.Navigator.RefreshDataRequest += Map_MapViewOnChange1;
        }


        ////添加事件,但是这个会导致地图卡顿严重
        //mapControl.PointerWheelChanged += Map_MapViewOnChange1;


    }


    private double oldScale = 0;
    // 监听地图的缩放注册图片
    private PropertyChangedEventHandler Map_MapViewOnChange()
    {
        if (AppStaticData.siheyiconfig.SfImg.Value != 1)
        {
            return null;
        }
        const double tolerance = 0.000001;

        var scale = mapControl.Map.Navigator.Viewport.Resolution;

        var ischange = Math.Abs(oldScale - scale) > tolerance;
        if (ischange)
        {
            InitMapCarSize(scale);

        }
        return null;
    }

    private void InitMapCarSize(double scale)
    {
        GetMapCarSize();
        // 获取A2车辆
        if (AppStaticData.siheyiconfig.SfA2.Value == 1)
        { GetMapCarSizeA2(); }
        oldScale = scale;
    }

    private void Map_MapViewOnChange1(object? sender, EventArgs e)
    {
        if (AppStaticData.siheyiconfig.SfImg.Value != 1)
        {
            return;
        }

        const double tolerance = 0.000001;

        var scale = mapControl.Map.Navigator.Viewport.Resolution;

        var ischange = Math.Abs(oldScale - scale) > tolerance;
        if (ischange)
        {
            InitMapCarSize(scale);
        }

        return;
    }
   
    
    private static List<Sensor> InintXdXyz(string filePath)
    {
        List<Sensor> Sensors = new List<Sensor>();
        //var filePath = System.IO.Path.Combine(System.AppDomain.CurrentDomain.BaseDirectory, "xianshi.xyz");
        var sensorStr = File.ReadAllText(filePath);
        sensorStr += System.Environment.NewLine + "DESCRIPTION";
        var pattern = @"(xianshi=)[.\\\s\\\S]*?(?=([DESCRIPTION]))";
        var matchs = Regex.Matches(sensorStr, pattern);
        //foreach (Match match in matchs)
        for (int j = 0; j < matchs.Count; j++)

        {

            //var value = Regex.Split(matchs[j].Value, @"\r\n").Where(v => !string.IsNullOrEmpty(v)).ToList();
            var value = Regex.Split(matchs[j].Value, System.Environment.NewLine).Where(v => !string.IsNullOrEmpty(v)).ToList();
            if (value.Any())
            {

                var obj = new Sensor();
                // =1表示虚线传感器
                if (value[0].EndsWith("=1"))
                {
                    value[0] += $"-xx-{j}";

                }
                // 否则为实线
                else
                {
                    value[0] += $"-sx-{j}";

                }

                // 将中文的横线替换为英文
                obj.Name = value[0].Replace("——", "-");
                // count-1是因为第一个是传感器名称，后面的才是坐标点
                for (int i = 1; i < value.Count(); i++)
                {
                    var pointstr = value[i];

                    if (string.IsNullOrWhiteSpace(pointstr.Trim()))
                    {
                        Log.Information(@$"pointstr为空");
                        continue;
                    }
                    var strings = pointstr.Split(',');
                    var p = new DoublePoint(Convert.ToSingle(strings[0]),
                        Convert.ToSingle(strings[1]));
                    obj.Data.Add(p);
                }

                if (Sensors.Any(s => s.Name == obj.Name))
                {
                    //MessageBox.Show($"{obj.Name}:传感器重复，请检查数据！");
                    Console.WriteLine($"{obj.Name}:传感器重复，请检查数据！");
                    break;
                }

                Sensors.Add(obj);
            }
        }

        return Sensors;
    }

    private string xhstr = string.Empty;
    private string xhstr_old = string.Empty;    
    private string clxh = string.Empty;
    private carModel carA2;


    private carModel car; // 车辆（无初始值）
    private CarControlViewModel model;
    public carModel carLoad()
    {
        var cr = new carModel();
        // 车辆定位定义也放到Map文件夹内。
        var smapFileName = "车辆定位.ini";
        var mapFileName = $@"{clxh}map";
        var carFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, mapFileName, smapFileName);
        if (!File.Exists(carFilePath))
        {
            carFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "map", smapFileName);
        }
        if (!File.Exists(carFilePath))
        {
            WeakReferenceMessenger.Default.Send($"没有找到定位文件{carFilePath}", "XiaoXi");
            //Log.Error($"没有找到对应的地图文件{mapFilePath}");
            WeakReferenceMessenger.Default.Send($"Dialog-Exit没有找到定位文件{carFilePath}", "XiaoXi");
            //return cr;
        }

        //var cartype = model.CarType;

        try
        {
            // Linux下不可用
            //var ini = new readINI(Current + iniName);
            var ini = new Sini.IniFile(carFilePath);
            //var ini = parser.ReadFile(fileName);
            //if (configModel.CarConfig != "")
            //{ ini = new readINI(configModel.CarConfig); }
            cr.Cx = ini.GetStr("sys", "cx");
            cr.Htx_x = Convert.ToDouble(ini.GetStr("sys", "htx").Split(',')[0]);
            cr.Htx_y = Convert.ToDouble(ini.GetStr("sys", "htx").Split(',')[1]);
            cr.Hxj = Convert.ToDouble(ini.GetStr("sys", "hxj"));
            //cr.Hgj = 0; // Convert.ToDouble(ini.ReadValue("sys", "hgj"));
            //cr.Fyj = 0; // Convert.ToDouble(ini.ReadValue("sys", "fyj"));
            cr.Hgj = Convert.ToDouble(ini.GetStr("sys", "hgj"));
            cr.Fyj = Convert.ToDouble(ini.GetStr("sys", "fyj"));
            cr.Dpgd = Convert.ToDouble(ini.GetStr("sys", "dpgd"));
            cr.Htxgd = Convert.ToDouble(ini.GetStr("sys", "htxgd"));
            for (var i = 1; i <= 32; i++)
            {
                var iniStr = ini.GetStr("clbd", i.ToString()).Split(',');
                var cd = new clbd();
                cd.Index = i;
                cd.X = Convert.ToDouble(iniStr[0]);
                cd.Y = Convert.ToDouble(iniStr[1]);
                cd.Jd = Convert.ToDouble(iniStr[2]);
                cd.Xdjd = Convert.ToDouble(iniStr[3]);
                cd.Jl = Convert.ToDouble(iniStr[4]);
                cr.Clbdsj.Add(cd);
            }
            //cr.Qhjl = Convert.ToDouble(ini.ReadValue("tz", "qhjl"));
            //cr.Zyjl = Convert.ToDouble(ini.ReadValue("tz", "zyjl"));
            //cr.Xzjd = Convert.ToDouble(ini.ReadValue("tz", "xzjd"));
            // 这里是厘米计算，车辆定位是按照厘米标定的

            cr.Qhjl = Convert.ToDouble(ini.GetStr("tz", "qhjl")) * 0.01;
            cr.Zyjl = Convert.ToDouble(ini.GetStr("tz", "zyjl")) * 0.01;
            cr.Xzjd = Convert.ToDouble(ini.GetStr("tz", "xzjd")) * 0.1;
        }
        catch (Exception ex)
        {
            Log.Error(@$"加载车模型出错{ex.Message}{ex.StackTrace}");
        }
        return cr;
    }

    private void GetDataContext()
    {
        model = DataContext as CarControlViewModel;
        if (model != null)
        {
            clxh = model.CarNo;            
        }
    }

    public carModel carLoadA2()
    {
        var cr = new carModel();
        // A2车辆定位定义也放到Map文件夹内。
        var smapFileName = "A2车辆定位.ini";
        var mapFileName = $@"{clxh}map";
        var carFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, mapFileName, smapFileName);
        if (!File.Exists(carFilePath))
        {
            carFilePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "map", smapFileName);
        }
        if (!File.Exists(carFilePath))
        {
            WeakReferenceMessenger.Default.Send($"没有找到定位文件{carFilePath}", "XiaoXi");
            //Log.Error($"没有找到对应的地图文件{mapFilePath}");
            WeakReferenceMessenger.Default.Send($"Dialog-Exit没有找到定位文件{carFilePath}", "XiaoXi");

            //return cr;

        }
        try
        {

            var ini = new Sini.IniFile(carFilePath);
            //if (configModel.CarConfig != "")
            //{ ini = new readINI(configModel.CarConfig); }
            cr.Cx = ini.GetStr("sys", "cx");
            cr.Htx_x = Convert.ToDouble(ini.GetStr("sys", "htx").Split(',')[0]);
            cr.Htx_y = Convert.ToDouble(ini.GetStr("sys", "htx").Split(',')[1]);
            cr.Hxj = Convert.ToDouble(ini.GetStr("sys", "hxj"));
            cr.Hgj = Convert.ToDouble(ini.GetStr("sys", "hgj"));
            cr.Fyj = Convert.ToDouble(ini.GetStr("sys", "fyj"));
            // 这里不计算俯仰角和横滚角
            //cr.Hgj = 0; // Convert.ToDouble(ini.ReadValue("sys", "hgj"));
            //cr.Fyj = 0; // Convert.ToDouble(ini.ReadValue("sys", "fyj"));
            cr.Dpgd = Convert.ToDouble(ini.GetStr("sys", "dpgd"));
            cr.Htxgd = Convert.ToDouble(ini.GetStr("sys", "htxgd"));

            for (var i = 1; i <= 32; i++)
            {
                var iniStr = ini.GetStr("clbd", i.ToString()).Split(',');
                var cd = new clbd();
                cd.Index = i;
                cd.X = Convert.ToDouble(iniStr[0]);
                cd.Y = Convert.ToDouble(iniStr[1]);
                cd.Jd = Convert.ToDouble(iniStr[2]);
                cd.Xdjd = Convert.ToDouble(iniStr[3]);
                cd.Jl = Convert.ToDouble(iniStr[4]);
                cr.Clbdsj.Add(cd);
            }

            //cr.Qhjl = Convert.ToDouble(ini.ReadValue("tz", "qhjl"));
            //cr.Zyjl = Convert.ToDouble(ini.ReadValue("tz", "zyjl"));
            //cr.Xzjd = Convert.ToDouble(ini.ReadValue("tz", "xzjd")); 
            cr.Qhjl = Convert.ToDouble(ini.GetStr("tz", "qhjl")) * 0.01;
            cr.Zyjl = Convert.ToDouble(ini.GetStr("tz", "zyjl")) * 0.01;
            cr.Xzjd = Convert.ToDouble(ini.GetStr("tz", "xzjd")) * 0.1;
        }
        catch (Exception ex)
        {
            ////File.AppendAllText("udpgjrz.txt", "carLoad" + DateTime.Now + ":" + ex.Message + "\r\n");          
            Log.Error(@$"加载车模型出错{ex.Message}{ex.StackTrace}");
        }


        return cr;
    }
    public float MapCarWidth = 0;
    public float MapCarHeigth = 0;
    public float MapCarWidthA2 = 0;
    public float MapCarHeigthA2 = 0;
    public void GetMapCarSize()
    {
        var list = new List<Coordinate>();
        var carSimple = new CarSimple();
        carSimple.CourseAngle = 0;
        carSimple.Dp_Htx = new DoublePoint(car.Htx_x, car.Htx_y);
        //carSimple.PitchAngle = car.Fyj;
        //carSimple.RollAngle = car.Hgj;
        carSimple.PitchAngle = 0;
        carSimple.RollAngle = 0;
        foreach (var c in car.Clbdsj)
            if (c.Index <= 24)
            {
                var pointHelp = new PintHelp();
                var d = pointHelp.newPointGet(carSimple, car, c);
                //dp[c.Index - 1] = new PointF((float)d.X, (float)d.Y);
                list.Add(new Coordinate(d.X, d.Y));
            }
            else if (c.Index == 25)
            {
                //dp[c.Index - 1] = dp[0];
                list.Add(list[0]);
            }
        NetTopologySuite.Geometries.GeometryFactory geometryFactory1 = new NetTopologySuite.Geometries.GeometryFactory();
        var geo = geometryFactory1.CreatePolygon(list.ToArray());

        var geoSize = geo.EnvelopeInternal;



        var resolution = mapControl.Map.Navigator.Viewport.Resolution;
        // 获取地图上车辆的2维像素大小
        MapCarHeigth = (float)(geoSize.Height / resolution);
        MapCarWidth = (float)(geoSize.Width / resolution);
        RegisterImage();

    }
    // 获取A2车辆地图大小
    public void GetMapCarSizeA2()
    {

        var list = new List<Coordinate>();
        var carSimple = new CarSimple();
        carSimple.CourseAngle = 0;
        carSimple.Dp_Htx = new DoublePoint(carA2.Htx_x, carA2.Htx_y);
        //carSimple.PitchAngle = carA2.Fyj;
        //carSimple.RollAngle = carA2.Hgj; 
        // 将中心点设为车头的中心点
        carSimple.PitchAngle = 0;
        carSimple.RollAngle = 0;
        foreach (var c in carA2.Clbdsj)
            if (c.Index <= 24)
            {
                var pointHelp = new PintHelp();
                var d = pointHelp.newPointGet(carSimple, carA2, c);
                //dp[c.Index - 1] = new PointF((float)d.X, (float)d.Y);
                list.Add(new Coordinate(d.X, d.Y));
            }
            else if (c.Index == 25)
            {
                //dp[c.Index - 1] = dp[0];
                list.Add(list[0]);
            }
        NetTopologySuite.Geometries.GeometryFactory geometryFactory1 = new NetTopologySuite.Geometries.GeometryFactory();
        var geo = geometryFactory1.CreatePolygon(list.ToArray());

        var geoSize = geo.EnvelopeInternal;
        var rotation = mapControl.Map.Navigator.Viewport.Rotation;
        // 获取地图上车辆的2维像素大小
        MapCarHeigthA2 = (float)(geoSize.Height / rotation);
        MapCarWidthA2 = (float)(geoSize.Width / rotation);
        RegisterImage(true);
    }
    private int bitmapId = 0;
    static string pngpath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PNG");
    static string carImageName = "Car.png";
    public void RegisterImage(bool A2 = false)
    {
        if (A2)
        {
            carImageName = "GuaCar.png";
        }
        // 车辆图片命名规则：Car类型+01Car.png

        var imageName = $@"{clxh}{carImageName}";

        var symbolNme = Path.Combine(pngpath, imageName);
        // 如果没有找到对应的Car图片，则使用车型Car图片，如C1Car.png
        if (!File.Exists(symbolNme))
        {
            // 如果还没有找到，使用Car.png
            var carType = model.CarType;
            imageName = $"{carType}{carImageName}";
            symbolNme = Path.Combine(pngpath, imageName);

            // 最后使用默认的Car.png
            if (!File.Exists(symbolNme))
            {
                imageName = carImageName;
                symbolNme = Path.Combine(pngpath, imageName);
                if (!File.Exists(symbolNme))
                {
                    WeakReferenceMessenger.Default.Send($"Dialog-Exit没有找到车辆图片{symbolNme}", "XiaoXi");
                    return;
                }
            }
        }


        // 1. 目标坐标范围

        using var original = SKBitmap.Decode(symbolNme);

        // 2. 获取图片
        int targetWidth = (int)MapCarWidth;
        int targetHeight = (int)MapCarHeigth;

        // 3. 缩放图片
        using var resized = original.Resize(new SKImageInfo(targetWidth, targetHeight), SKFilterQuality.High);
        // 4. 创建自定义 Style
        var imageBytes = ImageHelper.SKBitmapToByteArray(resized);
        if (A2)
        {
            A2bitmapId = BitmapRegistry.Instance.Register(imageBytes);
        }
        else
            bitmapId = BitmapRegistry.Instance.Register(imageBytes);


    }
    private void InitCarModel()
    {

        car = carLoad();
        if (AppStaticData.siheyiconfig.SfA2.Value == 1)
        {
            carA2 = carLoadA2();
        }
    }
    private CancellationTokenSource? _cts = new CancellationTokenSource();
    private void InitCarXh()
    {
        Task.Factory.StartNew(BindCarXH, _cts.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);
        //Task.Run(BindCarXH, TaskCreationOptions.LongRunning);
    }
    private void BindCarXH()
    {
        //return;
        var clbm = model.CarNumber;
        #region 调试
        //var sxsj = AppStaticData.ycjkConfig.xhsxsj;
        var sxsj = AppStaticData.siheyiconfig.Xhsxsj.Value;
        #endregion
        //while (true)
        while (!_cts.Token.IsCancellationRequested)

            try
            {
                Thread.Sleep(sxsj);
                //await Task.Delay(sxsj,_cts.Token);
                // 调试
                //if (IsDebug)
                if (AppStaticData.siheyiconfig.IsDebug.Value)
                {
                    if (string.IsNullOrEmpty(xhstr))
                    {
                        continue;
                    }
                    xhstr = xhstr.ToUpper();
                    //if (!xhstr.Contains("420200406040201830")) continue;
                }


                // end

                #region 正式

                else
                {
                    if (xhstr_old == xhstr) continue;
                    if (!xhstr.StartsWith("XHJS") && !xhstr.EndsWith("END")) continue;

                    if (!xhstr.Contains(clbm)) continue;
                    xhstr=xhstr.ToUpper();
                }
                // 记录UPD数据
                //if (AppStaticData.ycjkConfig.saveupd == "1")
                //{
                //    FileTools.WriteLog("comData.ini", xhstr + "\r\n");
                //}
                #endregion
                var bmd = new BaseDataModel();
                // 会导致从串口读多个数据时，无法保证数据是哪个时刻的数据
                //xhstr = xhstr.ToLower();
                var xhlist = xhstr.Split(',');
                #region  这里是考试科目，科目2和科目3可以共用数据
                if (xhlist[6] != AppStaticData.siheyiconfig.Kskm.Value.ToString())
                {
                    isRightKm = false;
                    continue;
                }
                else
                {
                    isRightKm = true;
                }
                #endregion
                // 可能考试项目序号
                //if (xhlist.Length > 1) bmd.SetColumnValue("jgkslsh", xhlist[1]);
                // 车辆编码
                if (xhlist.Length > 2) bmd.SetColumnValue("clbm", xhlist[2]);
                // 车辆状态
                if (xhlist.Length > 3) bmd.SetColumnValue("kczt", xhlist[3]);
                ////车辆状态,车辆状态信息
                //if (bmd.GetValueStr("kczk")=="0")
                //{
                //    ResetShowValues(null);
                //}
                // 身份证号码
                if (xhlist.Length > 4) bmd.SetColumnValue("sfzmhm", xhlist[4]);

                // 考试员
                if (xhlist.Length > 5) bmd.SetColumnValue("ksy", xhlist[5]);
                // 考试科目
                if (xhlist.Length > 6) bmd.SetColumnValue("kskm", xhlist[6]);
                // 设备信号状态
                if (xhlist.Length > 7) bmd.SetColumnValue("sbxhzt", xhlist[7]);
                // 当前速度
                if (xhlist.Length > 8) bmd.SetColumnValue("sd", xhlist[8]);
                // 当前累计里程
                if (xhlist.Length > 9) bmd.SetColumnValue("lc", xhlist[9]);
                // 发动机转速
                if (xhlist.Length > 10) bmd.SetColumnValue("fdjzs", xhlist[10]);

                if (xhlist.Length > 16) bmd.SetColumnValue("gpsdxjl", xhlist[16]);
                // GPS 东向距离
                if (xhlist.Length > 17) bmd.SetColumnValue("gpsbxjl", xhlist[17]);
                // GPS 北向距离
                if (xhlist.Length > 18) bmd.SetColumnValue("gpstxjl", xhlist[18]);
                // 航向角
                if (xhlist.Length > 19) bmd.SetColumnValue("hxj", xhlist[19]);
                // 俯仰角
                if (xhlist.Length > 20) bmd.SetColumnValue("fyj", xhlist[20]);
                // 横滚角
                if (xhlist.Length > 21) bmd.SetColumnValue("hgj", xhlist[21]);

                // 项目状态
                if (xhlist.Length > 22) bmd.SetColumnValue("xmzt", xhlist[22]);
                // 设备编号
                //if (xhlist.Length > 23) bmd.SetColumnValue("sbbh", xhlist[23]);
                // 扣分分值
                if (xhlist.Length > 24) bmd.SetColumnValue("kffz", xhlist[24]);
                // 扣分数量
                //if (xhlist.Length > 25) bmd.SetColumnValue("kfsl", xhlist[25]);
                // 扣分项目
                if (xhlist.Length > 26) bmd.SetColumnValue("kfxm", xhlist[26]);

                // 实时成绩
                if (xhlist.Length > 34)
                {
                    bmd.SetColumnValue("sscj", xhlist[34]);
                }


                //// string gpsData = "GPS数据字符串";
                //GPS数据处理相关代码
                //newBmd = bmd;
                #region
                //Task.Factory.StartNew(() =>
                //{
                Dispatcher.UIThread.Invoke(() =>
                {
                    try
                    {
                        //if (DataContext is CarControlViewModel vm )
                        //{

                        //    vm?.UpdateOthers(bmd);
                        //}
                        model?.UpdateOthers(bmd);
                    }
                    catch (Exception)
                    {

                        //throw;
                    }

                });

                //});

                #endregion
                var gpsData = bmd.GetValueStr("hxj") + "," + bmd.GetValueStr("fyj") + "," +
                              bmd.GetValueStr("hgj") + "," + bmd.GetValueStr("gpsdxjl") + "," +
                              bmd.GetValueStr("gpsbxjl");

                //这里有一个问题，上传图片的定时器和数据更新的定时器不能及时切换，所以优化
                //使用数据切换时，使用的是大车数据，即考试项目的实时数据
                ///<seealso cref="CastNvrByXh"/>
                //if (AppStaticData.ycjkConfig.kskm == "2" && AppStaticData.ycjkConfig.sfIps!="1")
                //{ CastNvr(bmd);}
                //if (AppStaticData.ycjkConfig.kskm == "2" || Properties.Settings.Default.IsMoto)
                //    CastNvr(bmd);





                //this.Invoke(new Action(() =>
                Dispatcher.UIThread.Invoke(() =>
                {
                    //ches.Text = bmd.GetValueStr("sd");
                    //zhuans.Text = bmd.GetValueStr("fdjzs");
                    //try
                    //{
                    //    lic.Text = $"{Convert.ToDecimal(bmd.GetValueStr("lc")):N3}";
                    //}
                    //catch (Exception)
                    //{
                    //}

                    //if (!sfA2)
                    if (AppStaticData.siheyiconfig.SfA2.Value != 1)
                    {
                        carMove(gpsData);
                    }
                    else
                    {
                        if (xhlist.Length > 33)
                        {
                            //// string gpsData = "GPS数据字符串";
                            var gpsDataA2 = xhlist[31] + "," + xhlist[32] + "," +
                                            xhlist[33] + "," + xhlist[28] + "," +
                                            xhlist[29];
                            carMoveA2(gpsData, gpsDataA2);
                        }
                        //没有挂车数据，所以只移动车头。
                        else
                        {
                            carMove(gpsData);
                        }



                    }

                });

                //设备信号信息
                //Task.Factory.StartNew(() => { SetSBXH(bmd.GetValueStr("sbxhzt")); });

                xhstr_old = xhstr;


            }
            catch (Exception ex)
            {

                Log.Error(@$"移动车辆数据出错{xhstr}：{ex.Message}{ex.StackTrace}");
            }
            finally
            {

            }

        //#endif

    }
    private static readonly object obj = new object();
    public static ComData comDataSet(string data, carModel car)
    {
        var comdata = new ComData();
        var cd = data.Split(',');

        comdata.Course = Convert.ToDouble(cd[0]); // 航向角（相对于真北的角度）

        comdata.Pitch = Convert.ToDouble(cd[1]);
        // 先不计算转向角度
        if (AppStaticData.siheyiconfig.Jshg.Value)
        {
            comdata.Roll = Convert.ToDouble(cd[2]);
        }
        else
        {
            comdata.Roll = 0.0;
        }

        // 20220413 这里不计算俯仰角，横滚角
        //comdata.Pitch = 0.0;
        //comdata.Roll = 0.0;
        comdata.EastDistance = Convert.ToDouble(cd[3]);
        comdata.NorthDistance = Convert.ToDouble(cd[4]);


        if (car.Qhjl != 0)
        {
            var jd = comdata.Course;

            if (car.Qhjl > 0)
                jd = jd + 0;
            else
                jd = jd + 180;
            if (jd > 360)
                jd = jd - 360;
            var Rage2 = jd / 180 * Math.PI;
            comdata.EastDistance = comdata.EastDistance + Math.Sin(Rage2) * Math.Abs(car.Qhjl);
            comdata.NorthDistance = comdata.NorthDistance + Math.Cos(Rage2) * Math.Abs(car.Qhjl);
        }

        if (car.Zyjl != 0)
        {
            var jd = comdata.Course;

            if (car.Zyjl > 0)
                jd = jd + 90;
            else
                jd = jd + 270;
            if (jd > 360)
                jd = jd - 360;
            var Rage2 = jd / 180 * Math.PI;
            comdata.EastDistance = comdata.EastDistance + Math.Sin(Rage2) * Math.Abs(car.Zyjl);
            comdata.NorthDistance = comdata.NorthDistance + Math.Cos(Rage2) * Math.Abs(car.Zyjl);
        }

        if (car.Xzjd != 0)
            // 这里应该使用cd[0]，而不应该是cd[3]
            //comdata.Course = Convert.ToDouble(cd[3]) + car.Xzjd;
            comdata.Course = Convert.ToDouble(cd[0]) + car.Xzjd;
        comdata.ValueStr = data;
        return comdata;
    }
    private CarSimple GetCarSimple(string gpsData, carModel car)
    {
        ComData comdata = null;
        var carsimple = new CarSimple();

        comdata = comDataSet(gpsData, car);

        var newPointX = comdata.EastDistance;
        var newPointY = comdata.NorthDistance;
        var newCourse = comdata.Course;

        var dp_Htx = new DoublePoint(newPointX, newPointY);

        carsimple.Dp_Htx = dp_Htx;
        carsimple.CourseAngle = newCourse;
        carsimple.PitchAngle = comdata.Pitch;
        carsimple.RollAngle = comdata.Roll;
        return carsimple;
    }
    public void carMoveA2(string gpsData, string gpsDataA2)
    {
        try
        {
            lock (obj)
            {
                #region A2车头
                var carsimple = GetCarSimple(gpsData, car);
                #endregion
                #region A2??

                var carSimpleA2 = GetCarSimple(gpsDataA2, carA2);
                #endregion
                //绘制

                //将中心点设为车头的中心点
                this.mapControl.Map.Navigator.CenterOn(carsimple.Dp_Htx.X, carsimple.Dp_Htx.Y);
                //if (timeOut)
                //{
                ////绘制图片
                if (AppStaticData.siheyiconfig.SfImg.Value == 1)
                {

                    //drawCarMoveImageA2(carsimple, carSimpleA2); return;
                }
                //绘制A2轨迹
                drawCarMoveA2(carsimple, carSimpleA2);
                //timeOut = false;
                //}
                //else
                //{
                //    ////绘制图片
                //    if (AppStaticData.ycjkConfig.sfImg == "1")
                //    {


                //        drawCarMoveImageA2(carsimple, carSimpleA2); return;
                //    }
                //    //绘制A2轨迹
                //    drawCarMoveA2(carsimple, carSimpleA2);
                //}
            }
        }
        catch (Exception ex)
        {
            //File.AppendAllText("udpgjrz.txt", "carMoveA2" + DateTime.Now + ":" + ex.Message + "\r\n");
            Log.Error(@$"移动车辆数据出错{ex.Message}{ex.StackTrace}");
        }
    }
    public void carMove(string gpsData)
    {
        try
        {
            lock (obj)
            {
                #region 绘制Carsimple

                var carsimple = GetCarSimple(gpsData, car);
                #endregion
                this.mapControl.Map.Navigator.CenterOn(carsimple.Dp_Htx.X, carsimple.Dp_Htx.Y);

                {

                    drawCarMove(carsimple);

                }
            }
        }
        catch (Exception ex)
        {
            Log.Error($"绘制A2轨迹失败:{ex.Message}");
        }
    }
    private object lockdraw = new object();
    private bool isRightKm = false;
    private int gjsbcs;
    private int cjsbcs;

    private List<ILayer> CreateLayer(CarSimple carSimple, string layerName = "")
    {
        lock (lockdraw)
        {
            var carModel = car;
            if (layerName.ToUpper() == "A2CAR")
            {
                carModel = carA2;
            }

            if (string.IsNullOrEmpty(layerName))
            {
                layerName = "Car";
            }

            var layer = mapControl.Map.Layers.FirstOrDefault(d => d.Name == layerName);
            if (layer != null)
            {
                mapControl.Map.Layers.Remove(layer);
            }

            var list = new List<Coordinate>();

            foreach (var c in carModel.Clbdsj)
            {
                if (c.Index <= 24)
                {
                    var pointHelp = new PintHelp();
                    var d = pointHelp.newPointGet(carSimple, carModel, c);
                    list.Add(new Coordinate(d.X, d.Y));
                }
                else if (c.Index == 25)
                {
                    list.Add(list[0]);
                }
            }
            var features = new List<IFeature>();
            #region 绘制复选框
            var cllist = GetClDponits(carSimple, carModel);
            foreach (var item in cllist)
            {
                var mp = new MPoint(item.X, item.Y);
                var feature = new PointFeature(mp);
                feature.Styles.Add(MapsUiHelper.CreateClStyle());
                features.Add(feature);

            }
            #endregion
            var linering = new NetTopologySuite.Geometries.LinearRing(list.ToArray());
            var polygon = new NetTopologySuite.Geometries.Polygon(linering);

            VectorStyle Style = MapsUiHelper.CreateCarStyle();

            var polygonFeature = polygon.ToFeature();

            polygonFeature.Styles.Add(Style);
            features.Add(polygonFeature);
            var polygonLayer = new Layer(layerName);
            polygonLayer.DataSource = new MemoryProvider(features);
            //如果设置这个Layer的样式为Null，则不显示点，而是显示一个外环。
            polygonLayer.Style = null;
            var listLayer = new List<ILayer>();
            listLayer.Add(polygonLayer);
            return listLayer;

        }


    }
    private List<ILayer> CreateLayerByImg(CarSimple carSimple, string layerName = "")
    {
        //先不计算转向角度
        //carSimple.CourseAngle = 0;
        //carSimple.PitchAngle = 0;
        //carSimple.RollAngle = 0;

        lock (lockdraw)
        {
            var carModel = car;
            if (layerName.ToUpper() == "A2CAR")
            {
                carModel = carA2;
            }

            if (string.IsNullOrEmpty(layerName))
            {
                layerName = "Car";
            }

            var layer = mapControl.Map.Layers.FirstOrDefault(d => d.Name == layerName);

            if (layer != null)
            {
                mapControl.Map.Layers.Remove(layer);
            }

            var list = new List<Coordinate>();

            foreach (var c in carModel.Clbdsj)
            {
                if (c.Index <= 24)
                {
                    var pointHelp = new PintHelp();
                    var d = pointHelp.newPointGet(carSimple, carModel, c);
                    list.Add(new Coordinate(d.X, d.Y));
                }
                else if (c.Index == 25)
                {
                    list.Add(list[0]);
                }
            }
            var features = new List<IFeature>();
            #region 绘制复选框
            var cllist = GetClDponits(carSimple, carModel);
            foreach (var item in cllist)
            {
                var mp = new MPoint(item.X, item.Y);
                var feature = new PointFeature(mp);
                feature.Styles.Add(MapsUiHelper.CreateClStyle());
                features.Add(feature);
            }
            #endregion
            var linering = new NetTopologySuite.Geometries.LinearRing(list.ToArray());
            var polygon = new NetTopologySuite.Geometries.Polygon(linering);

            #region 绘制图片层
            //var imageLayer = ImageLayerHelper.CreateImageLayer(layerName, polygon, (float)carSimple.CourseAngle, bitmapId);
            IFeature imagerFeature;
            if (layerName.ToUpper() == "A2CAR")
            {
                imagerFeature = ImageLayerHelper.CreateImageFeature(polygon, (float)carSimple.CourseAngle, A2bitmapId);
            }
            else
                imagerFeature = ImageLayerHelper.CreateImageFeature(polygon, (float)carSimple.CourseAngle, bitmapId);
            features.Add(imagerFeature);
            #endregion
            #region 绘制轮廓
            if (AppStaticData.siheyiconfig.IsDebug.Value)
            {
                VectorStyle Style = MapsUiHelper.CreateCarStyle();
                var polygonFeature = polygon.ToFeature();
                polygonFeature.Styles.Add(Style);
                features.Add(polygonFeature);
            }

            #endregion
            var polygonLayer = new Layer(layerName);
            polygonLayer.DataSource = new MemoryProvider(features);
            //如果设置这个Layer的样式为Null，则不显示点，而是显示一个外环。
            polygonLayer.Style = null;
            var listLayer = new List<ILayer>();

            listLayer.Add(polygonLayer);
            //listLayer.Add(imageLayer);

            return listLayer;

        }


    }

    public void carMoveImg(Polygon polygon)
    {
        // 1. 目标坐标范围

        // 2. 获取图片
        var imagePath = "your_image.png";
        var imageBytes = File.ReadAllBytes(imagePath);

        // 3. 创建 Feature
        //var feature = new Feature
        //{
        //    Geometry = new Point((minLon + maxLon) / 2, (minLat + maxLat) / 2)
        //};

        // 4. 创建自定义 Style
        //feature.Styles.Add(new StretchBitmapStyle
        //{
        //    ImageBytes = imageBytes,
        //    poly = bbox,
        //    Rotation = 0 // 旋转角度
        //});
        var style = new StretchBitmapStyle { ImageBytes = imageBytes, Myolygon = polygon, Rotation = 0 };
        // 5. 创建 MemoryLayer
        var layer = new MemoryLayer
        {
            Name = "StretchedImageLayer",

        };

    }
    private void drawClear()

    {
        //使用Mapsui清除重叠


    }
    /// <summary>
    /// 绘制车头
    /// </summary>
    /// <param name="carsimple"></param>
    private void drawCarMove(CarSimple carsimple)
    {
        var layerList = new List<ILayer>();
        if (AppStaticData.siheyiconfig.SfImg.Value == 1)
        {
            //绘制图片
            layerList = CreateLayerByImg(carsimple);
        }
        else
            layerList = CreateLayer(carsimple);

        foreach (var item in layerList)
        {
            mapControl.Map.Layers.Add(item);
        }

    }

    public void drawCarMoveA2(CarSimple carSimple, CarSimple carSimple2)
    {
        try
        {
            if (AppStaticData.siheyiconfig.SfImg.Value == 1)
            {
                //绘制车头
                var layers = CreateLayerByImg(carSimple);
                //绘制a2
                var layersA2 = CreateLayerByImg(carSimple2, "A2Car");
                var layersTemp = layers.Concat(layersA2);
                foreach (var layer in layersTemp)
                {
                    mapControl.Map.Layers.Add(layer);
                }
            }
            else
            {     //绘制车头
                var layers = CreateLayer(carSimple);
                //绘制a2
                var layersA2 = CreateLayer(carSimple2, "A2Car");
                var layersTemp = layers.Concat(layersA2);
                foreach (var layer in layersTemp)
                {
                    mapControl.Map.Layers.Add(layer);
                }
            }



        }
        catch (Exception ex)
        {
            //File.AppendAllText("udpgjrz.txt", "drawCarMove" + DateTime.Now + ":" + ex.Message + "\r\n");
            Log.Error($"绘制A2轨迹失败:{ex.Message}");
        }
    }
    private List<Coordinate> GetClDponits(CarSimple carSimple, carModel car)
    {
        var dp = new List<Coordinate>();
        //var dpdcj1 = new DoublePoint[4];
        //var dpdcj2 = new DoublePoint[4];
        foreach (var c in car.Clbdsj)
            if (c.Index > 24)
            {
                var pointHelp = new PintHelp();
                var d = pointHelp.newPointGet(carSimple, car, c);
                dp.Add(new Coordinate(d.X, d.Y));
            }
        return dp;
    }
    private void StartUpload()
    {

        //{ 
        //    if (AppStaticData.ycjkConfig.sf6816M == "1")
        //    {
        //        Task.Factory.StartNew(GetKscjPic6816M);
        //    }
        //    else
        //    {
        //        Task.Factory.StartNew(GetKscjPic);
        //    }
        //}



        //{
        //    if (AppStaticData.ycjkConfig.sf6816M == "1")
        //    {
        //        Task.Factory.StartNew(GetGjPicWhile6816M);
        //    }
        //    else
        //    {
        //        Task.Factory.StartNew(GetGjPicWhile); //只启动一个线程
        //    }
        //}
        //CancellationToken cts=new CancellationToken();
        //Task.Factory.StartNew(GetGjPicWhile6816M, TaskCreationOptions.LongRunning);   //logRunning 长时间运行线程不会停
        Task.Factory.StartNew(async () => await GetGjPicWhile6816MAsync(), _cts.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);
        Task.Factory.StartNew(async () => await GetKscjPic6816MAsync(), _cts.Token, TaskCreationOptions.LongRunning, TaskScheduler.Default);


    }
    private static readonly string fileDic = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GjPic");
    public int uploadhandl = -1;
    public int gjUploadHandl = -1;
    public async Task GetGjPicWhile6816MAsync()
    {

        await Task.Delay(5000); // �ȴ�5��
        //Task.Delay(5000).GetAwaiter().GetResult();
        while (!_cts.Token.IsCancellationRequested)
        {
            var sxsj = 2000;
            try
            {
                sxsj = AppStaticData.siheyiconfig.Gjsxsj.Value;
            }
            catch (Exception ex)
            {
                sxsj = 2000;
            }
            if (model==null)
            {
                
                continue;
            }
            var picWinIndex = model.picWinIndex;
            var picWin = model.picWin;
            var winCount = model.winCount;
            //当前的科目不正确，暂时不能上传图片
            if (!isRightKm)
            {
                continue;
            }
            //await Task.Delay(1000);
            //await Dispatcher.UIThread.InvokeAsync(() => ImageHelper.SavePanelAsImage(this.Pgj));
            try
            {
                await Task.Delay(sxsj);
                var picno = (uint)(picWinIndex * 2 + 2);

                var m_dwWallNo = uint.Parse(picWin.Split('_')[0]);
                var m_dwRes = uint.Parse(picWin.Split('_')[1]);
                //轨迹图片默认为窗口序列中的2窗口
                uint m_dwWinNo = (uint)(winCount + 2);
                //窗口号(组合)：1字节设备号+1字节保留号+2字节窗口号
                var dwWinNum = ((m_dwWallNo & 0xff) << 24) + ((m_dwRes & 0xff) << 16) + (m_dwWinNo & 0xff);
                var rd = new Random();
                var temp = rd.Next(1, 100);
                var guid = DateTime.Now.ToString("fff") + temp;
                //var guid = DateTime.Now.ToString("fff");

                var fileName = dwWinNum + "_" + picno + "_" + guid + ".jpg";

                var filePath = Path.Combine(fileDic ,fileName);                
                var GetResult = true;

                GetResult = await Dispatcher.UIThread.InvokeAsync(async () =>
                {
                    // ���ȳ���������
                    var result = await ImageHelper.SavePanelAsImage3Async(this.PanelCar1, filePath);
                    if (!result)
                    {
                        // ���������ʧ�ܣ����Ա��÷���
                        Log.Warning("��ͼ�񱣴淽��ʧ�ܣ����Ա��÷���");
                        result = await ImageHelper.SavePanelAsImageSimpleAsync(this.PanelCar1, filePath);
                    }
                    return result;
                });
                //使用其他的PanelCar绘制图，没有什么内容，只有空白背景，所以不建议原因
                //Dispatcher.UIThread.Invoke(() => ImageHelper.SavePanelAsImage3(this.PanelCar));
                if (!GetResult) { gjsbcs++; continue; }
                var upResult = HeMaQiHelper.upLoadPic6816M(AppStaticData.HmqUserId, picno, fileName, filePath, dwWinNum, gjUploadHandl);
                if (upResult)
                {
                    try
                    {
                        gjsbcs = 0;
                        File.Delete(filePath);
                    }
                    catch (Exception ex)
                    {
                        //LogHelper.ErrorLog("上传轨迹文件失败", ex);
                        Log.Error($"上传轨迹文件失败:{ex.Message}");
                        //throw;
                    }
                }
                else
                {
                    GetGjUploadHandel();
                    gjsbcs++;
                }

                //});
            }
            catch (Exception ex)
            {
                gjsbcs++;
                //LogHelper.ErrorLog("�ϴ��켣�ļ�ʧ��", ex);
                Log.Error($"�ϴ��켣�ļ�ʧ��:{ex.Message}");
            }
            finally
            {
                SetGjscsbInfo();
                //islock = false;
            }
        }
    }
    public async Task GetKscjPic6816MAsync()
    {
        await Task.Delay(5000); // ��ʱ5�룬�ϴ��߳�
        while (!_cts.Token.IsCancellationRequested)
        {

            //var winCount = GetWinCount();
            // ���ط������ļ�û��������Ӧ��ֵʱ���ᵼ���߳�һֱˢ�£�ʹ��Ĭ��ֵ
            if (AppStaticData.siheyiconfig.Xytpxssj.Value >= 200)
            {
                await Task.Delay(AppStaticData.siheyiconfig.Xytpxssj.Value);
            }
            else
            {
                await Task.Delay(2000);
            }
            // ��鿼��
            if (!isRightKm)
            {
                continue;
            }
            if (model == null)
            {

                continue;
            }
            var picWinIndex = model.picWinIndex;
            var picWin = model.picWin;
            var winCount = model.winCount;
            //lock (cjlock)
            {
                try
                {

                    //var filePath = Application.StartupPath + "\\" + "GjPic" + "\\";

                    //if (!Directory.Exists(filePath)) Directory.CreateDirectory(filePath);

                    //采集图片默认为窗口序列中的2窗口
                    var picno = (uint)(picWinIndex * 2 + 1);
                    var m_dwWallNo = uint.Parse(picWin.Split('_')[0]);
                    var m_dwRes = uint.Parse(picWin.Split('_')[1]);
                    //采集图片默认为窗口序列中的2窗口
                    uint m_dwWinNo = (uint)(winCount + 1);
                    //窗口号(组合)：1字节设备号+1字节保留号+2字节窗口号
                    var dwWinNum = ((m_dwWallNo & 0xff) << 24) + ((m_dwRes & 0xff) << 16) + (m_dwWinNo & 0xff);
                    var rd = new Random();
                    var temp = rd.Next(1, 100);
                    var guid = DateTime.Now.ToString("fff") + temp;
                    var fileName = dwWinNum + "_" + picno + "_" + guid + ".jpg";
                   var   filePath = Path.Combine(fileDic,fileName);
                    var GetResult = true;                    
                    GetResult = await Dispatcher.UIThread.InvokeAsync(async () =>
                    {
                        // ���ȳ���������
                        var result = await ImageHelper.SavePanelAsImage3Async(this.Panelxy1, filePath);
                        if (!result)
                        {
                            // ���������ʧ�ܣ����Ա��÷���
                            Log.Warning("��ͼ�񱣴淽��ʧ�ܣ����Ա��÷���");
                            result = await ImageHelper.SavePanelAsImageSimpleAsync(this.Panelxy1, filePath);
                        }
                        return result;
                    });
                    if (!GetResult) { cjsbcs++; continue; }
                    try
                    {

                        var upResult = HeMaQiHelper.upLoadPic6816M(AppStaticData.HmqUserId, picno, fileName,
                            filePath, dwWinNum, uploadhandl);
                        if (!upResult)
                        {
                            cjsbcs++;
                            GetUploadHandel();
                            continue;

                        }
                        cjsbcs = 0;
                        File.Delete(filePath);

                    }
                    catch (Exception ex)
                    {
                        cjsbcs++;
                        //LogHelper.ErrorLog("生成采集图片失败", ex);
                        Log.Error($"生成采集图片失败:{ex.Message}");
                    }


                }
                catch (Exception ex)
                {
                    //LogHelper.ErrorLog("上传删除文件失败", ex);
                    Log.Error($"上传删除文件失败:{ex.Message}");
                    cjsbcs++;

                }
                finally
                {
                    SetCjscsbInfo();
                }
            }




        }
    }

    private void GetUploadHandel()
    {
        //throw new NotImplementedException();
    }

    /// <summary>
    /// 设置轨迹上传失败信息
    /// </summary>
    private void SetGjscsbInfo()
    {
        if (gjsbcs > 20)
        {
            //FileTools.WriteLog("uploaderror.txt", $"{cardata.GetValueStr("cphm")}连续20次上传轨迹图片失败{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
            Log.Error( $"{model.CarPlateAndNo}连续20次上传轨迹图片失败{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
            AppManager.AddKeepLiveData("5", $"{model.CarPlateAndNo}连续20次上传轨迹图片失败");
            gjsbcs = 0;
        }
    }
    /// <summary>
    /// 设置图片上传失败信息
    /// </summary>
    private void SetCjscsbInfo()
    {
        if (cjsbcs > 20)
        {

            Log.Error( $"{model.CarPlateAndNo}连续20次上传学员信息片失败{DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss")}");
            AppManager.AddKeepLiveData("5", $"{model.CarPlateAndNo}连续20次上传学员信息片失败");
            cjsbcs = 0;
        }
    }

    private void GetGjUploadHandel()
    {
        //throw new NotImplementedException();
    }
}