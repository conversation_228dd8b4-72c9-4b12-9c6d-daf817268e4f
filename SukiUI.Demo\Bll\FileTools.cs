﻿using Avalonia.Controls;
using Avalonia.Media.Imaging;
using SkiaSharp;
using System;
using System.IO;
using System.Threading.Tasks;

namespace SukiUI.Demo.Bll
{
    public class FileTools
    {


        public static Bitmap? Base64ToBitmapWithSkia(string base64String)
        {
            if (string.IsNullOrEmpty(base64String)) return null;
            try
            {
                byte[] bytes = Convert.FromBase64String(base64String);

                using (var skImage = SKImage.FromEncodedData(bytes))
                using (var skData = skImage.Encode())
                using (var stream = new MemoryStream(skData.ToArray()))
                {
                    return new Bitmap(stream);
                }
            }
            catch
            {
                return null;
            }

        }
        /// <summary>
        /// base64str转为buffer,空为null
        /// </summary>
        /// <param name="str"></param>
        /// <returns></returns>
        public static byte[] ConvertFromBase64Str(string str)
        {
            if (string.IsNullOrEmpty(str)) return null;
            return Convert.FromBase64String(str);
        }
    }
}
