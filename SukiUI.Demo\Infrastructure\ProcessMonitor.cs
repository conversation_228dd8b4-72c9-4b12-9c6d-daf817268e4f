using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Threading;
using System.Threading.Tasks;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// 进程监控器 - 监控进程状态和资源使用情况
    /// </summary>
    public static class ProcessMonitor
    {
        private static Timer? _monitorTimer;
        private static Process? _currentProcess;
        private static bool _isMonitoring = false;
        private static readonly object _lockObject = new object();
        private static readonly Queue<int> _threadCountHistory = new Queue<int>();
        private static readonly int _maxHistorySize = 10;

        /// <summary>
        /// 开始监控当前进程
        /// </summary>
        public static void StartMonitoring()
        {
            lock (_lockObject)
            {
                if (_isMonitoring) return;

                try
                {
                    _currentProcess = Process.GetCurrentProcess();
                    
                    // 记录进程启动信息
                    LogProcessStartInfo();

                    // 每30秒记录一次进程状态
                    _monitorTimer = new Timer(MonitorCallback, null, TimeSpan.FromSeconds(30), TimeSpan.FromSeconds(30));
                    
                    _isMonitoring = true;
                    CrashLogger.LogApplicationException(null, "Process monitoring started");
                }
                catch (Exception ex)
                {
                    CrashLogger.LogApplicationException(ex, "Failed to start process monitoring");
                }
            }
        }

        /// <summary>
        /// 停止监控
        /// </summary>
        public static void StopMonitoring()
        {
            lock (_lockObject)
            {
                if (!_isMonitoring) return;

                try
                {
                    _monitorTimer?.Dispose();
                    _monitorTimer = null;
                    _isMonitoring = false;
                    
                    CrashLogger.LogApplicationException(null, "Process monitoring stopped");
                }
                catch (Exception ex)
                {
                    CrashLogger.LogApplicationException(ex, "Error stopping process monitoring");
                }
            }
        }

        /// <summary>
        /// 记录进程启动信息
        /// </summary>
        private static void LogProcessStartInfo()
        {
            try
            {
                if (_currentProcess == null) return;

                var startInfo = new System.Text.StringBuilder();
                startInfo.AppendLine("=== Process Start Information ===");
                startInfo.AppendLine($"Process ID: {_currentProcess.Id}");
                startInfo.AppendLine($"Process Name: {_currentProcess.ProcessName}");
                startInfo.AppendLine($"Start Time: {_currentProcess.StartTime:yyyy-MM-dd HH:mm:ss.fff}");
                startInfo.AppendLine($"Main Module: {_currentProcess.MainModule?.FileName ?? "Unknown"}");
                startInfo.AppendLine($"Working Directory: {Environment.CurrentDirectory}");
                startInfo.AppendLine($"Command Line: {Environment.CommandLine}");
                startInfo.AppendLine($"User Interactive: {Environment.UserInteractive}");
                startInfo.AppendLine($"OS Version: {Environment.OSVersion}");
                startInfo.AppendLine($"CLR Version: {Environment.Version}");
                startInfo.AppendLine($"Processor Count: {Environment.ProcessorCount}");
                startInfo.AppendLine($"System Page Size: {Environment.SystemPageSize}");
                startInfo.AppendLine($"Machine Name: {Environment.MachineName}");
                startInfo.AppendLine($"User Name: {Environment.UserName}");

                // 添加环境变量信息
                startInfo.AppendLine("=== Key Environment Variables ===");
                var keyVars = new[] { "DISPLAY", "XDG_SESSION_TYPE", "DESKTOP_SESSION", "WAYLAND_DISPLAY", "PATH" };
                foreach (var varName in keyVars)
                {
                    var value = Environment.GetEnvironmentVariable(varName);
                    if (!string.IsNullOrEmpty(value))
                    {
                        startInfo.AppendLine($"{varName}: {value}");
                    }
                }

                CrashLogger.LogApplicationException(null, startInfo.ToString());
            }
            catch (Exception ex)
            {
                CrashLogger.LogApplicationException(ex, "Error logging process start info");
            }
        }

        /// <summary>
        /// 监控回调
        /// </summary>
        private static void MonitorCallback(object? state)
        {
            try
            {
                if (_currentProcess == null || _currentProcess.HasExited)
                {
                    CrashLogger.LogApplicationException(null, "Process has exited during monitoring");
                    StopMonitoring();
                    return;
                }

                // 刷新进程信息
                _currentProcess.Refresh();

                var status = new System.Text.StringBuilder();
                status.AppendLine("=== Process Status ===");
                status.AppendLine($"Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                status.AppendLine($"Process ID: {_currentProcess.Id}");
                status.AppendLine($"Threads: {_currentProcess.Threads.Count}");
                status.AppendLine($"Handles: {_currentProcess.HandleCount}");
                
                // 内存信息
                status.AppendLine($"Working Set: {_currentProcess.WorkingSet64 / 1024 / 1024} MB");
                status.AppendLine($"Private Memory: {_currentProcess.PrivateMemorySize64 / 1024 / 1024} MB");
                status.AppendLine($"Virtual Memory: {_currentProcess.VirtualMemorySize64 / 1024 / 1024} MB");
                status.AppendLine($"Paged Memory: {_currentProcess.PagedMemorySize64 / 1024 / 1024} MB");
                status.AppendLine($"Non-Paged Memory: {_currentProcess.NonpagedSystemMemorySize64 / 1024} KB");

                // CPU时间
                status.AppendLine($"Total CPU Time: {_currentProcess.TotalProcessorTime}");
                status.AppendLine($"User CPU Time: {_currentProcess.UserProcessorTime}");
                status.AppendLine($"Privileged CPU Time: {_currentProcess.PrivilegedProcessorTime}");

                // GC信息
                status.AppendLine("=== Garbage Collection ===");
                for (int i = 0; i <= GC.MaxGeneration; i++)
                {
                    status.AppendLine($"Gen {i} Collections: {GC.CollectionCount(i)}");
                }
                status.AppendLine($"Total Memory: {GC.GetTotalMemory(false) / 1024 / 1024} MB");

                // 线程信息
                status.AppendLine("=== Thread Information ===");
                status.AppendLine($"Current Thread ID: {Thread.CurrentThread.ManagedThreadId}");
                status.AppendLine($"Thread Pool Threads: {ThreadPool.ThreadCount}");
                status.AppendLine($"Completed Work Items: {ThreadPool.CompletedWorkItemCount}");

                CrashLogger.LogApplicationException(null, status.ToString());

                // 检查内存使用是否过高
                var workingSetMB = _currentProcess.WorkingSet64 / 1024 / 1024;
                if (workingSetMB > 1000) // 超过1GB
                {
                    CrashLogger.LogApplicationException(null, $"WARNING: High memory usage detected: {workingSetMB} MB");
                }

                // 跟踪线程数量历史
                var currentThreadCount = _currentProcess.Threads.Count;
                _threadCountHistory.Enqueue(currentThreadCount);
                if (_threadCountHistory.Count > _maxHistorySize)
                {
                    _threadCountHistory.Dequeue();
                }

                // 检查线程数是否过多 (调整阈值，Avalonia应用通常需要更多线程)
                if (currentThreadCount > 150)
                {
                    var threadDetails = GetThreadDetails();
                    var trendAnalysis = AnalyzeThreadTrend();
                    CrashLogger.LogApplicationException(null, $"WARNING: High thread count detected: {currentThreadCount}\n{threadDetails}\n{trendAnalysis}");
                }
                else if (currentThreadCount > 100)
                {
                    // 记录信息级别的线程数量，不作为警告
                    CrashLogger.LogTestEvent("ThreadCount", $"Thread count is elevated but within acceptable range: {currentThreadCount}");
                }
            }
            catch (Exception ex)
            {
                CrashLogger.LogApplicationException(ex, "Error in process monitoring callback");
            }
        }

        /// <summary>
        /// 记录当前进程快照
        /// </summary>
        public static void LogCurrentSnapshot(string reason)
        {
            try
            {
                if (_currentProcess == null) return;

                _currentProcess.Refresh();
                
                var snapshot = new System.Text.StringBuilder();
                snapshot.AppendLine($"=== Process Snapshot - {reason} ===");
                snapshot.AppendLine($"Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}");
                snapshot.AppendLine($"Process ID: {_currentProcess.Id}");
                snapshot.AppendLine($"Has Exited: {_currentProcess.HasExited}");
                
                if (!_currentProcess.HasExited)
                {
                    snapshot.AppendLine($"Working Set: {_currentProcess.WorkingSet64 / 1024 / 1024} MB");
                    snapshot.AppendLine($"Threads: {_currentProcess.Threads.Count}");
                    snapshot.AppendLine($"Handles: {_currentProcess.HandleCount}");
                }

                CrashLogger.LogApplicationException(null, snapshot.ToString());
            }
            catch (Exception ex)
            {
                CrashLogger.LogApplicationException(ex, $"Error logging process snapshot for: {reason}");
            }
        }

        /// <summary>
        /// 检查进程是否仍在运行
        /// </summary>
        public static bool IsProcessRunning()
        {
            try
            {
                return _currentProcess != null && !_currentProcess.HasExited;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// 获取线程详细信息
        /// </summary>
        private static string GetThreadDetails()
        {
            try
            {
                var details = new System.Text.StringBuilder();
                details.AppendLine("=== Thread Analysis ===");

                // 线程池信息
                details.AppendLine($"ThreadPool.ThreadCount: {ThreadPool.ThreadCount}");
                details.AppendLine($"ThreadPool.CompletedWorkItemCount: {ThreadPool.CompletedWorkItemCount}");
                details.AppendLine($"ThreadPool.PendingWorkItemCount: {ThreadPool.PendingWorkItemCount}");

                // 获取线程池的工作线程和I/O线程数
                ThreadPool.GetMaxThreads(out int maxWorkerThreads, out int maxIoThreads);
                ThreadPool.GetAvailableThreads(out int availableWorkerThreads, out int availableIoThreads);

                details.AppendLine($"Max Worker Threads: {maxWorkerThreads}");
                details.AppendLine($"Available Worker Threads: {availableWorkerThreads}");
                details.AppendLine($"Active Worker Threads: {maxWorkerThreads - availableWorkerThreads}");

                details.AppendLine($"Max I/O Threads: {maxIoThreads}");
                details.AppendLine($"Available I/O Threads: {availableIoThreads}");
                details.AppendLine($"Active I/O Threads: {maxIoThreads - availableIoThreads}");

                // 进程线程信息
                details.AppendLine($"Process Threads Count: {_currentProcess.Threads.Count}");

                // 按状态分组统计线程
                var threadStates = new Dictionary<System.Diagnostics.ThreadState, int>();
                foreach (ProcessThread thread in _currentProcess.Threads)
                {
                    if (threadStates.ContainsKey(thread.ThreadState))
                        threadStates[thread.ThreadState]++;
                    else
                        threadStates[thread.ThreadState] = 1;
                }

                details.AppendLine("Thread States:");
                foreach (var state in threadStates)
                {
                    details.AppendLine($"  {state.Key}: {state.Value}");
                }

                return details.ToString();
            }
            catch (Exception ex)
            {
                return $"Error getting thread details: {ex.Message}";
            }
        }

        /// <summary>
        /// 分析线程数量趋势
        /// </summary>
        private static string AnalyzeThreadTrend()
        {
            try
            {
                if (_threadCountHistory.Count < 3)
                    return "=== Thread Trend Analysis ===\nInsufficient data for trend analysis";

                var history = _threadCountHistory.ToArray();
                var trend = new System.Text.StringBuilder();
                trend.AppendLine("=== Thread Trend Analysis ===");

                // 显示历史数据
                trend.AppendLine($"Thread count history (last {history.Length} samples):");
                for (int i = 0; i < history.Length; i++)
                {
                    trend.AppendLine($"  Sample {i + 1}: {history[i]} threads");
                }

                // 计算趋势
                var first = history[0];
                var last = history[history.Length - 1];
                var change = last - first;
                var changePercent = first > 0 ? (change * 100.0 / first) : 0;

                trend.AppendLine($"Trend: {change:+0;-0;0} threads ({changePercent:+0.1;-0.1;0.0}%)");

                // 检测是否有持续增长
                var increasingCount = 0;
                for (int i = 1; i < history.Length; i++)
                {
                    if (history[i] > history[i - 1])
                        increasingCount++;
                }

                if (increasingCount >= history.Length - 1)
                {
                    trend.AppendLine("⚠️  WARNING: Thread count is consistently increasing - possible thread leak!");
                }
                else if (increasingCount > history.Length / 2)
                {
                    trend.AppendLine("⚠️  CAUTION: Thread count is mostly increasing - monitor for potential leak");
                }
                else
                {
                    trend.AppendLine("✅ Thread count appears stable");
                }

                return trend.ToString();
            }
            catch (Exception ex)
            {
                return $"Error analyzing thread trend: {ex.Message}";
            }
        }
    }
}
