﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:SukiUI.Controls"
                    xmlns:converters="clr-namespace:SukiUI.Converters">

    <Design.PreviewWith>
        <StackPanel Width="300"
                    Height="400"
                    Margin="0,20,0,0"
                    Spacing="20">
            <controls:InfoBadge Appearance="Information"
                                CornerPosition="TopRight"
                                Header="Info">

                <Button Content="Log In" />
            </controls:InfoBadge>
            <controls:InfoBadge Appearance="Warning"
                                CornerPosition="TopLeft"
                                Header="Warning">
                <Button />
            </controls:InfoBadge>
            <controls:InfoBadge Appearance="Success"
                                CornerPosition="BottomRight"
                                Header="Success">
                <Button />
            </controls:InfoBadge>
            <controls:InfoBadge Appearance="Error"
                                CornerPosition="BottomLeft"
                                Header="Error">
                <Button />
            </controls:InfoBadge>
            <controls:InfoBadge Appearance="Error"
                                CornerPosition="TopRight"
                                IsDot="True">
                <Button />
            </controls:InfoBadge>
            <controls:InfoBadge Appearance="Information"
                                CornerPosition="TopRight"
                                Header="100"
                                Overflow="99">
                <Button />
            </controls:InfoBadge>
        </StackPanel>
    </Design.PreviewWith>

    <converters:InfoBadgeOverflowConverter x:Key="InfoBadgeContentConverter" />

    <ControlTheme x:Key="{x:Type controls:InfoBadge}" TargetType="{x:Type controls:InfoBadge}">
        <Setter Property="CornerRadius" Value="{DynamicResource MediumCornerRadius}" />
        <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="UseLayoutRounding" Value="False" />
        <Setter Property="Template">
            <ControlTemplate TargetType="{x:Type controls:InfoBadge}">
                <Grid HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                      VerticalAlignment="{TemplateBinding VerticalAlignment}"
                      ClipToBounds="False">
                    <ContentPresenter Name="PART_ContentPresenter"
                                      HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}" />
                    <Border Name="BadgeBorder"
                            MinWidth="25"
                            MinHeight="25"
                            Padding="5,2,5,2"
                            HorizontalAlignment="Right"
                            VerticalAlignment="Top"
                            Background="{TemplateBinding Background}"
                            CornerRadius="{TemplateBinding CornerRadius}"
                            IsVisible="{Binding !!Header, RelativeSource={RelativeSource TemplatedParent}}"
                            UseLayoutRounding="False">
                        <ContentPresenter Name="PART_HeaderPresenter"
                                          Margin="2"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Focusable="False"
                                          FontWeight="{DynamicResource DefaultDemiBold}"
                                          Foreground="White"
                                          IsTabStop="False">
                            <ContentPresenter.Styles>
                                <Style Selector="TextBlock">
                                    <Setter Property="FontSize" Value="13" />
                                </Style>
                            </ContentPresenter.Styles>
                            <ContentPresenter.Content>
                                <MultiBinding Converter="{StaticResource InfoBadgeContentConverter}">
                                    <Binding Path="Header" RelativeSource="{RelativeSource TemplatedParent}" />
                                    <Binding Path="Overflow" RelativeSource="{RelativeSource TemplatedParent}" />
                                </MultiBinding>
                            </ContentPresenter.Content>
                        </ContentPresenter>
                    </Border>
                </Grid>
            </ControlTemplate>
        </Setter>

        <Style Selector="^[IsDot=True]">
            <Setter Property="Template">
                <ControlTemplate TargetType="{x:Type controls:InfoBadge}">
                    <Grid HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                          VerticalAlignment="{TemplateBinding VerticalAlignment}"
                          ClipToBounds="False">
                        <ContentPresenter Name="PART_ContentPresenter"
                                          Margin="-1"
                                          HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalAlignment}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}" />
                        <Border Name="BadgeBorder"
                                Width="14"
                                Height="14"
                                HorizontalAlignment="Right"
                                VerticalAlignment="Top"
                                Background="{TemplateBinding Background}"
                                CornerRadius="{TemplateBinding CornerRadius}"
                                IsVisible="{Binding !!Header, RelativeSource={RelativeSource TemplatedParent}}" />
                    </Grid>
                </ControlTemplate>
            </Setter>
        </Style>

        <Style Selector="^[CornerPosition=TopLeft] /template/ Border#BadgeBorder">
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="VerticalAlignment" Value="Top" />
        </Style>
        <Style Selector="^[CornerPosition=TopRight] /template/ Border#BadgeBorder">
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Setter Property="VerticalAlignment" Value="Top" />
        </Style>
        <Style Selector="^[CornerPosition=BottomLeft] /template/ Border#BadgeBorder">
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="VerticalAlignment" Value="Bottom" />
        </Style>
        <Style Selector="^[CornerPosition=BottomRight] /template/ Border#BadgeBorder">
            <Setter Property="HorizontalAlignment" Value="Right" />
            <Setter Property="VerticalAlignment" Value="Bottom" />
        </Style>
    </ControlTheme>
</ResourceDictionary>