<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI"
                    xmlns:converters="clr-namespace:Dock.Avalonia.Converters"
                    xmlns:core="using:Dock.Model.Core">
    <Design.PreviewWith>
        <ToolDockControl Width="300" Height="300" />
    </Design.PreviewWith>

    <ControlTheme x:Key="{x:Type ToolDockControl}" TargetType="ToolDockControl">

        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border Margin="8"
                            BoxShadow="{DynamicResource SukiPopupShadow}"
                            CornerRadius="20"
                            IsVisible="{TemplateBinding Background,
                                                        Converter={x:Static converters:TransparentToTrueConverter.Instance}}" />
                    <suki:GlassCard Margin="5" Padding="0">
                        <Panel>

                            <Panel IsVisible="{TemplateBinding Background, Converter={x:Static converters:TransparentToTrueConverter.Instance}}">
                                <Panel Background="{DynamicResource SukiCardBackground}" />
                                <Panel Margin="0">
                                    <Panel.Background>
                                        <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
                                            <GradientStop Offset="0" Color="{DynamicResource SukiAccentColor5}" />
                                            <GradientStop Offset="1" Color="{DynamicResource SukiPrimaryColor10}" />
                                        </LinearGradientBrush>
                                    </Panel.Background>
                                </Panel>
                            </Panel>

                            <DockableControl Name="PART_DockableControl"
                                             Margin="12,15"
                                             x:DataType="core:IDock"
                                             Background="Transparent"
                                             TrackingMode="Visible">
                                <ToolChromeControl x:DataType="core:IDock"
                                                   IsActive="{Binding IsActive}"
                                                   IsVisible="{Binding !!VisibleDockables.Count}">
                                    <ToolControl />
                                </ToolChromeControl>
                            </DockableControl>
                        </Panel>
                    </suki:GlassCard>
                </Panel>
            </ControlTemplate>
        </Setter>
        <!--
    <Style Selector="^[(ProportionalStackPanelSplitter.IsEmpty)=False] /template/ DockableControl#PART_DockableControl">
      <Setter Property="Background" Value="Blue" />
    </Style>

    <Style Selector="^[(ProportionalStackPanelSplitter.IsEmpty)=True] /template/ DockableControl#PART_DockableControl">
      <Setter Property="Background" Value="Red" />
    </Style>
        -->
    </ControlTheme>

</ResourceDictionary>
