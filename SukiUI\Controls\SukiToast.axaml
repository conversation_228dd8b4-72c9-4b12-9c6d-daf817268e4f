<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:content="clr-namespace:SukiUI.Content"
                    xmlns:suki="clr-namespace:SukiUI.Controls">
    <Design.PreviewWith>
        <suki:SukiToast Title="Title" Content="Message" />
    </Design.PreviewWith>
    <ControlTheme x:Key="SukiToastTheme" TargetType="suki:SukiToast">
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Border Name="PART_ToastCard"
                            MinWidth="300" MaxWidth="400"
                            Margin="40,5,30,10"
                            Padding="0"
                            Background="{DynamicResource SukiCardBackground}"
                            BorderBrush="{DynamicResource SukiBorderBrush}"
                            BorderThickness="1"
                            BoxShadow="{DynamicResource SukiPopupShadow}"
                            CornerRadius="15">
                        <Border ClipToBounds="True" CornerRadius="15">
                            <Panel>
                                <Panel IsVisible="{DynamicResource IsDark}">
                                    <Panel Margin="0">
                                        <Panel.Background>
                                            <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
                                                <GradientStop Offset="0" Color="{DynamicResource SukiPrimaryColor5}" />
                                                <GradientStop Offset="1" Color="{DynamicResource SukiPrimaryColor10}" />
                                            </LinearGradientBrush>
                                        </Panel.Background>
                                    </Panel>
                                </Panel>
                                <Panel IsVisible="{DynamicResource IsLight}">
                                    <Panel Margin="0">
                                        <Panel.Background>
                                            <LinearGradientBrush StartPoint="0%,0%" EndPoint="100%,100%">
                                                <GradientStop Offset="0" Color="{DynamicResource SukiAccentColor1}" />
                                                <GradientStop Offset="1" Color="{DynamicResource SukiPrimaryColor1}" />
                                            </LinearGradientBrush>
                                        </Panel.Background>
                                    </Panel>
                                </Panel>
                                <Panel Margin="20,22,20,8">
                                    <DockPanel Margin="0,-7,0,-4" LastChildFill="True">
                                        <StackPanel DockPanel.Dock="Top" Orientation="Horizontal">
                                            <ContentPresenter Margin="12,0,0,0"
                                                              Content="{TemplateBinding Title}"
                                                              FontSize="17"
                                                              FontWeight="{DynamicResource DefaultDemiBold}"
                                                              Foreground="{DynamicResource SukiText}" >
                                                <ContentPresenter.Styles>
                                                    <Style Selector="TextBlock">
                                                        <Setter Property="FontSize" Value="16" />
                                                    </Style>
                                                </ContentPresenter.Styles>
                                            </ContentPresenter>
                                        </StackPanel>
                                        <ItemsControl DockPanel.Dock="Bottom" 
                                                      ItemsSource="{TemplateBinding ActionButtons}" 
                                                      Margin="0,10,0,0"
                                                      HorizontalAlignment="Right">
                                            <ItemsControl.ItemsPanel>
                                                <ItemsPanelTemplate>
                                                    <StackPanel Orientation="Horizontal"/>
                                                </ItemsPanelTemplate>
                                            </ItemsControl.ItemsPanel>
                                        </ItemsControl>
                                        <ContentPresenter Margin="12,10,0,0" 
                                                          Content="{TemplateBinding Content}"
                                                          Foreground="{DynamicResource SukiText}"
                                                          TextWrapping="Wrap" >
                                            <ContentPresenter.Styles>
                                                <Style Selector="TextBlock">
                                                    <Setter Property="FontSize" Value="14" />
                                                </Style>
                                            </ContentPresenter.Styles>
                                        </ContentPresenter>

                                    </DockPanel>
                                </Panel>
                            </Panel>
                        </Border>
                    </Border>
                    <Border Width="35"
                            Height="35"
                            Margin="22,0,0,3"
                            HorizontalAlignment="Left"
                            VerticalAlignment="Center"
                            Background="{DynamicResource SukiCardBackground}"
                            BoxShadow="{DynamicResource SukiSmallPopupShadow}"
                            CornerRadius="35">
                        <Border  ClipToBounds="True" CornerRadius="35">
                            <Panel>
                                <Panel Background="{TemplateBinding Foreground}" Opacity="1" />
                                <PathIcon Width="15"
                                          Height="15"
                                          Margin="0,0,0,0"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="Center"
                                          Data="{TemplateBinding Icon}"
                                          Foreground="White" />
                                <Border IsVisible="{TemplateBinding LoadingState}" Background="{DynamicResource SukiCardBackground}">
                                  
                                    <suki:WaveProgress IsTextVisible="False" Value="50" Height="35" Width="35"></suki:WaveProgress>
                                </Border>
                            </Panel>
                        </Border>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type suki:SukiToast}"
                  BasedOn="{StaticResource SukiToastTheme}"
                  TargetType="suki:SukiToast" />
</ResourceDictionary>