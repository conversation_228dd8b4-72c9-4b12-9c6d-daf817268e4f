<ResourceDictionary x:Class="SukiUI.Controls.PropertyGridTemplateSelector"
                    xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:SukiUI.Controls;assembly=SukiUI">

	<controls:DateTimePickerSelectedDateConverter x:Key="DateTimePickerSelectedDateConverter" />

	<DataTemplate x:Key="CategoryViewModel" DataType="controls:CategoryViewModel">
        <Expander Padding="10,20,10,20" Margin="0,0,0,10" Grid.IsSharedSizeScope="True"
                  Header="{Binding DisplayName}"
                  IsExpanded="True">
            <ItemsControl Margin="7,0,0,20" ItemsSource="{Binding Properties}" />
        </Expander>
    </DataTemplate>

    <DataTemplate x:Key="StringViewModel" DataType="controls:StringViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <TextBox Grid.Column="2"
                     HorizontalAlignment="Right" MinWidth="120"
                     VerticalAlignment="Center"
                     HorizontalContentAlignment="Right"
                     IsReadOnly="{Binding IsReadOnly}"
                     Text="{Binding Value}" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="DateTimeOffsetViewModel" DataType="controls:DateTimeOffsetViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <DatePicker Grid.Column="2" Width="175"
                        Margin="-6,0" Height="36"
                        HorizontalAlignment="Right"
                        VerticalAlignment="Stretch"
                        IsEnabled="{Binding !IsReadOnly}"
                        SelectedDate="{Binding Value, Converter={StaticResource DateTimePickerSelectedDateConverter}}" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="DateTimeViewModel" DataType="controls:DateTimeViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <DatePicker Grid.Column="2"
                        Margin="-6,0" Height="36"
                        HorizontalAlignment="Right" Width="175"
                        VerticalAlignment="Stretch"
                        IsEnabled="{Binding !IsReadOnly}"
                        SelectedDate="{Binding Value, Converter={StaticResource DateTimePickerSelectedDateConverter}}" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="IntegerViewModel" DataType="controls:IntegerViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <NumericUpDown Classes="NumberSelector" Increment="1" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="DoubleViewModel" DataType="controls:DoubleViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <NumericUpDown Classes="NumberSelector" Increment="0.001" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="BoolViewModel" DataType="controls:BoolViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <ToggleSwitch Grid.Column="2"
                          Margin="-2,0"
                          VerticalAlignment="Stretch"
                          HorizontalContentAlignment="Right" HorizontalAlignment="Right"
                          IsChecked="{Binding Value}"
                          IsEnabled="{Binding !IsReadOnly}" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="DecimalViewModel" DataType="controls:DecimalViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <NumericUpDown Classes="NumberSelector" Increment="0.001" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="FloatViewModel" DataType="controls:FloatViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <NumericUpDown Classes="NumberSelector" Increment="0.001" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="LongViewModel" DataType="controls:LongViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <NumericUpDown Classes="NumberSelector" Increment="1" />
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="ComplexTypeViewModel" DataType="controls:ComplexTypeViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <Button Grid.Column="2"
                    HorizontalAlignment="Right"
                     Height="36" Margin="4,0"
                    Click="OnMoreInfoClick">
                <TextBlock Text="More Info" />
            </Button>
        </Grid>
    </DataTemplate>

    <DataTemplate x:Key="EnumViewModel" DataType="controls:EnumViewModel">
        <Grid Classes="Row">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto" SharedSizeGroup="PropertyGridDisplyColumn" />
                <ColumnDefinition Width="4" />
                <ColumnDefinition Width="*" />
            </Grid.ColumnDefinitions>

            <TextBlock Classes="Label" />
            <ComboBox Grid.Column="2"
                      HorizontalAlignment="Right" MinWidth="120"
                      VerticalAlignment="Stretch"
                      HorizontalContentAlignment="Center"
                      IsEnabled="{Binding !IsReadOnly}"
                      ItemsSource="{Binding Values}"
                      SelectedItem="{Binding Value}" />
        </Grid>
    </DataTemplate>
</ResourceDictionary>
