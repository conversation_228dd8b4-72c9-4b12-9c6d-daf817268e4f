﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Text.Json.Serialization;

namespace SukiUI.Demo.Bll
{

    public class SiheyiConfig
    {
        public SiheyiConfig()        
        {
            //IsDebug.Value = true;
        }
        [JsonPropertyName("jkurl")]
        public ConfigItem<string> Jkurl { get; set; }

        [JsonPropertyName("jkxlh")]
        public ConfigItem<string> Jkxlh { get; set; }

        [JsonPropertyName("winsize")]
        public ConfigItem<string> Winsize { get; set; }

        [JsonPropertyName("spwinsize")]
        public ConfigItem<string> Spwinsize { get; set; }

        [JsonPropertyName("showcars")]
        public ConfigItem<string> Showcars { get; set; }

        [JsonPropertyName("jtdk_czudp")]
        public ConfigItem<int> JtdkCzudp { get; set; }

        [JsonPropertyName("kskm")]
        public ConfigItem<int> Kskm { get; set; }

        [JsonPropertyName("xsms")]
        public ConfigItem<string> Xsms { get; set; }

        [JsonPropertyName("dtjztc")]
        public ConfigItem<string> Dtjztc { get; set; }

        [JsonPropertyName("csms")]
        public ConfigItem<int> Csms { get; set; }

        [JsonPropertyName("uvphotoms")]
        public ConfigItem<int> Uvphotoms { get; set; }

        [JsonPropertyName("uvphoto_sfz_size")]
        public ConfigItem<string> UvphotoSfzSize { get; set; }

        [JsonPropertyName("uvphoto_mj_size")]
        public ConfigItem<string> UvphotoMjSize { get; set; }

        [JsonPropertyName("yplxjaddress")]
        public ConfigItem<string> Yplxjaddress { get; set; }

        [JsonPropertyName("yplxjuser")]
        public ConfigItem<string> Yplxjuser { get; set; }

        [JsonPropertyName("yplxjpwd")]
        public ConfigItem<string> Yplxjpwd { get; set; }

        [JsonPropertyName("yplxjcnsppz")]
        public ConfigItem<string> Yplxjcnsppz { get; set; }

        [JsonPropertyName("yplxjcwsppz")]
        public ConfigItem<string> Yplxjcwsppz { get; set; }

        [JsonPropertyName("yplxjcwsppz2")]
        public ConfigItem<string> Yplxjcwsppz2 { get; set; }

        [JsonPropertyName("yplxjcwsppz3")]
        public ConfigItem<string> Yplxjcwsppz3 { get; set; }

        [JsonPropertyName("gjsxsj")]
        public ConfigItem<int> Gjsxsj { get; set; }

        [JsonPropertyName("hmqckksxh")]
        public ConfigItem<int> Hmqckksxh { get; set; }

        [JsonPropertyName("sctpSize")]
        public ConfigItem<string> SctpSize { get; set; }

        [JsonPropertyName("spcksl")]
        public ConfigItem<int> Spcksl { get; set; }

        [JsonPropertyName("xysxsj")]
        public ConfigItem<int> Xysxsj { get; set; }

        [JsonPropertyName("xytpxssj")]
        public ConfigItem<int> Xytpxssj { get; set; }

        [JsonPropertyName("saveupd")]
        public ConfigItem<int> Saveupd { get; set; }

        [JsonPropertyName("ksjsqcxyxx")]
        public ConfigItem<int> Ksjsqcxyxx { get; set; }

        [JsonPropertyName("qcxyxxys")]
        public ConfigItem<int> Qcxyxxys { get; set; }

        [JsonPropertyName("SEATBELT")]
        public ConfigItem<int> Seatbelt { get; set; }

        [JsonPropertyName("CLUTCH")]
        public ConfigItem<int> Clutch { get; set; }

        [JsonPropertyName("xsfusha")]
        public ConfigItem<int> Xsfusha { get; set; }

        [JsonPropertyName("xhsxsj")]
        public ConfigItem<int> Xhsxsj { get; set; }

        [JsonPropertyName("dxcsc")]
        public ConfigItem<int> Dxcsc { get; set; }

        [JsonPropertyName("uppcj")]
        public ConfigItem<int> Uppcj { get; set; }

        [JsonPropertyName("sfIps")]
        public ConfigItem<int> SfIps { get; set; }

        [JsonPropertyName("sfA2")]
        public ConfigItem<int> SfA2 { get; set; }

        [JsonPropertyName("sf6816M")]
        public ConfigItem<int> Sf6816M { get; set; }

        [JsonPropertyName("sfImg")]
        public ConfigItem<int> SfImg { get; set; }

        [JsonPropertyName("ksxms")]
        public ConfigItem<string> Ksxms { get; set; }

        [JsonPropertyName("dlms")]
        public ConfigItem<int> Dlms { get; set; }

        [JsonPropertyName("xscl")]
        public ConfigItem<int> Xscl { get; set; }

        [JsonPropertyName("wbqh")]
        public ConfigItem<int> Wbqh { get; set; }

        [JsonPropertyName("xsqjd")]
        public ConfigItem<int> Xsqjd { get; set; }

        [JsonPropertyName("sfqyscjg")]
        public ConfigItem<int> Sfqyscjg { get; set; }

        [JsonPropertyName("xszkzh")]
        public ConfigItem<int> Xszkzh { get; set; }

        [JsonPropertyName("kcmc")]
        public ConfigItem<string> Kcmc { get; set; }

        [JsonPropertyName("IsDebug")]
       
        public ConfigItem<bool> IsDebug { get; set; }

        [JsonPropertyName("MapZoom")]
        public ConfigItem<int> MapZoom { get; set; }

        [JsonPropertyName("IsMoto")]
        public ConfigItem<bool> IsMoto { get; set; }

        [JsonPropertyName("Jxmc")]
        public ConfigItem<string> Jxmc { get; set; }

        [JsonPropertyName("IsMouseWheel")]
        public ConfigItem<bool> IsMouseWheel { get; set; }

        [JsonPropertyName("IsRecord")]
        public ConfigItem<bool> IsRecord { get; set; }

        [JsonPropertyName("Ysjzxyxx")]
        public ConfigItem<bool> Ysjzxyxx { get; set; }

        [JsonPropertyName("Tpgd")]
        public ConfigItem<int> Tpgd { get; set; }

        [JsonPropertyName("IsChangeMapZoom")]
        public ConfigItem<bool> IsChangeMapZoom { get; set; }

        [JsonPropertyName("ShowKssj")]
        public ConfigItem<bool> ShowKssj { get; set; }

        [JsonPropertyName("ShowKsy")]
        public ConfigItem<bool> ShowKsy { get; set; }

        [JsonPropertyName("ShowKsxms")]
        public ConfigItem<bool> ShowKsxms { get; set; }

        [JsonPropertyName("XsDqxm")]
        public ConfigItem<bool> XsDqxm { get; set; }

        [JsonPropertyName("Zybj")]
        public ConfigItem<bool> Zybj { get; set; }

        [JsonPropertyName("XhPic")]
        public ConfigItem<bool> XhPic { get; set; }

        [JsonPropertyName("DefalutPicPos")]
        public ConfigItem<bool> DefalutPicPos { get; set; }

        [JsonPropertyName("Xsxhs")]
        public ConfigItem<string> Xsxhs { get; set; }

        [JsonPropertyName("XsJxmc")]
        public ConfigItem<bool> XsJxmc { get; set; }

        [JsonPropertyName("C2Dw")]
        public ConfigItem<string> C2Dw { get; set; }

        [JsonPropertyName("yxbs")]
        public ConfigItem<bool> Yxbs { get; set; }

        [JsonPropertyName("IsGuizhou")]
        public ConfigItem<bool> IsGuizhou { get; set; }

        [JsonPropertyName("CarZoom")]
        public ConfigItem<double> CarZoom { get; set; }

        [JsonPropertyName("tbfwqsj")]
        public ConfigItem<bool> Tbfwqsj { get; set; }

        [JsonPropertyName("tcyj")]
        public ConfigItem<bool> Tcyj { get; set; }

        [JsonPropertyName("sfldlms")]
        public ConfigItem<bool> Sfldlms { get; set; }

        [JsonPropertyName("mrqjqh")]
        public ConfigItem<bool> Mrqjqh { get; set; }

        [JsonPropertyName("jshg")]
        public ConfigItem<bool> Jshg { get; set; }

        [JsonPropertyName("Sha256")]
        public ConfigItem<bool> Sha256 { get; set; }
    }

    public class ConfigItem<T>
    {
        [JsonPropertyName("comment")]
        public string Comment { get; set; }

        [JsonPropertyName("value")]
        public T Value { get; set; }
    }
}

