<Window x:Class="SukiUI.Demo.Features.Login.LoginView"
        xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:login="clr-namespace:SukiUI.Demo.Features.Login"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:suki="https://github.com/kikipoulet/SukiUI"
        Title="登录"
        Width="450"
        Height="600"
        d:DesignHeight="450"
        d:DesignWidth="800"
        x:DataType="login:LoginViewModel"
        Background="Transparent"
        CanResize="False"
        ExtendClientAreaToDecorationsHint="True"
        ShowInTaskbar="False"
        SystemDecorations="BorderOnly"
        TransparencyLevelHint="AcrylicBlur"
        WindowStartupLocation="CenterScreen"
        WindowState="Normal"
        mc:Ignorable="d">

    <Grid>
        <suki:GlassCard Width="400"
                        Height="500"
                        Margin="20"
                        HorizontalAlignment="Center"
                        VerticalAlignment="Center">
            <StackPanel Margin="20" Spacing="20">
                <Image Width="240"
                       Height="120"
                       Margin="0,20,0,30"
                       Source="/Assets/OIG.N5o-removebg-preview.png" />

                <TextBox Classes="clearButton"
                         Text="{Binding Username}"
                         Watermark="用户名" />

                <TextBox Classes="revealPasswordButton"
                         PasswordChar="*"
                         Text="{Binding Password}"
                         Watermark="密码" />

                <!--<CheckBox Content="记住密码"
                          IsChecked="{Binding RememberMe}"/>-->



                <Grid Margin="0,10" ColumnDefinitions="*,20,*">
                    <Button Grid.Column="0"
                            HorizontalAlignment="Stretch"
                            Classes="Flat Rounded"
                            Command="{Binding LoginCommand}"
                            Content="登录" />

                    <Button Grid.Column="2"
                            HorizontalAlignment="Stretch"
                            Classes="Flat Rounded Accent"
                            Command="{Binding CancelCommand}"
                            Content="取消" />
                </Grid>

                <TextBlock Margin="0,5"
                           HorizontalAlignment="Center"
                           Foreground="Red"
                           IsVisible="{Binding HasError}"
                           Text="{Binding ErrorMessage}"
                           TextWrapping="Wrap" />
            </StackPanel>
        </suki:GlassCard>
    </Grid>
</Window>