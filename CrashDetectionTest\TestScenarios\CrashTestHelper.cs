using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using CrashDetectionTest.Infrastructure;

namespace CrashDetectionTest.TestScenarios
{
    /// <summary>
    /// 崩溃测试助手 - 提供各种崩溃场景的测试方法
    /// </summary>
    public static class CrashTestHelper
    {
        /// <summary>
        /// 测试空引用异常
        /// </summary>
        public static void TestNullReferenceException()
        {
            Console.WriteLine("🧪 执行空引用异常测试...");
            Thread.Sleep(1000);

            try
            {
                string? nullString = null;
                var length = nullString!.Length; // 这将抛出 NullReferenceException
            }
            catch (Exception ex)
            {
                CrashLogger.LogException(ex, "CrashTestHelper - 空引用异常测试");
                Console.WriteLine($"✓ 捕获异常: {ex.GetType().Name}");
            }
        }

        /// <summary>
        /// 测试除零异常
        /// </summary>
        public static void TestDivideByZeroException()
        {
            Console.WriteLine("🧪 执行除零异常测试...");
            Thread.Sleep(1000);

            try
            {
                int divisor = 0;
                int result = 10 / divisor; // 这将抛出 DivideByZeroException
            }
            catch (Exception ex)
            {
                CrashLogger.LogException(ex, "CrashTestHelper - 除零异常测试");
                Console.WriteLine($"✓ 捕获异常: {ex.GetType().Name}");
            }
        }

        /// <summary>
        /// 测试索引越界异常
        /// </summary>
        public static void TestIndexOutOfRangeException()
        {
            Console.WriteLine("🧪 执行索引越界异常测试...");
            Thread.Sleep(1000);

            try
            {
                var array = new int[5];
                var value = array[10]; // 这将抛出 IndexOutOfRangeException
            }
            catch (Exception ex)
            {
                CrashLogger.LogException(ex, "CrashTestHelper - 索引越界异常测试");
                Console.WriteLine($"✓ 捕获异常: {ex.GetType().Name}");
            }
        }

        /// <summary>
        /// 测试内存不足异常（模拟）
        /// </summary>
        public static void TestOutOfMemoryException()
        {
            Console.WriteLine("🧪 执行内存不足异常测试（模拟）...");
            Thread.Sleep(1000);

            try
            {
                // 模拟内存不足异常
                throw new OutOfMemoryException("模拟的内存不足异常");
            }
            catch (Exception ex)
            {
                CrashLogger.LogException(ex, "CrashTestHelper - 内存不足异常测试");
                Console.WriteLine($"✓ 捕获异常: {ex.GetType().Name}");
            }
        }

        /// <summary>
        /// 测试栈溢出异常（模拟）
        /// </summary>
        public static void TestStackOverflowException()
        {
            Console.WriteLine("🧪 执行栈溢出异常测试（模拟）...");
            Thread.Sleep(1000);

            try
            {
                // 注意：真正的栈溢出异常无法被捕获，这里只是模拟
                throw new StackOverflowException("模拟的栈溢出异常");
            }
            catch (Exception ex)
            {
                CrashLogger.LogException(ex, "CrashTestHelper - 栈溢出异常测试");
                Console.WriteLine($"✓ 捕获异常: {ex.GetType().Name}");
            }
        }

        /// <summary>
        /// 测试多线程异常
        /// </summary>
        public static async Task TestMultiThreadedException()
        {
            Console.WriteLine("🧪 执行多线程异常测试...");
            
            var tasks = new List<Task>();
            
            for (int i = 0; i < 3; i++)
            {
                int taskId = i;
                tasks.Add(Task.Run(() =>
                {
                    Thread.Sleep(500 + taskId * 200);
                    try
                    {
                        if (taskId == 1)
                        {
                            throw new InvalidOperationException($"线程 {taskId} 中的测试异常");
                        }
                    }
                    catch (Exception ex)
                    {
                        CrashLogger.LogException(ex, $"CrashTestHelper - 多线程异常测试 (线程 {taskId})");
                        Console.WriteLine($"✓ 线程 {taskId} 捕获异常: {ex.GetType().Name}");
                    }
                }));
            }

            await Task.WhenAll(tasks);
            Console.WriteLine("✓ 多线程异常测试完成");
        }

        /// <summary>
        /// 测试高内存使用
        /// </summary>
        public static void TestHighMemoryUsage()
        {
            Console.WriteLine("🧪 执行高内存使用测试...");
            
            try
            {
                // 分配大量内存来触发监控警告
                var largeArrays = new List<byte[]>();
                
                for (int i = 0; i < 10; i++)
                {
                    var array = new byte[50 * 1024 * 1024]; // 50MB
                    largeArrays.Add(array);
                    Console.WriteLine($"已分配 {(i + 1) * 50}MB 内存");
                    Thread.Sleep(500);
                }

                Console.WriteLine("✓ 高内存使用测试完成，等待5秒后释放内存...");
                Thread.Sleep(5000);
                
                // 释放内存
                largeArrays.Clear();
                GC.Collect();
                GC.WaitForPendingFinalizers();
                GC.Collect();
                
                Console.WriteLine("✓ 内存已释放");
            }
            catch (Exception ex)
            {
                CrashLogger.LogException(ex, "CrashTestHelper - 高内存使用测试");
                Console.WriteLine($"✓ 捕获异常: {ex.GetType().Name}");
            }
        }

        /// <summary>
        /// 测试高线程数
        /// </summary>
        public static async Task TestHighThreadCount()
        {
            Console.WriteLine("🧪 执行高线程数测试...");
            
            var tasks = new List<Task>();
            var cancellationTokenSource = new CancellationTokenSource();
            
            try
            {
                // 创建大量线程
                for (int i = 0; i < 60; i++)
                {
                    int threadId = i;
                    tasks.Add(Task.Run(async () =>
                    {
                        Console.WriteLine($"线程 {threadId} 启动");
                        try
                        {
                            await Task.Delay(10000, cancellationTokenSource.Token);
                        }
                        catch (OperationCanceledException)
                        {
                            Console.WriteLine($"线程 {threadId} 被取消");
                        }
                    }));
                    
                    if (i % 10 == 0)
                    {
                        Console.WriteLine($"已创建 {i + 1} 个线程");
                        Thread.Sleep(200);
                    }
                }

                Console.WriteLine("✓ 高线程数测试完成，等待5秒后停止所有线程...");
                await Task.Delay(5000);
                
                // 取消所有任务
                cancellationTokenSource.Cancel();
                
                try
                {
                    await Task.WhenAll(tasks);
                }
                catch (OperationCanceledException)
                {
                    // 预期的异常
                }
                
                Console.WriteLine("✓ 所有线程已停止");
            }
            catch (Exception ex)
            {
                CrashLogger.LogException(ex, "CrashTestHelper - 高线程数测试");
                Console.WriteLine($"✓ 捕获异常: {ex.GetType().Name}");
            }
            finally
            {
                cancellationTokenSource.Cancel();
                cancellationTokenSource.Dispose();
            }
        }

        /// <summary>
        /// 测试未处理异常（这将导致程序崩溃）
        /// </summary>
        public static void TestUnhandledException()
        {
            Console.WriteLine("🚨 警告：即将触发未处理异常，程序将崩溃！");
            Console.WriteLine("按任意键继续，或按 Ctrl+C 取消...");
            Console.ReadKey();
            
            Console.WriteLine("🧪 触发未处理异常...");
            Thread.Sleep(1000);
            
            // 这将触发未处理异常，导致程序崩溃
            throw new InvalidOperationException("这是一个测试用的未处理异常，将导致程序崩溃");
        }
    }
}
