using CommunityToolkit.Mvvm.ComponentModel;

namespace SukiUI.Demo.Features.Dashboard;

public partial class InvoiceViewModel : ObservableObject
{
    [ObservableProperty] private string? _id;
    [ObservableProperty] private int? _jvalue;
    [ObservableProperty] private string? _jname;
    [ObservableProperty] private bool? _paid;

    public InvoiceViewModel(string id, string? jname, int jvalue, bool paid)
    {
        Id = id;
        Jname = jname;
        Jvalue = jvalue;
        Paid = paid;
    }
    public InvoiceViewModel() { }
}