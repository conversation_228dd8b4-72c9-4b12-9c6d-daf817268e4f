﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using System.Data;
using System.Threading;
using Newtonsoft.Json;

namespace SukiUI.Demo.Bll
{
    public class JKTrans
    {
        public static List<BaseDataModel> GetXyList()
        {

            //string sqlstr1 = "select t.jxdm ,t.jxjc  from tk_driving t";//减少服务器压力 ldw2018-08-17
            //List<BaseDataModel> list_jx = DALBase.GetList(sqlstr1);
            SiHeYiListParam param= new SiHeYiListParam();
            param.JKID = APIS.jiaXiao;
            List<BaseDataModel> list_jx = APIS.GetListDataFromApi(param);

            //string sqlstr = "select * from vd_ycjk_ksing_xy";
            //List<BaseDataModel> list = DALBase.GetList(sqlstr);
            SiHeYiListParam paramXy = new SiHeYiListParam();
            paramXy.JKID = APIS.xueYuan;
            List<BaseDataModel> list = APIS.GetListDataFromApi(paramXy);
            for (int i = 0; i < list.Count; i++)
            {
                BaseDataModel data = list[i];
                #region 不再走视图
                data.SetColumnValue("t_mjzp_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_mjzp")));
                data.SetColumnValue("t_zw_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_zw")));
                data.SetColumnValue("t_photo_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_photo")));
                data.SetColumnValue("t_zxzp_buffer", FileTools.ConvertFromBase64Str(data.GetValueStr("t_zxzp")));
                data.SetColumnValue("kskssj", data.GetValueStr("extendedproperty2") );
                data.SetColumnValue("ykkskssj", data.GetValueStr("extendedproperty3") );
                #endregion 直接取读数据
                string sczt = data.GetValueStr("t_sczt");
                if (sczt == "0" && !data.GetValueStr("t_ksgczt").Contains("44"))
                {
                    data.SetColumnValue("ks_up_code", "");
                    data.SetColumnValue("kfxms", "");
                    data.SetColumnValue("yks_ksxms", "");
                    data.SetColumnValue("kslc", "");
                }
                BaseDataModel data_jx = list_jx.Find(_d => _d.GetValueStr("jxdm") == data.GetValueStr("dlr"));
                if (data_jx != null)
                {
                    data.SetColumnValue("jxjc", data_jx.GetValueStr("jxjc"));
                }
                else
                {
                    
                    var jxjc = data.GetValueStr("dlr");//兼容摩托车
                    if (string.IsNullOrEmpty(jxjc))
                    {
                        jxjc = "网络报名";
                    }
                    data.SetColumnValue("jxjc", jxjc);
                }
            }
            return list;
        }
        /// <summary>
        /// 是否考试结束
        /// </summary>
        /// <param name="sfzmhm"></param>
        /// <returns></returns>
       
        public static bool IsKsjsWithYk(string sfzmhm,string kskssj="", bool isdc = false)
        {
           
           
            //新模式
            //string sqlstr = $"select count(t.id) from tk_exam_order t where t.ykrq=trunc(sysdate) and t.sfzmhm='{sfzmhm}' and t.t_kszt='1'" ;
            //var obj = DALBase.ExecuteScalar(sqlstr);
            ////没开始夜考，白考合格的情况下也清除
            //if (isdc && !(Convert.ToInt32(obj) > 0))
            //{
            //    sqlstr =
            //        $"select count(t.id) from TK_EXAM_ORDER t where (t.t_ksyk!='1' or t.t_ksyk is null) and t.t_bkcj>=90 and t.ykrq=trunc(sysdate)  and t.sfzmhm='{sfzmhm}'";
            //    obj = DALBase.ExecuteScalar(sqlstr);

            //}
            //return Convert.ToInt32(obj) > 0;
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.kaoShiJieShu;
            param.DATA = JsonConvert.SerializeObject(new { sfzmhm = sfzmhm, isdc=isdc ? "1" : "0" });
            var result= APIS.GetStringDataFromApi(param);
            return result == "1";
        }
       

        

       

        public static List<BaseDataModel> GetKfxmList()
        {
            
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.kouFen;
           return   APIS.GetListDataFromApi(param);
        }
        public static string GetConfig(string configName)
        {
            
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.config;
            param.DATA = JsonConvert.SerializeObject(new { name= configName });
            return APIS.GetStringDataFromApi(param);
        }
        public static List<BaseDataModel> GetKsyList()
        {
            
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.KaoShiYuan;
            return APIS.GetListDataFromApi(param);
        }
        public static List<BaseDataModel> GetClList()
        {
           
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.cheliang;
            return APIS.GetListDataFromApi(param);
        }
        

       

       

      
       
        

       

        /// <summary>
        /// 得到所有的角色列表
        /// </summary>
        /// <returns></returns>
        public static List<BaseDataModel> GetAllUserList()
        {
            //string sqlstr = "select a.*,b.r_rolename from tb_user a,tb_role b where a.u_role_id=b.pk_role(+)";
            //return DALBase.GetList(sqlstr);
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.user;
            var result = APIS.GetListDataFromApi(param);
            return result;
        }

        /// <summary>
        /// pk_user,u_username,u_password,u_rename,u_login_count,u_status,pk_role,r_rolename,r_status
        /// </summary>
        /// <param name="username"></param>
        /// <param name="pwd"></param>
        /// <returns></returns>
        public static BaseDataModel CheckLogin(string username, string pwd)
        {
            //var Uid = string.Empty;
            //pwd = PwdTools.SHA256(Uid + username + pwd);
            //string sqlstr = "select * from vw_user_role where u_username=:u_username and u_password=:u_password";
            //List<OracleParameter> oplist = new List<OracleParameter>();
            //oplist.Add(new OracleParameter("u_username", username));
            //oplist.Add(new OracleParameter("u_password", pwd));
            SiHeYiListParam param = new SiHeYiListParam();
            param.JKID = APIS.checkLogin;
            var data = new { username = username, password = pwd };
            param.DATA= JsonConvert.SerializeObject(data);
            var list = APIS.GetListDataFromApi(param);
            if (list.Count == 0) return null;
            return list[0];
        }

    }
}
