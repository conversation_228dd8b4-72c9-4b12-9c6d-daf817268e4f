﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:converters="clr-namespace:SukiUI.Converters">

    <Style Selector="ScrollViewer">
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="*,Auto" RowDefinitions="*,Auto">
                    <ScrollContentPresenter Name="PART_ContentPresenter"
                                            Padding="{TemplateBinding Padding}"
                                            Background="{TemplateBinding Background}"
                                            HorizontalSnapPointsAlignment="{TemplateBinding HorizontalSnapPointsAlignment}"
                                            HorizontalSnapPointsType="{TemplateBinding HorizontalSnapPointsType}"
                                            ScrollViewer.IsScrollInertiaEnabled="{TemplateBinding IsScrollInertiaEnabled}"
                                            VerticalSnapPointsAlignment="{TemplateBinding VerticalSnapPointsAlignment}"
                                            VerticalSnapPointsType="{TemplateBinding VerticalSnapPointsType}">
                        <ScrollContentPresenter.GestureRecognizers>
                            <ScrollGestureRecognizer CanHorizontallyScroll="{Binding CanHorizontallyScroll, ElementName=PART_ContentPresenter}"
                                                     CanVerticallyScroll="{Binding CanVerticallyScroll, ElementName=PART_ContentPresenter}"
                                                     IsScrollInertiaEnabled="{Binding (ScrollViewer.IsScrollInertiaEnabled), ElementName=PART_ContentPresenter}" />
                        </ScrollContentPresenter.GestureRecognizers>
                    </ScrollContentPresenter>
                    <ScrollBar Name="PART_HorizontalScrollBar"
                               Grid.Row="1"
                               Grid.Column="0"
                               Orientation="Horizontal" />
                    <ScrollBar Name="PART_VerticalScrollBar"
                               Grid.Row="0"
                               Grid.Column="1"
                               Orientation="Vertical" />
                    <Panel Name="PART_ScrollBarsSeparator"
                           Grid.Row="1"
                           Grid.Column="1"
                           Background="Transparent" />
                </Grid>
            </ControlTemplate>
        </Setter>
    </Style>
    <Style Selector="ScrollViewer.Stack">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    <Panel>
                        <Panel.OpacityMask>
                            <MultiBinding Converter="{x:Static converters:SideMenuScrollerToOpacityMask.Top}">
                                <Binding ElementName="PART_VerticalScrollBar" Path="Value" />
                                <Binding ElementName="PART_VerticalScrollBar" Path="Minimum" />
                            </MultiBinding>
                        </Panel.OpacityMask>
                        <Panel.Transitions>
                            <Transitions>
                                <BrushTransition Property="OpacityMask" Duration="{StaticResource ShortAnimationDuration}" />
                            </Transitions>
                        </Panel.Transitions>
                        <ScrollContentPresenter Name="PART_ContentPresenter"
                                                Padding="{TemplateBinding Padding}"
                                                Background="{TemplateBinding Background}"
                                                HorizontalSnapPointsAlignment="{TemplateBinding HorizontalSnapPointsAlignment}"
                                                HorizontalSnapPointsType="{TemplateBinding HorizontalSnapPointsType}"
                                                ScrollViewer.IsScrollInertiaEnabled="{TemplateBinding IsScrollInertiaEnabled}"
                                                VerticalSnapPointsAlignment="{TemplateBinding VerticalSnapPointsAlignment}"
                                                VerticalSnapPointsType="{TemplateBinding VerticalSnapPointsType}">
                            <ScrollContentPresenter.OpacityMask>
                                <MultiBinding Converter="{x:Static converters:SideMenuScrollerToOpacityMask.Bottom}">
                                    <Binding ElementName="PART_VerticalScrollBar" Path="Value" />
                                    <Binding ElementName="PART_VerticalScrollBar" Path="Maximum" />
                                </MultiBinding>
                            </ScrollContentPresenter.OpacityMask>
                            <ScrollContentPresenter.Transitions>
                                <Transitions>
                                    <BrushTransition Property="OpacityMask" Duration="{StaticResource ShortAnimationDuration}" />
                                </Transitions>
                            </ScrollContentPresenter.Transitions>
                            <ScrollContentPresenter.GestureRecognizers>
                                <ScrollGestureRecognizer CanHorizontallyScroll="{Binding CanHorizontallyScroll, ElementName=PART_ContentPresenter}"
                                                         CanVerticallyScroll="{Binding CanVerticallyScroll, ElementName=PART_ContentPresenter}"
                                                         IsScrollInertiaEnabled="{Binding (ScrollViewer.IsScrollInertiaEnabled), ElementName=PART_ContentPresenter}" />
                            </ScrollContentPresenter.GestureRecognizers>
                        </ScrollContentPresenter>
                    </Panel>
                    <ScrollBar Name="PART_VerticalScrollBar"
                               HorizontalAlignment="Stretch"
                               VerticalAlignment="Stretch"
                               Classes="Stack"
                               Orientation="Vertical" />
                </Panel>
            </ControlTemplate>
        </Setter>
        <Style Selector="^ /template/ ScrollContentPresenter" />
    </Style>
</Styles>