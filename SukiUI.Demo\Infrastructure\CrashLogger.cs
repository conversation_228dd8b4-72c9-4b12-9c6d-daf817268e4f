using System;
using System.IO;
using System.Text;
using System.Threading.Tasks;
using System.Runtime.InteropServices;
using System.Reflection;
using System.Diagnostics;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// 崩溃日志记录器 - 专门用于记录程序异常退出信息
    /// </summary>
    public static class CrashLogger
    {
        private static readonly object _lockObject = new object();
        private static string _logDirectory;
        private static bool _isInitialized = false;

        /// <summary>
        /// 初始化崩溃日志记录器
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            try
            {
                // 确定日志目录
                _logDirectory = GetLogDirectory();
                
                // 确保目录存在
                if (!Directory.Exists(_logDirectory))
                {
                    Directory.CreateDirectory(_logDirectory);
                }

                _isInitialized = true;
                
                // 记录初始化成功
                WriteLog("INFO", "CrashLogger initialized successfully", null);
                WriteSystemInfo();
            }
            catch (Exception ex)
            {
                // 如果初始化失败，尝试写入到临时目录
                try
                {
                    _logDirectory = Path.GetTempPath();
                    WriteLog("ERROR", $"CrashLogger initialization failed, using temp directory: {ex.Message}", ex);
                    _isInitialized = true;
                }
                catch
                {
                    // 如果连临时目录都无法写入，则放弃日志记录
                    _isInitialized = false;
                }
            }
        }

        /// <summary>
        /// 记录未处理异常
        /// </summary>
        public static void LogUnhandledException(Exception exception, string source = "Unknown")
        {
            if (!_isInitialized) return;

            try
            {
                var context = GetCurrentContext();
                WriteLog("FATAL", $"Unhandled exception from {source}: {exception.Message}", exception, context);
            }
            catch
            {
                // 忽略日志记录失败，避免二次异常
            }
        }

        /// <summary>
        /// 记录任务异常
        /// </summary>
        public static void LogTaskException(Exception exception, string taskInfo = "Unknown Task")
        {
            if (!_isInitialized) return;

            try
            {
                WriteLog("ERROR", $"Task exception in {taskInfo}: {exception.Message}", exception);
            }
            catch
            {
                // 忽略日志记录失败
            }
        }

        /// <summary>
        /// 记录应用程序异常
        /// </summary>
        public static void LogApplicationException(Exception exception, string context = "Application")
        {
            if (!_isInitialized) return;

            try
            {
                if (exception != null)
                {
                    WriteLog("ERROR", $"Application exception in {context}: {exception.Message}", exception);
                }
                else
                {
                    WriteLog("INFO", $"Application event in {context}: No exception details available", null);
                }
            }
            catch
            {
                // 忽略日志记录失败
            }
        }

        /// <summary>
        /// 记录测试事件信息
        /// </summary>
        public static void LogTestEvent(string eventName, string description = "")
        {
            if (!_isInitialized) return;

            try
            {
                var message = string.IsNullOrEmpty(description)
                    ? $"Test event: {eventName}"
                    : $"Test event: {eventName} - {description}";
                WriteLog("INFO", message, null, null);
            }
            catch
            {
                // 忽略日志记录失败
            }
        }

        /// <summary>
        /// 记录进程退出信息
        /// </summary>
        public static void LogProcessExit(string reason = "Normal exit")
        {
            if (!_isInitialized) return;

            try
            {
                WriteLog("INFO", $"Process exiting: {reason}", null);
            }
            catch
            {
                // 忽略日志记录失败
            }
        }

        /// <summary>
        /// 获取日志目录
        /// </summary>
        private static string GetLogDirectory()
        {
            // Linux优先使用应用程序目录下的logs文件夹
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                var appDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                var logsDir = Path.Combine(appDir ?? "/tmp", "crash_logs");
                
                // 如果应用程序目录不可写，使用/tmp
                try
                {
                    if (!Directory.Exists(logsDir))
                        Directory.CreateDirectory(logsDir);
                    
                    // 测试写权限
                    var testFile = Path.Combine(logsDir, "test_write.tmp");
                    File.WriteAllText(testFile, "test");
                    File.Delete(testFile);
                    
                    return logsDir;
                }
                catch
                {
                    return "/tmp/SukiUI_crash_logs";
                }
            }
            
            // Windows使用应用程序目录
            var winAppDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
            return Path.Combine(winAppDir ?? Environment.GetFolderPath(Environment.SpecialFolder.LocalApplicationData), "crash_logs");
        }

        /// <summary>
        /// 写入日志
        /// </summary>
        private static void WriteLog(string level, string message, Exception exception, string additionalContext = null)
        {
            lock (_lockObject)
            {
                try
                {
                    var timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss.fff");
                    var fileName = $"crash_{DateTime.Now:yyyyMMdd}.log";
                    var filePath = Path.Combine(_logDirectory, fileName);

                    var logBuilder = new StringBuilder();
                    logBuilder.AppendLine($"[{timestamp}] [{level}] {message}");
                    
                    if (!string.IsNullOrEmpty(additionalContext))
                    {
                        logBuilder.AppendLine($"Context: {additionalContext}");
                    }

                    if (exception != null)
                    {
                        logBuilder.AppendLine($"Exception Type: {exception.GetType().FullName}");
                        logBuilder.AppendLine($"Exception Message: {exception.Message}");
                        logBuilder.AppendLine($"Stack Trace:");
                        logBuilder.AppendLine(exception.StackTrace);
                        
                        // 记录内部异常
                        var innerEx = exception.InnerException;
                        int innerLevel = 1;
                        while (innerEx != null && innerLevel <= 5) // 限制内部异常层级
                        {
                            logBuilder.AppendLine($"Inner Exception {innerLevel}: {innerEx.GetType().FullName}");
                            logBuilder.AppendLine($"Inner Message {innerLevel}: {innerEx.Message}");
                            logBuilder.AppendLine($"Inner Stack Trace {innerLevel}:");
                            logBuilder.AppendLine(innerEx.StackTrace);
                            innerEx = innerEx.InnerException;
                            innerLevel++;
                        }
                    }

                    logBuilder.AppendLine(new string('-', 80));

                    File.AppendAllText(filePath, logBuilder.ToString(), Encoding.UTF8);
                }
                catch
                {
                    // 忽略写入失败，避免无限递归
                }
            }
        }

        /// <summary>
        /// 写入系统信息
        /// </summary>
        private static void WriteSystemInfo()
        {
            try
            {
                var systemInfo = GetSystemInfo();
                WriteLog("INFO", "System Information", null, systemInfo);
            }
            catch
            {
                // 忽略系统信息获取失败
            }
        }

        /// <summary>
        /// 获取系统信息
        /// </summary>
        private static string GetSystemInfo()
        {
            var info = new StringBuilder();
            
            try
            {
                info.AppendLine($"OS: {RuntimeInformation.OSDescription}");
                info.AppendLine($"Architecture: {RuntimeInformation.OSArchitecture}");
                info.AppendLine($"Framework: {RuntimeInformation.FrameworkDescription}");
                info.AppendLine($"Process Architecture: {RuntimeInformation.ProcessArchitecture}");
                info.AppendLine($"Machine Name: {Environment.MachineName}");
                info.AppendLine($"User Name: {Environment.UserName}");
                info.AppendLine($"Working Directory: {Environment.CurrentDirectory}");
                info.AppendLine($"Application Version: {Assembly.GetExecutingAssembly().GetName().Version}");
                info.AppendLine($"CLR Version: {Environment.Version}");
                info.AppendLine($"Processor Count: {Environment.ProcessorCount}");
                info.AppendLine($"System Page Size: {Environment.SystemPageSize}");
                
                // 内存信息
                var process = Process.GetCurrentProcess();
                info.AppendLine($"Working Set: {process.WorkingSet64 / 1024 / 1024} MB");
                info.AppendLine($"Private Memory: {process.PrivateMemorySize64 / 1024 / 1024} MB");
                info.AppendLine($"Virtual Memory: {process.VirtualMemorySize64 / 1024 / 1024} MB");
            }
            catch (Exception ex)
            {
                info.AppendLine($"Error getting system info: {ex.Message}");
            }

            return info.ToString();
        }

        /// <summary>
        /// 获取当前操作上下文
        /// </summary>
        private static string GetCurrentContext()
        {
            try
            {
                var context = new StringBuilder();
                context.AppendLine($"Thread ID: {System.Threading.Thread.CurrentThread.ManagedThreadId}");
                context.AppendLine($"Is Background Thread: {System.Threading.Thread.CurrentThread.IsBackground}");
                context.AppendLine($"Thread State: {System.Threading.Thread.CurrentThread.ThreadState}");
                
                // 获取调用堆栈（不包括异常堆栈）
                var stackTrace = new StackTrace(true);
                context.AppendLine("Call Stack:");
                for (int i = 0; i < Math.Min(stackTrace.FrameCount, 10); i++)
                {
                    var frame = stackTrace.GetFrame(i);
                    var method = frame?.GetMethod();
                    if (method != null)
                    {
                        context.AppendLine($"  at {method.DeclaringType?.FullName}.{method.Name}");
                    }
                }
                
                return context.ToString();
            }
            catch
            {
                return "Unable to get current context";
            }
        }

        /// <summary>
        /// 清理旧日志文件（保留最近7天）
        /// </summary>
        public static void CleanupOldLogs()
        {
            if (!_isInitialized) return;

            try
            {
                var cutoffDate = DateTime.Now.AddDays(-7);
                var logFiles = Directory.GetFiles(_logDirectory, "crash_*.log");
                
                foreach (var file in logFiles)
                {
                    var fileInfo = new FileInfo(file);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(file);
                    }
                }
            }
            catch
            {
                // 忽略清理失败
            }
        }
    }
}
