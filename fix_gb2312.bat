@echo off
chcp 936
echo 开始修复GB2312编码问题...

set "sourceFile=SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs"
set "backupFile=SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs.backup"

if not exist "%sourceFile%" (
    echo 错误：找不到文件 %sourceFile%
    pause
    exit /b 1
)

echo 备份原文件...
copy "%sourceFile%" "%backupFile%"

echo 转换编码为GB2312...
powershell -Command "& {[System.Text.Encoding]::RegisterProvider([System.Text.CodePagesEncodingProvider]::Instance); $content = Get-Content '%sourceFile%' -Raw -Encoding UTF8; $gb2312 = [System.Text.Encoding]::GetEncoding('GB2312'); [System.IO.File]::WriteAllText('%sourceFile%', $content, $gb2312)}"

echo 完成！
pause
