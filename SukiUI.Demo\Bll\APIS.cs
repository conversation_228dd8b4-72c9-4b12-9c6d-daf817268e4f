﻿using Newtonsoft.Json;

namespace SukiUI.Demo.Bll
{
    public class APIS
    {
        public static string GetServerTime()
        {

            string servertime = WebApiHelper.PostData("Api/Vehicle/GetServerTime", null, true);          

            var time = JsonConvert.DeserializeObject(servertime);

            return time.ToString();
        }
        /// <summary>
        /// 从API返回BaseDataModel列表
        /// </summary>
        /// <param name="uri">uri地址</param>
        /// <param name="isPost">是否为Post请求</param>
        /// <returns></returns>
        public static List<BaseDataModel> GetListDataFromApi(object data = null,string uri= "Api/SiHeYi/GetDataList", bool isPost=true)
        {
            
            var list = WebApiHelper.PostData(uri, data, isPost);
            var datalist = FsqlToBDM.ConvertToBaseDataModel(list);
            return datalist;          
           
        }
        public static string GetStringDataFromApi(object data = null, string uri = "Api/SiHeYi/GetDataList", bool isPost = true)
        {

            var list = WebApiHelper.PostData(uri, data, isPost);
            var result= FsqlToBDM.ConvertToString(list);
            return result;

        }
        //public static List<BaseDataModel> GetDataFromApi1(object data = null, string uri = "Api/SiHeYi/GetDataList", bool isPost = true)
        //{

        //    var list = WebApiHelper.PostData(uri, data, isPost);
        //    var datalist = FsqlToBDM.ConvertToBaseDataModel(list);
        //    return datalist;

        //}


        public static string shiJian = @"shiJian";
        public static string jiaXiao = @"jiaXiao";
        public static string chengJi = @"chengJi";
        public static string xueYuan = @"xueYuan";
        public static string kouFen = @"kouFen";
        public static string KaoShiYuan = @"kaoShiYuan";
        public static string cheliang = @"cheLiang";
        public static string softCheck = @"softCheck";
        public static string xueYuanByXinHao=@"xueYuanByXinHao";
        public static string kaoShiCiShu = @"kaoShiCiShu";
        public static  string kaoShiJieShu= @"kaoShiJieShu";
        public static string user = @"user";
        public static string checkLogin=@"checkLogin";
        public static string config=@"config";
    }
}
