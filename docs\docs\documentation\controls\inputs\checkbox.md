# CheckBox

A control used for collecting user's choices.

## Show

<img src="/controls/inputs/checkbox.gif"/>

## Example

```xml
<CheckBox Content="Option One" IsChecked="True" />
<CheckBox Content="Option Two" />
<CheckBox IsThreeState="True" Content="Option Three" />
```

## See Also

[Demo: SukiUI.Demo/Features/ControlsLibrary/TogglesView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/TogglesView.axaml)