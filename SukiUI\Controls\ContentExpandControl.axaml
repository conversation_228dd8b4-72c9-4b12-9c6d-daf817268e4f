<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:SukiUI.Controls">
    <ControlTheme x:Key="SukiContentExpandControl" TargetType="controls:ContentExpandControl">
        <Setter Property="Template">
            <ControlTemplate>
                <ContentPresenter Name="PART_ContentPresenter"
                                  Padding="{TemplateBinding Padding}"
                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                  Background="{TemplateBinding Background}"
                                  BorderBrush="{TemplateBinding BorderBrush}"
                                  BorderThickness="{TemplateBinding BorderThickness}"
                                  Content="{TemplateBinding Content}"
                                  ContentTemplate="{TemplateBinding ContentTemplate}" />
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type controls:ContentExpandControl}"
                  BasedOn="{StaticResource SukiContentExpandControl}"
                  TargetType="controls:ContentExpandControl" />
</ResourceDictionary>
