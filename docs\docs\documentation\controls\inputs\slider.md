# Slider

A control for displaying current value and intervals in range.

## Show

![slider](https://github.com/user-attachments/assets/80afe350-0032-41a8-b81c-5ff7acddf1e2)

## Example

```xml
<Slider IsSnapToTickEnabled="True"
        Maximum="100"
        Minimum="0"
        TickFrequency="1"
        Value="{Binding}" />
```

## See Also

[Demo: SukiUI.Demo/Features/ControlsLibrary/ProgressView.axaml](https://github.com/kikipoulet/SukiUI/blob/cc73e0ddc894cc6b0ae3e73d44eb19e4d3328043/SukiUI.Demo/Features/ControlsLibrary/ProgressView.axaml#L45C16-L45C17)
