<Styles x:Class="SukiUI.SukiTheme"
        xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:system="clr-namespace:System;assembly=netstandard">

    <SimpleTheme />

    <StyleInclude Source="avares://SukiUI/Theme/TextStyles.axaml"/>

    <StyleInclude Source="avares://SukiUI/Theme/ComboBoxStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/Colors.xaml" />

    <StyleInclude Source="avares://SukiUI/Theme/TextBoxStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/RichTextBoxStyles.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/BorderStyles.xaml" />

    <StyleInclude Source="avares://SukiUI/Theme/ProgressBarStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/ListBoxStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/TreeViewStyles.xaml" />

    <StyleInclude Source="avares://SukiUI/Theme/ToggleSwitchStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/RadioButtonStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/ToggleButtonStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/SliderStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/NumericUpDownStyles.xaml" />
    <StyleInclude Source="avares://SukiUI/Theme/DataGridStyle.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/ListBoxItemStyle.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/ComboBoxItemStyle.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/NotificationStyle.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/NotificationCardStyle.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/CheckBoxStyles.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/ScrollBarStyle.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/ScrollViewerStyles.axaml" />
    <StyleInclude Source="avares://SukiUI/Theme/ButtonLoadingStyles.axaml" />

    <StyleInclude Source="avares://SukiUI/Theme/PathIconStyles.axaml" />

    <Styles.Resources>
        <ResourceDictionary>

            <ResourceDictionary.ThemeDictionaries>
                <ResourceInclude x:Key="Light" Source="../ColorTheme/Light.axaml" />
                <ResourceInclude x:Key="Dark" Source="../ColorTheme/Dark.axaml" />
            </ResourceDictionary.ThemeDictionaries>

            <CornerRadius x:Key="SmallCornerRadius">8</CornerRadius>
            <CornerRadius x:Key="MediumCornerRadius">15</CornerRadius>

            <system:TimeSpan x:Key="LongAnimationDuration">0.5</system:TimeSpan>
            <system:TimeSpan x:Key="MediumAnimationDuration">0.35</system:TimeSpan>
            <system:TimeSpan x:Key="ShortAnimationDuration">0.2</system:TimeSpan>
            <Easing x:Key="MenuEasing">CircularEaseOut</Easing>

            <ResourceDictionary.MergedDictionaries>
                <ResourceInclude Source="avares://sukiUI/Theme/TextBlock.xaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/SplitButton.axaml" />
                <ResourceInclude Source="avares://SukiUI/Theme/SplitView.axaml" />
                <ResourceInclude Source="avares://SukiUI/Theme/DatePicker.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/ToolTipStyle.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Button.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/HyperlinkButton.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/PathIcon.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Expander.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/CalendarDatePickerStyle.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/TabControl.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/TabItem.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Separator.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/DropDownButton.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Menu.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/ContextMenu.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/MenuItem.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/FlyoutPresenter.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/MenuFlyoutPresenter.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/AutoCompleteBoxStyles.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/ManagedFileChooser.axaml" />

                <ResourceInclude Source="avares://sukiUI/Theme/TimePickerStyle.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Calendar/Calendar.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Calendar/CalendarButton.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Calendar/CalendarDayButton.axaml" />
                <ResourceInclude Source="avares://sukiUI/Theme/Calendar/CalendarItem.axaml" />

                <ResourceInclude Source="avares://sukiUI/Controls/SukiWindow.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/SukiToast.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/Hosts/SukiToastHost.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/Hosts/SukiDialogHost.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/SukiDialog.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/SukiSideMenu.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/SukiStackPage.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/SukiSideMenuItem.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/SukiTransitioningContentControl.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/Stepper.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/InfoBadge.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/InfoBar.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/GlassMorphism/GlassCard.axaml" />
                <ResourceInclude Source="avares://sukiUI/Controls/ContentExpandControl.axaml" />

                <ResourceInclude Source="avares://SukiUI/Locale/en-US.axaml" />
            </ResourceDictionary.MergedDictionaries>

        </ResourceDictionary>
    </Styles.Resources>
</Styles>