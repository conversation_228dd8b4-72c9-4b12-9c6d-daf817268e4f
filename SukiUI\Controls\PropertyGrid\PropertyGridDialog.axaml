<UserControl x:Class="SukiUI.Controls.PropertyGridDialog"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controls="clr-namespace:SukiUI.Controls"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controls:InstanceViewModel"
             Background="{DynamicResource SukiBackground}"
             mc:Ignorable="d">

    <Border Padding="0" Background="{DynamicResource SukiBackground}">
        <Panel>
            <ScrollViewer>
                <controls:PropertyGrid Item="{Binding}">
                    <controls:PropertyGrid.DataTemplates>

                        <DataTemplate DataType="controls:CategoryViewModel">
                            <controls:GlassCard Padding="15,15,15,5" Margin="0,10">
                                <Expander   Grid.IsSharedSizeScope="True"
                                            Header="{Binding DisplayName}"
                                            IsExpanded="True">
                                    <ItemsControl Margin="7,0,0,20" ItemsSource="{Binding Properties}" />
                                </Expander>
                            </controls:GlassCard>
                        </DataTemplate>
                        
                        <!--
                            replace the PropertyGridTemplateSelector with your own type or subclass it,
                            if you want to customize the datatemplates being used
                        -->

                        <controls:PropertyGridTemplateSelector />
                    </controls:PropertyGrid.DataTemplates>
                </controls:PropertyGrid>
            </ScrollViewer>
        </Panel>
    </Border>
</UserControl>
