﻿<Style xmlns="https://github.com/avaloniaui"
       xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
       xmlns:sys="clr-namespace:System;assembly=netstandard">
    <Style.Resources>

        <Color x:Key="MediumGray">#F4F4F4</Color>
        <Color x:Key="BackgroundGray">#fafafa</Color>
        <Color x:Key="BorderGray">#ebebeb</Color>

        <Color x:Key="ThemeAccentColor">#0063bb</Color>
        <Color x:Key="ThemeAccentColor2">#0063bb</Color>
        <Color x:Key="ThemeAccentColor3">#0063bb</Color>
        <Color x:Key="ThemeAccentColor4">#003f8e</Color>
        <Color x:Key="ThemeAccentBadgeColor">#300063bb</Color>

        <Color x:Key="ThemeCardColor">#FFFFFF</Color>
        <Color x:Key="ThemeBackgroundColor">#FFECECEC</Color>
        <Color x:Key="ThemeBorderLowColor">#FFF3F3F3</Color>
        <Color x:Key="ThemeBorderMidColor">#FFECECEC</Color>
        <Color x:Key="ThemeBorderHighColor">#FFAAAAAA</Color>

        <Color x:Key="ThemeControlLowColor">#FF868999</Color>
        <Color x:Key="ThemeControlMidColor">#FFF5F5F5</Color>
        <Color x:Key="ThemeControlMidHighColor">#FFC2C3C9</Color>
        <Color x:Key="ThemeControlHighColor">#FF686868</Color>
        <Color x:Key="ThemeControlVeryHighColor">#FF5B5B5B</Color>

        <Color x:Key="ThemeControlHighlightLowColor">#FFF0F0F0</Color>
        <Color x:Key="ThemeControlHighlightMidColor">#FFD0D0D0</Color>
        <Color x:Key="ThemeControlHighlightHighColor">#FF808080</Color>
        <Color x:Key="ThemeForegroundColor">#FF000000</Color>
        <Color x:Key="ThemeForegroundLowColor">#FF808080</Color>

        <Color x:Key="HighlightBadgeColor">#30fd7d00</Color>
        <Color x:Key="HighlightColor">#fd7d00</Color>
        <Color x:Key="HighlightHoverColor">#ed6d00</Color>
        <Color x:Key="HighlightForegroundColor">#FFFFFFFF</Color>
        <Color x:Key="ErrorColor">#FFFF0000</Color>
        <Color x:Key="ErrorLowColor">#AAFF0000</Color>

        <SolidColorBrush x:Key="ThemeCardBrush" Color="{DynamicResource ThemeCardColor}" />
        <SolidColorBrush x:Key="ThemeBackgroundBrush" Color="{DynamicResource ThemeBackgroundColor}" />
        <SolidColorBrush x:Key="ThemeBorderLowBrush" Color="{DynamicResource ThemeBorderLowColor}" />
        <SolidColorBrush x:Key="ThemeBorderMidBrush" Color="{DynamicResource ThemeBorderMidColor}" />
        <SolidColorBrush x:Key="ThemeBorderHighBrush" Color="#dedede" />
        <SolidColorBrush x:Key="ThemeControlLowBrush" Color="{DynamicResource ThemeControlLowColor}" />
        <SolidColorBrush x:Key="ThemeControlMidBrush" Color="{DynamicResource ThemeControlMidColor}" />
        <SolidColorBrush x:Key="ThemeControlMidHighBrush" Color="{DynamicResource ThemeControlMidHighColor}" />
        <SolidColorBrush x:Key="ThemeControlHighBrush" Color="{DynamicResource ThemeControlHighColor}" />
        <SolidColorBrush x:Key="ThemeControlVeryHighBrush" Color="{DynamicResource ThemeControlVeryHighColor}" />
        <SolidColorBrush x:Key="ThemeControlHighlightLowBrush" Color="{DynamicResource ThemeControlHighlightLowColor}" />
        <SolidColorBrush x:Key="ThemeControlHighlightMidBrush" Color="{DynamicResource ThemeControlHighlightMidColor}" />
        <SolidColorBrush x:Key="ThemeControlHighlightHighBrush" Color="{DynamicResource ThemeControlHighlightHighColor}" />
        <SolidColorBrush x:Key="ThemeForegroundBrush" Color="{DynamicResource ThemeForegroundColor}" />
        <SolidColorBrush x:Key="ThemeForegroundLowBrush" Color="{DynamicResource ThemeForegroundLowColor}" />
        <SolidColorBrush x:Key="ColorViewContentBackgroundBrush" Color="{DynamicResource SukiCardBackground}" />

        <SolidColorBrush x:Key="HighlightBrush" Color="{DynamicResource HighlightColor}" />
        <SolidColorBrush x:Key="HighlightBadgeBrush" Color="{DynamicResource HighlightBadgeColor}" />
        <SolidColorBrush x:Key="HighlightHoverBrush" Color="{DynamicResource HighlightHoverColor}" />
        <SolidColorBrush x:Key="HighlightForegroundBrush" Color="{DynamicResource HighlightForegroundColor}" />
        <SolidColorBrush x:Key="ThemeAccentBrush" Color="{DynamicResource SukiPrimaryColor}" />
        <SolidColorBrush x:Key="ThemeAccentBrush2" Color="{DynamicResource ThemeAccentColor2}" />
        <SolidColorBrush x:Key="ThemeAccentBrush3" Color="{DynamicResource ThemeAccentColor3}" />
        <SolidColorBrush x:Key="ThemeAccentBrush4" Color="{DynamicResource ThemeAccentColor4}" />
        <SolidColorBrush x:Key="ThemeAccentBadgeBrush" Color="{DynamicResource ThemeAccentBadgeColor}" />
        <SolidColorBrush x:Key="ErrorBrush" Color="{DynamicResource ErrorColor}" />
        <SolidColorBrush x:Key="ErrorLowBrush" Color="{DynamicResource ErrorLowColor}" />

        <SolidColorBrush x:Key="NotificationCardBackgroundBrush"
                         Opacity="0.75"
                         Color="#444444" />
        <SolidColorBrush x:Key="NotificationCardInformationBackgroundBrush"
                         Opacity="0.75"
                         Color="#007ACC" />
        <SolidColorBrush x:Key="NotificationCardSuccessBackgroundBrush"
                         Opacity="0.75"
                         Color="#1F9E45" />
        <SolidColorBrush x:Key="NotificationCardWarningBackgroundBrush"
                         Opacity="0.75"
                         Color="#FDB328" />
        <SolidColorBrush x:Key="NotificationCardErrorBackgroundBrush"
                         Opacity="0.75"
                         Color="#BD202C" />

        <SolidColorBrush x:Key="DatePickerFlyoutPresenterHighlightFill"
                         Opacity="0.4"
                         Color="{DynamicResource ThemeAccentColor}" />
        <SolidColorBrush x:Key="TimePickerFlyoutPresenterHighlightFill"
                         Opacity="0.4"
                         Color="{DynamicResource ThemeAccentColor}" />

        <SolidColorBrush x:Key="ThemeControlTransparentBrush" Color="Transparent" />

        <Thickness x:Key="ThemeBorderThickness">1,1,1,1</Thickness>
        <sys:Double x:Key="ThemeDisabledOpacity">0.5</sys:Double>

        <sys:Double x:Key="FontSizeSmall">13</sys:Double>
        <sys:Double x:Key="FontSizeNormal">14</sys:Double>
        <sys:Double x:Key="FontSizeLarge">14</sys:Double>

        <sys:Double x:Key="ScrollBarThickness">18</sys:Double>
        <sys:Double x:Key="ScrollBarThumbThickness">8</sys:Double>

        <sys:Double x:Key="IconElementThemeHeight">20</sys:Double>
        <sys:Double x:Key="IconElementThemeWidth">20</sys:Double>
    </Style.Resources>
</Style>