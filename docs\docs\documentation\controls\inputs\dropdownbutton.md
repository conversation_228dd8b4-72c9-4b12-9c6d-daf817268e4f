# DropDownButton

When you click on the `DropDownButton`, the custom content will pop up.

## Show

<img src="/controls/inputs/dropdownbutton.webp" height="300px" width="300px"/>

## Example

```xml
<DropDownButton Content="Click To Open">
    <DropDownButton.Flyout>
        <Flyout>
            <!-- Content -->
        </Flyout>
    </DropDownButton.Flyout>
</DropDownButton>
```

## See Also

[Demo: SukiUI.Demo/Features/ControlsLibrary/MiscView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/MiscView.axaml)