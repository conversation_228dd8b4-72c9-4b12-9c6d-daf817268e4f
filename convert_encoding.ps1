# Convert file encoding to GB2312
param(
    [string]$FilePath = "SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs"
)

Write-Host "Converting file to GB2312 encoding..." -ForegroundColor Green
Write-Host "File: $FilePath" -ForegroundColor Cyan

if (-not (Test-Path $FilePath)) {
    Write-Host "Error: File not found" -ForegroundColor Red
    exit 1
}

# Create backup
$backupPath = "$FilePath.backup"
Copy-Item $FilePath $backupPath
Write-Host "Backup created: $backupPath" -ForegroundColor Yellow

try {
    # Register encoding provider
    [System.Text.Encoding]::RegisterProvider([System.Text.CodePagesEncodingProvider]::Instance)
    
    # Read file as UTF-8
    $content = Get-Content $FilePath -Raw -Encoding UTF8
    
    # Get GB2312 encoder
    $gb2312 = [System.Text.Encoding]::GetEncoding("GB2312")
    
    # Convert and save as GB2312
    $bytes = $gb2312.GetBytes($content)
    [System.IO.File]::WriteAllBytes($FilePath, $bytes)
    
    Write-Host "Success: File converted to GB2312" -ForegroundColor Green
    
    # Verify
    $verifyContent = $gb2312.GetString([System.IO.File]::ReadAllBytes($FilePath))
    if ($verifyContent.Length -gt 0) {
        Write-Host "Verification: OK" -ForegroundColor Green
    }
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    # Restore backup
    Copy-Item $backupPath $FilePath -Force
    Write-Host "Backup restored" -ForegroundColor Yellow
    exit 1
}

Write-Host "Conversion completed successfully!" -ForegroundColor Green
