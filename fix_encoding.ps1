# PowerShell脚本：修复CarControlView.axaml.cs的编码问题
# 使用方法：在PowerShell中运行此脚本

$filePath = "SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs"
$backupPath = "SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs.backup"

Write-Host "开始修复编码问题..." -ForegroundColor Green

# 1. 备份原文件
if (Test-Path $filePath) {
    Copy-Item $filePath $backupPath
    Write-Host "已备份原文件到: $backupPath" -ForegroundColor Yellow
}

# 2. 读取文件内容（尝试GB2312编码）
try {
    # 注册GB2312编码提供程序
    [System.Text.Encoding]::RegisterProvider([System.Text.CodePagesEncodingProvider]::Instance)
    
    # 使用GB2312编码读取
    $gb2312 = [System.Text.Encoding]::GetEncoding("GB2312")
    $bytes = [System.IO.File]::ReadAllBytes($filePath)
    $content = $gb2312.GetString($bytes)
    
    Write-Host "成功使用GB2312编码读取文件" -ForegroundColor Green
    
    # 3. 保存为UTF-8编码
    $utf8 = New-Object System.Text.UTF8Encoding $true
    [System.IO.File]::WriteAllText($filePath, $content, $utf8)
    
    Write-Host "已转换为UTF-8编码并保存" -ForegroundColor Green
    Write-Host "修复完成！" -ForegroundColor Green
    
} catch {
    Write-Host "编码转换失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "请尝试在Visual Studio中手动转换编码" -ForegroundColor Yellow
}

# 4. 验证转换结果
Write-Host "`n验证转换结果..." -ForegroundColor Cyan
$newContent = Get-Content $filePath -Raw -Encoding UTF8
if ($newContent -match "[\u4e00-\u9fff]") {
    Write-Host "✓ 检测到中文字符，转换可能成功" -ForegroundColor Green
} else {
    Write-Host "⚠ 未检测到中文字符，可能需要手动处理" -ForegroundColor Yellow
}
