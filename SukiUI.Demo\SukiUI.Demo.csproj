﻿<Project Sdk="Microsoft.NET.Sdk">
  <PropertyGroup>
    <OutputType>WinExe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <BuiltInComInteropSupport>true</BuiltInComInteropSupport>
    <ApplicationManifest>app.manifest</ApplicationManifest>
    <AvaloniaUseCompiledBindingsByDefault>true</AvaloniaUseCompiledBindingsByDefault>
    <PackageIcon>生成证件照 (1).png</PackageIcon>   
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)' != 'Debug'">
    <!--<PublishAot>true</PublishAot>-->
    <PublishReadyToRun>true</PublishReadyToRun>
    <!--<InvariantGlobalization>true</InvariantGlobalization>-->
    <InvariantGlobalization>false</InvariantGlobalization>
  </PropertyGroup>

  <!--<ItemGroup>--><!--
    <AvaloniaXaml Remove="Features\CarControl\SensorSetView.axaml" />
  </ItemGroup>-->

  <ItemGroup>
    <PackageReference Include="Avalonia" />
    <PackageReference Include="Avalonia.AvaloniaEdit" />
    <PackageReference Include="Avalonia.Controls.ColorPicker" />
    <PackageReference Include="Avalonia.Desktop" />
    <PackageReference Include="Avalonia.Fonts.Inter" />
    <!--Condition below is needed to remove Avalonia.Diagnostics package from build output in Release configuration.-->
    <PackageReference Condition="'$(Configuration)' == 'Debug'" Include="Avalonia.Diagnostics" />
    <PackageReference Include="Avalonia.Themes.Fluent" />
    <PackageReference Include="AvaloniaEdit.TextMate" />
    <PackageReference Include="CommunityToolkit.Mvvm" />
    <PackageReference Include="Dock.Avalonia" />
    <PackageReference Include="Dock.Model" />
    <PackageReference Include="Dock.Model.Avalonia" />
    <PackageReference Include="Dock.Model.Mvvm" />
    <PackageReference Include="Dock.Serializer" />
    <PackageReference Include="Mapsui.Avalonia" />
    <PackageReference Include="Mapsui.Nts" />
    <PackageReference Include="Material.Icons.Avalonia" />
    <PackageReference Include="Microsoft.Extensions.Configuration" />
    <PackageReference Include="Microsoft.Extensions.Configuration.Json" />
    <PackageReference Include="Microsoft.Extensions.DependencyInjection" />
    <PackageReference Include="NetCoreServer" />
    <PackageReference Include="Polly" />
    <PackageReference Include="Polly.Extensions" />
    <PackageReference Include="RestSharp" />
    <PackageReference Include="Serilog" />
    <PackageReference Include="Serilog.Sinks.Console" />
    <PackageReference Include="Serilog.Sinks.File" />
    <PackageReference Include="ShowMeTheXaml.Avalonia" />
    <PackageReference Include="ShowMeTheXaml.Avalonia.Generator" />
    <PackageReference Include="Sini" />
    <PackageReference Include="System.Text.Json" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SukiUI.Dock\SukiUI.Dock.csproj" />
    <ProjectReference Include="..\SukiUI\SukiUI.csproj" />
  </ItemGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
    <None Remove="Assets\Car.png" />
    <None Remove="Assets\logo.png" />
    <None Remove="Assets\photo.png" />
    <None Remove="Assets\samllicon.png" />
    <AvaloniaResource Include="Assets\Car.png" />
    <AvaloniaResource Include="Assets\logo.png" />
    <AvaloniaResource Include="Assets\OIG.N5o-removebg-preview2.png" />
    <AvaloniaResource Include="Assets\OIG.N5o-removebg-preview1.png" />
    <None Remove="Assets\cat.jpeg" />
    <None Remove="Assets\cat-modified.png" />
    <AvaloniaResource Include="Assets\cat-modified.png" />
    <None Remove="Assets\desktopbackground.jpg" />
    <AvaloniaResource Include="Assets\desktopbackground.jpg" />
    <None Remove="Assets\Icons\icons8-note-48 (1).png" />
    <None Remove="Assets\Icons\mail.png" />
    <AvaloniaResource Include="Assets\Icons\mail.png" />
    <None Remove="Assets\Icons\notes.png" />
    <AvaloniaResource Include="Assets\Icons\notes.png" />
    <None Remove="Assets\Icons\Mail\icons8-trash-96.png" />
    <AvaloniaResource Include="Assets\Icons\Mail\icons8-trash-96.png" />
    <None Remove="Assets\Icons\Mail\icons8-mailbox-96.png" />
    <AvaloniaResource Include="Assets\Icons\Mail\icons8-mailbox-96.png" />
    <None Remove="Assets\Icons\Mail\icons8-folder-96.png" />
    <AvaloniaResource Include="Assets\Icons\Mail\icons8-folder-96.png" />
    <None Remove="Assets\Icons\Mail\icons8-draft-96.png" />
    <AvaloniaResource Include="Assets\Icons\Mail\icons8-draft-96.png" />
    <None Remove="Assets\Icons\Mail\icons8-user-male-96.png" />
    <AvaloniaResource Include="Assets\Icons\Mail\icons8-user-male-96.png" />
    <None Remove="Assets\Icons\Mail\icons8-female-user-96.png" />
    <AvaloniaResource Include="Assets\Icons\Mail\icons8-female-user-96.png" />
    <AvaloniaResource Include="Assets\OIG.N5o-removebg-preview.png" />
    <AvaloniaResource Include="Assets\photo.png" />
    <AvaloniaResource Include="Assets\samllicon.png" />
    
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Assets\space.sksl" />
    <EmbeddedResource Include="Assets\clouds.sksl" />
    <EmbeddedResource Include="Assets\weird.sksl" />
  </ItemGroup>

  <ItemGroup>
    <UpToDateCheckInput Remove="Features\ControlsLibrary\Effects\EffectsView.axaml" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <!-- 海康威视SDK库文件复制规则 - 从源目录复制到输出目录 -->
  <!-- Windows平台 - 复制bin目录下的所有SDK文件 -->
  <ItemGroup>
    <!-- 复制Windows SDK文件 -->
    <Content Include="bin\Debug\bin\**\*.*" Condition="Exists('bin\Debug\bin')">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>bin\%(RecursiveDir)%(Filename)%(Extension)</TargetPath>
    </Content>
  </ItemGroup>

  <!-- Linux平台 - 复制lib目录下的所有SDK文件 -->
  <ItemGroup>
    <!-- 复制Linux SDK文件 -->
    <Content Include="bin\Debug\lib\**\*.*" Condition="Exists('bin\Debug\lib')">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
      <TargetPath>Lib\%(RecursiveDir)%(Filename)%(Extension)</TargetPath>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <EmbeddedResource Include="Features\Effects\shaderart.sksl" />
  </ItemGroup>

  <ItemGroup>
    <None Include="C:\Users\<USER>\Downloads\生成证件照 (1).png">
      <Pack>True</Pack>
      <PackagePath>\</PackagePath>
    </None>
  </ItemGroup>

  <ItemGroup>
    <AdditionalFiles Update="Features\CarControl\SensorSetView.axaml">
      <SubType>Designer</SubType>
    </AdditionalFiles>
  </ItemGroup>

  

  

  <ItemGroup>
    <Compile Update="Features\ControlsLibrary\ExperimentalControls\ExperimentalView.axaml.cs">
      <DependentUpon>ExperimentalView.axaml</DependentUpon>
      <SubType>Code</SubType>
    </Compile>
   
  </ItemGroup>

  

</Project>
