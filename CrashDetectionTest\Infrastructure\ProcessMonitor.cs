using System;
using System.Diagnostics;
using System.Threading;
using System.Threading.Tasks;

namespace CrashDetectionTest.Infrastructure
{
    /// <summary>
    /// 进程监控器 - 监控进程资源使用情况
    /// </summary>
    public class ProcessMonitor : IDisposable
    {
        private readonly Timer _monitorTimer;
        private readonly Process _currentProcess;
        private bool _disposed = false;

        // 监控阈值
        private const long HIGH_MEMORY_THRESHOLD = 500 * 1024 * 1024; // 500MB
        private const int HIGH_THREAD_COUNT_THRESHOLD = 50;

        public ProcessMonitor(TimeSpan monitorInterval)
        {
            _currentProcess = Process.GetCurrentProcess();
            _monitorTimer = new Timer(MonitorCallback, null, TimeSpan.Zero, monitorInterval);
            Console.WriteLine("✓ 进程监控器已启动");
        }

        private void MonitorCallback(object? state)
        {
            if (_disposed) return;

            try
            {
                _currentProcess.Refresh();

                var workingSet = _currentProcess.WorkingSet64;
                var privateMemory = _currentProcess.PrivateMemorySize64;
                var threadCount = _currentProcess.Threads.Count;
                var cpuTime = _currentProcess.TotalProcessorTime;

                // 记录当前状态
                var statusMessage = $"[监控] 内存: {workingSet / 1024 / 1024}MB, " +
                                  $"私有内存: {privateMemory / 1024 / 1024}MB, " +
                                  $"线程数: {threadCount}, " +
                                  $"CPU时间: {cpuTime.TotalSeconds:F2}s";

                Console.WriteLine(statusMessage);

                // 检查异常情况
                CheckForAnomalies(workingSet, threadCount);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 进程监控异常: {ex.Message}");
            }
        }

        private void CheckForAnomalies(long workingSet, int threadCount)
        {
            if (workingSet > HIGH_MEMORY_THRESHOLD)
            {
                var warning = $"⚠️ 高内存使用警告: {workingSet / 1024 / 1024}MB (阈值: {HIGH_MEMORY_THRESHOLD / 1024 / 1024}MB)";
                Console.WriteLine(warning);
                CrashLogger.LogException(
                    new InvalidOperationException($"High memory usage detected: {workingSet / 1024 / 1024}MB"),
                    "ProcessMonitor - High Memory Usage"
                );
            }

            if (threadCount > HIGH_THREAD_COUNT_THRESHOLD)
            {
                var warning = $"⚠️ 高线程数警告: {threadCount} (阈值: {HIGH_THREAD_COUNT_THRESHOLD})";
                Console.WriteLine(warning);
                CrashLogger.LogException(
                    new InvalidOperationException($"High thread count detected: {threadCount}"),
                    "ProcessMonitor - High Thread Count"
                );
            }
        }

        /// <summary>
        /// 获取当前进程快照
        /// </summary>
        public ProcessSnapshot GetSnapshot()
        {
            _currentProcess.Refresh();
            return new ProcessSnapshot
            {
                Timestamp = DateTime.Now,
                ProcessId = _currentProcess.Id,
                ProcessName = _currentProcess.ProcessName,
                WorkingSet = _currentProcess.WorkingSet64,
                PrivateMemory = _currentProcess.PrivateMemorySize64,
                VirtualMemory = _currentProcess.VirtualMemorySize64,
                ThreadCount = _currentProcess.Threads.Count,
                TotalProcessorTime = _currentProcess.TotalProcessorTime,
                StartTime = _currentProcess.StartTime
            };
        }

        public void Dispose()
        {
            if (!_disposed)
            {
                _monitorTimer?.Dispose();
                _currentProcess?.Dispose();
                _disposed = true;
                Console.WriteLine("✓ 进程监控器已停止");
            }
        }
    }

    /// <summary>
    /// 进程快照数据
    /// </summary>
    public class ProcessSnapshot
    {
        public DateTime Timestamp { get; set; }
        public int ProcessId { get; set; }
        public string ProcessName { get; set; } = string.Empty;
        public long WorkingSet { get; set; }
        public long PrivateMemory { get; set; }
        public long VirtualMemory { get; set; }
        public int ThreadCount { get; set; }
        public TimeSpan TotalProcessorTime { get; set; }
        public DateTime StartTime { get; set; }

        public override string ToString()
        {
            return $"[{Timestamp:HH:mm:ss}] PID:{ProcessId} 内存:{WorkingSet / 1024 / 1024}MB " +
                   $"线程:{ThreadCount} CPU:{TotalProcessorTime.TotalSeconds:F2}s";
        }
    }
}
