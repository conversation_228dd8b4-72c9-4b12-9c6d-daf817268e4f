using System;
using System.IO;
using System.Reflection;
using System.Runtime.InteropServices;
using Serilog;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// 统一原生库管理器
    /// 负责跨平台动态库加载 (Windows/Linux/macOS)
    /// </summary>
    public static class NativeLibraryManager
    {
        private static bool _isInitialized = false;
        private static bool _isLibraryLoaded = false;
        private static readonly object _lockObject = new object();

        /// <summary>
        /// 获取库管理器是否已初始化
        /// </summary>
        public static bool IsInitialized => _isInitialized;

        /// <summary>
        /// 初始化原生库管理器
        /// 注册DLL导入解析器以支持跨平台库加载
        /// </summary>
        public static void Initialize()
        {
            lock (_lockObject)
            {
                if (_isInitialized)
                {
                    Log.Debug("NativeLibraryManager: 已经初始化，跳过重复初始化");
                    return;
                }

                try
                {
                    Log.Information("NativeLibraryManager: 开始初始化跨平台库管理器");
                    Log.Information($"NativeLibraryManager: 当前平台: {GetCurrentPlatform()}");
                    Log.Information($"NativeLibraryManager: 应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");

                    // 第一步：预加载依赖库
                    PreloadDependencyLibraries();

                    // 第二步：设置SDK初始化配置（Linux平台）
                    ConfigureSDKInitialization();

                    // 第三步：注册DLL导入解析器
                    NativeLibrary.SetDllImportResolver(typeof(NativeLibraryManager).Assembly, DllImportResolver);

                    _isInitialized = true;
                    Log.Information("NativeLibraryManager: 跨平台库解析器初始化成功");
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "NativeLibraryManager: 初始化失败");
                    throw;
                }
            }
        }

        /// <summary>
        /// DLL导入解析器 - 根据平台自动选择正确的库文件
        /// </summary>
        /// <param name="libraryName">库名称</param>
        /// <param name="assembly">程序集</param>
        /// <param name="searchPath">搜索路径</param>
        /// <returns>库句柄</returns>
        private static IntPtr DllImportResolver(string libraryName, Assembly assembly, DllImportSearchPath? searchPath)
        {
            try
            {
                // 只处理HCNetSDK相关的库
                if (!libraryName.Contains("HCNetSDK"))
                {
                    Log.Debug($"NativeLibraryManager: 跳过非HCNetSDK库: {libraryName}");
                    return IntPtr.Zero; // 让系统使用默认解析
                }

                Log.Debug($"NativeLibraryManager: 开始解析库: {libraryName}");

                lock (_lockObject)
                {
                    if (_isLibraryLoaded)
                    {
                        // 库已加载，尝试获取已加载的库句柄
                        var loadedLibraryPath = GetNativeLibraryPath("HCNetSDK");
                        if (NativeLibrary.TryLoad(loadedLibraryPath, out IntPtr handle))
                        {
                            Log.Debug($"NativeLibraryManager: 返回已加载的库句柄: {loadedLibraryPath}");
                            return handle;
                        }
                    }

                    // 获取平台特定的库文件路径
                    string libraryPath = GetNativeLibraryPath("HCNetSDK");
                    
                    if (!File.Exists(libraryPath))
                    {
                        var errorMsg = $"NativeLibraryManager: 库文件不存在: {libraryPath}";
                        Log.Error(errorMsg);
                        
                        // 提供详细的错误信息和建议
                        var suggestion = GetLibraryNotFoundSuggestion();
                        Log.Error($"NativeLibraryManager: 建议: {suggestion}");
                        
                        throw new FileNotFoundException(errorMsg);
                    }

                    // 加载原生库
                    IntPtr libraryHandle = NativeLibrary.Load(libraryPath);
                    _isLibraryLoaded = true;
                    
                    Log.Information($"NativeLibraryManager: 成功加载库文件: {libraryPath}");
                    return libraryHandle;
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, $"NativeLibraryManager: 加载库失败 - {libraryName}");
                throw;
            }
        }

        /// <summary>
        /// 根据当前平台获取原生库文件路径
        /// </summary>
        /// <param name="libraryName">库名称</param>
        /// <returns>完整的库文件路径</returns>
        private static string GetNativeLibraryPath(string libraryName)
        {
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
            {
                var path = Path.Combine(baseDir, "bin", $"{libraryName}.dll");
                Log.Debug($"NativeLibraryManager: Windows库路径: {path}");
                return path;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                //var path = Path.Combine(baseDir, "Lib", $"lib{libraryName}.so");
                //Linux平台使用小写库名
                var path = Path.Combine(baseDir, "Lib", $"lib{libraryName.ToLower()}.so");
                Log.Debug($"NativeLibraryManager: Linux库路径: {path}");
                return path;
            }
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
            {
                var path = Path.Combine(baseDir, "Lib", $"lib{libraryName}.dylib");
                Log.Debug($"NativeLibraryManager: macOS库路径: {path}");
                return path;
            }
            else
            {
                var errorMsg = $"NativeLibraryManager: 不支持的平台: {RuntimeInformation.OSDescription}";
                Log.Error(errorMsg);
                throw new PlatformNotSupportedException(errorMsg);
            }
        }

        /// <summary>
        /// 获取当前平台名称
        /// </summary>
        /// <returns>平台名称</returns>
        private static string GetCurrentPlatform()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                return "Windows";
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                return "Linux";
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                return "macOS";
            else
                return "Unknown";
        }

        /// <summary>
        /// 获取库文件未找到时的建议信息
        /// </summary>
        /// <returns>建议信息</returns>
        private static string GetLibraryNotFoundSuggestion()
        {
            var platform = GetCurrentPlatform();
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            
            return platform switch
            {
                "Windows" => $"请确保 HCNetSDK.dll 文件存在于 {Path.Combine(baseDir, "bin")} 目录中",
                "Linux" => $"请确保 libHCNetSDK.so 文件存在于 {Path.Combine(baseDir, "Lib")} 目录中，并具有执行权限",
                "macOS" => $"请确保 libHCNetSDK.dylib 文件存在于 {Path.Combine(baseDir, "Lib")} 目录中",
                _ => "请检查库文件是否存在于正确的目录中"
            };
        }

        /// <summary>
        /// 获取当前加载的库信息
        /// </summary>
        /// <returns>库加载状态信息</returns>
        public static string GetLibraryInfo()
        {
            try
            {
                var platform = GetCurrentPlatform();
                var libraryPath = GetNativeLibraryPath("HCNetSDK");
                var exists = File.Exists(libraryPath);
                
                return $"平台: {platform}, 库路径: {libraryPath}, 文件存在: {exists}, 已加载: {_isLibraryLoaded}, 已初始化: {_isInitialized}";
            }
            catch (Exception ex)
            {
                return $"获取库信息失败: {ex.Message}";
            }
        }

        /// <summary>
        /// 检查库文件是否存在
        /// </summary>
        /// <returns>库文件是否存在</returns>
        public static bool CheckLibraryExists()
        {
            try
            {
                var result = CheckAllLibraryFiles();
                return result.IsAllLibrariesPresent;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "NativeLibraryManager: 检查库文件时发生异常");
                return false;
            }
        }

        /// <summary>
        /// 检查所有必需的库文件
        /// </summary>
        /// <returns>详细的检查结果</returns>
        public static LibraryCheckResult CheckAllLibraryFiles()
        {
            var result = new LibraryCheckResult();
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;

            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    result = CheckWindowsLibraries(baseDir);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    result = CheckLinuxLibraries(baseDir);
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    result = CheckMacOSLibraries(baseDir);
                }
                else
                {
                    result.ErrorMessage = $"不支持的平台: {RuntimeInformation.OSDescription}";
                }

                Log.Information($"NativeLibraryManager: 库文件检查完成 - 总计: {result.TotalFiles}, 存在: {result.ExistingFiles}, 缺失: {result.MissingFiles}");

                if (result.MissingLibraries.Count > 0)
                {
                    Log.Warning($"NativeLibraryManager: 缺失的库文件: {string.Join(", ", result.MissingLibraries)}");
                }

                return result;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "NativeLibraryManager: 检查库文件时发生异常");
                result.ErrorMessage = ex.Message;
                return result;
            }
        }

        /// <summary>
        /// 获取详细的系统和库信息
        /// </summary>
        /// <returns>详细信息字符串</returns>
        public static string GetDetailedSystemInfo()
        {
            var info = new System.Text.StringBuilder();
            
            info.AppendLine("=== NativeLibraryManager 详细信息 ===");
            info.AppendLine($"初始化状态: {_isInitialized}");
            info.AppendLine($"库加载状态: {_isLibraryLoaded}");
            info.AppendLine($"当前平台: {GetCurrentPlatform()}");
            info.AppendLine($"操作系统: {RuntimeInformation.OSDescription}");
            info.AppendLine($"系统架构: {RuntimeInformation.ProcessArchitecture}");
            info.AppendLine($"运行时标识符: {RuntimeInformation.RuntimeIdentifier}");
            info.AppendLine($"应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
            
            try
            {
                var libraryPath = GetNativeLibraryPath("HCNetSDK");
                var exists = File.Exists(libraryPath);
                info.AppendLine($"预期库路径: {libraryPath}");
                info.AppendLine($"库文件存在: {exists}");
                
                if (exists)
                {
                    var fileInfo = new FileInfo(libraryPath);
                    info.AppendLine($"库文件大小: {fileInfo.Length} 字节");
                    info.AppendLine($"库文件修改时间: {fileInfo.LastWriteTime}");
                }
            }
            catch (Exception ex)
            {
                info.AppendLine($"获取库文件信息时发生异常: {ex.Message}");
            }
            
            return info.ToString();
        }

        /// <summary>
        /// 检查Windows平台的库文件
        /// </summary>
        /// <param name="baseDir">应用程序基础目录</param>
        /// <returns>检查结果</returns>
        private static LibraryCheckResult CheckWindowsLibraries(string baseDir)
        {
            var result = new LibraryCheckResult { Platform = "Windows" };
            var binDir = Path.Combine(baseDir, "bin");
            var comDir = Path.Combine(binDir, "HCNetSDKCom");

            // 定义Windows平台必需的库文件（根据官方文档）
            var requiredLibraries = new Dictionary<string, (string description, bool isRequired)>
            {
                // 必需的主要库文件
                { "HCNetSDK.dll", ("主SDK库", true) },
                { "HCCore.dll", ("核心库", true) },
                { "PlayCtrl.dll", ("播放控制库", true) },
                { "SuperRender.dll", ("渲染库", true) },
                { "AudioRender.dll", ("音频渲染库", true) },

                // 必需的SSL和加密库
                { "libssl-1_1-x64.dll", ("SSL库", true) },
                { "libcrypto-1_1-x64.dll", ("加密库", true) },

                // 必需的依赖库
                { "hlog.dll", ("日志库", true) },
                { "hpr.dll", ("性能库", true) },
                { "zlib1.dll", ("压缩库", true) },

                // 可选库文件（存在更好，但不是必需的）
                { "OpenAL32.dll", ("音频库", false) },
                { "GdiPlus.dll", ("图形库", false) },
                { "HXVA.dll", ("视频加速库", false) },
                { "HmMerge.dll", ("合并库", false) },
                { "MP_Render.dll", ("媒体渲染库", false) },
                { "NPQos.dll", ("网络QoS库", false) },
                { "YUVProcess.dll", ("YUV处理库", false) },
                { "libmmd.dll", ("数学库", false) }
            };

            // 检查主要库文件
            foreach (var lib in requiredLibraries)
            {
                var libPath = Path.Combine(binDir, lib.Key);
                var exists = File.Exists(libPath);

                result.LibraryDetails.Add(new LibraryInfo
                {
                    Name = lib.Key,
                    Description = lib.Value.description,
                    ExpectedPath = libPath,
                    Exists = exists,
                    IsRequired = lib.Value.isRequired
                });

                if (exists)
                {
                    result.ExistingFiles++;
                    Log.Debug($"✓ {lib.Key}: 存在");
                }
                else
                {
                    // 只有必需库缺失时才添加到缺失列表
                    if (lib.Value.isRequired)
                    {
                        result.MissingLibraries.Add(lib.Key);
                        Log.Warning($"✗ {lib.Key}: 缺失 [必需]");
                    }
                    else
                    {
                        Log.Debug($"- {lib.Key}: 缺失 [可选]");
                    }
                }
                result.TotalFiles++;
            }

            // 检查HCNetSDKCom组件库
            result.ComponentLibraryPath = comDir;
            result.ComponentLibraryExists = Directory.Exists(comDir);

            if (result.ComponentLibraryExists)
            {
                var comFiles = Directory.GetFiles(comDir, "*.dll");
                result.ComponentLibraryCount = comFiles.Length;
                Log.Debug($"✓ HCNetSDKCom目录存在，包含 {comFiles.Length} 个组件库");

                // 检查关键组件库
                var keyComponents = new[] { "HCAlarm.dll", "HCPreview.dll", "HCPlayBack.dll", "HCDisplay.dll" };
                foreach (var component in keyComponents)
                {
                    var componentPath = Path.Combine(comDir, component);
                    var exists = File.Exists(componentPath);
                    result.LibraryDetails.Add(new LibraryInfo
                    {
                        Name = $"HCNetSDKCom/{component}",
                        Description = "扩展组件库（可选）",
                        ExpectedPath = componentPath,
                        Exists = exists,
                        IsRequired = false  // 标记为可选
                    });

                    if (exists)
                        result.ExistingFiles++;
                    else
                    {
                        // 只记录为调试信息，不影响整体状态
                        Log.Debug($"可选组件库不存在: {component}");
                    }
                    result.TotalFiles++;
                }
            }
            else
            {
                result.MissingLibraries.Add("HCNetSDKCom目录");
                Log.Warning("✗ HCNetSDKCom目录不存在");
            }

            return result;
        }

        /// <summary>
        /// 检查Linux平台的库文件
        /// </summary>
        /// <param name="baseDir">应用程序基础目录</param>
        /// <returns>检查结果</returns>
        private static LibraryCheckResult CheckLinuxLibraries(string baseDir)
        {
            var result = new LibraryCheckResult { Platform = "Linux" };
            var libDir = Path.Combine(baseDir, "Lib");
            var comDir = Path.Combine(libDir, "HCNetSDKCom");

            // 定义Linux平台必需的库文件（根据官方文档）
            var requiredLibraries = new Dictionary<string, (string description, bool isRequired)>
            {
                // 必需的主要库文件
                { "libhcnetsdk.so", ("主SDK库", true) },
                { "libHCCore.so", ("核心库", true) },
                { "libPlayCtrl.so", ("播放控制库", true) },
                { "libSuperRender.so", ("渲染库", true) },
                { "libAudioRender.so", ("音频渲染库", true) },

                // 必需的SSL和加密库（至少需要一个版本）
                { "libssl.so", ("SSL库", true) },
                { "libssl.so.1.1", ("SSL库(版本1.1)", false) },
                { "libcrypto.so", ("加密库", true) },
                { "libcrypto.so.1.0.0", ("加密库(版本1.0.0)", false) },
                { "libcrypto.so.1.1", ("加密库(版本1.1)", false) },

                // 必需的依赖库
                { "libhpr.so", ("性能库", true) },
                { "libz.so", ("压缩库", true) },

                // 可选库文件
                { "libNPQos.so", ("网络QoS库", false) },
                { "libopenal.so.1", ("音频库", false) }
            };

            // 检查主要库文件
            foreach (var lib in requiredLibraries)
            {
                var libPath = Path.Combine(libDir, lib.Key);
                var exists = File.Exists(libPath);

                result.LibraryDetails.Add(new LibraryInfo
                {
                    Name = lib.Key,
                    Description = lib.Value.description,
                    ExpectedPath = libPath,
                    Exists = exists,
                    IsRequired = lib.Value.isRequired
                });

                if (exists)
                {
                    result.ExistingFiles++;
                    Log.Debug($"✓ {lib.Key}: 存在");
                }
                else
                {
                    // 只有必需库缺失时才添加到缺失列表
                    if (lib.Value.isRequired)
                    {
                        result.MissingLibraries.Add(lib.Key);
                        Log.Warning($"✗ {lib.Key}: 缺失 [必需]");
                    }
                    else
                    {
                        Log.Debug($"- {lib.Key}: 缺失 [可选]");
                    }
                }
                result.TotalFiles++;
            }

            // 检查HCNetSDKCom组件库
            result.ComponentLibraryPath = comDir;
            result.ComponentLibraryExists = Directory.Exists(comDir);

            if (result.ComponentLibraryExists)
            {
                var comFiles = Directory.GetFiles(comDir, "*.so");
                result.ComponentLibraryCount = comFiles.Length;
                Log.Debug($"✓ HCNetSDKCom目录存在，包含 {comFiles.Length} 个组件库");

                // 检查关键组件库（标记为可选）
                var keyComponents = new[] { "libHCAlarm.so", "libHCPreview.so", "libHCPlayBack.so", "libHCDisplay.so" };
                foreach (var component in keyComponents)
                {
                    var componentPath = Path.Combine(comDir, component);
                    var exists = File.Exists(componentPath);
                    result.LibraryDetails.Add(new LibraryInfo
                    {
                        Name = $"HCNetSDKCom/{component}",
                        Description = "扩展组件库（可选）",
                        ExpectedPath = componentPath,
                        Exists = exists,
                        IsRequired = false  // 标记为可选
                    });

                    if (exists)
                        result.ExistingFiles++;
                    else
                    {
                        // 只记录为调试信息，不影响整体状态
                        Log.Debug($"可选组件库不存在: {component}");
                    }
                    result.TotalFiles++;
                }
            }
            else
            {
                result.MissingLibraries.Add("HCNetSDKCom目录");
                Log.Warning("✗ HCNetSDKCom目录不存在");
            }

            return result;
        }

        /// <summary>
        /// 检查macOS平台的库文件
        /// </summary>
        /// <param name="baseDir">应用程序基础目录</param>
        /// <returns>检查结果</returns>
        private static LibraryCheckResult CheckMacOSLibraries(string baseDir)
        {
            var result = new LibraryCheckResult { Platform = "macOS" };
            var libDir = Path.Combine(baseDir, "Lib");

            // macOS平台的库文件检查（基于Linux的结构，但使用.dylib扩展名）
            var requiredLibraries = new Dictionary<string, (string description, bool isRequired)>
            {
                { "libHCNetSDK.dylib", ("主SDK库", true) },
                { "libHCCore.dylib", ("核心库", true) },
                { "libPlayCtrl.dylib", ("播放控制库", true) },
                { "libSuperRender.dylib", ("渲染库", true) },
                { "libAudioRender.dylib", ("音频渲染库", true) }
            };

            foreach (var lib in requiredLibraries)
            {
                var libPath = Path.Combine(libDir, lib.Key);
                var exists = File.Exists(libPath);

                result.LibraryDetails.Add(new LibraryInfo
                {
                    Name = lib.Key,
                    Description = lib.Value.description,
                    ExpectedPath = libPath,
                    Exists = exists,
                    IsRequired = lib.Value.isRequired
                });

                if (exists)
                {
                    result.ExistingFiles++;
                    Log.Debug($"✓ {lib.Key}: 存在");
                }
                else
                {
                    if (lib.Value.isRequired)
                    {
                        result.MissingLibraries.Add(lib.Key);
                        Log.Warning($"✗ {lib.Key}: 缺失 [必需]");
                    }
                }
                result.TotalFiles++;
            }

            return result;
        }

        /// <summary>
        /// 判断是否为必需的库文件（根据官方文档要求）
        /// </summary>
        /// <param name="libraryName">库文件名</param>
        /// <returns>是否必需</returns>
        private static bool IsRequiredLibrary(string libraryName)
        {
            // 根据官方文档，这些是必需的核心库文件
            var requiredLibs = new[]
            {
                // 主SDK库
                "HCNetSDK.dll", "libhcnetsdk.so", "libHCNetSDK.dylib",
                // 核心库
                "HCCore.dll", "libHCCore.so", "libHCCore.dylib",
                // 播放控制库
                "PlayCtrl.dll", "libPlayCtrl.so", "libPlayCtrl.dylib",
                // 渲染库
                "SuperRender.dll", "libSuperRender.so", "libSuperRender.dylib",
                // 音频渲染库
                "AudioRender.dll", "libAudioRender.so", "libAudioRender.dylib",
                // SSL和加密库（必需）
                "libssl-1_1-x64.dll", "libssl.so", "libcrypto-1_1-x64.dll", "libcrypto.so",
                // 其他必需依赖
                "hlog.dll", "hpr.dll", "libhpr.so", "zlib1.dll", "libz.so"
            };

            return requiredLibs.Any(req => libraryName.Equals(req, StringComparison.OrdinalIgnoreCase));
        }

        /// <summary>
        /// 预加载依赖库文件
        /// 按照正确的依赖顺序加载库文件，确保SDK能正常工作
        /// </summary>
        private static void PreloadDependencyLibraries()
        {
            try
            {
                Log.Information("NativeLibraryManager: 开始预加载依赖库");

                if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                {
                    PreloadWindowsLibraries();
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    PreloadLinuxLibraries();
                }
                else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                {
                    PreloadMacOSLibraries();
                }

                Log.Information("NativeLibraryManager: 依赖库预加载完成");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "NativeLibraryManager: 预加载依赖库失败");
                throw;
            }
        }

        /// <summary>
        /// 预加载Windows平台的依赖库
        /// 按照依赖关系顺序加载，避免加载失败
        /// </summary>
        private static void PreloadWindowsLibraries()
        {
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            var binDir = Path.Combine(baseDir, "bin");

            // Windows平台依赖库加载顺序（重要：必须按依赖关系顺序）
            var loadOrder = new[]
            {
                // 第一层：基础依赖库（无其他依赖）
                "zlib1.dll",
                "libcrypto-1_1-x64.dll",
                "libssl-1_1-x64.dll",
                "hlog.dll",

                // 第二层：依赖第一层的库
                "hpr.dll",

                // 第三层：核心库（依赖前面的库）
                "HCCore.dll",

                // 第四层：功能库（依赖核心库）
                "AudioRender.dll",
                "SuperRender.dll",
                "PlayCtrl.dll",

                // 第五层：主SDK库（依赖所有前面的库）
                "HCNetSDK.dll"
            };

            int loadedCount = 0;
            foreach (var libraryName in loadOrder)
            {
                var libraryPath = Path.Combine(binDir, libraryName);

                if (!File.Exists(libraryPath))
                {
                    Log.Warning($"NativeLibraryManager: Windows依赖库不存在: {libraryPath}");
                    continue;
                }

                try
                {
                    // 尝试预加载库
                    var handle = NativeLibrary.Load(libraryPath);
                    if (handle != IntPtr.Zero)
                    {
                        loadedCount++;
                        Log.Debug($"✓ 预加载Windows库: {libraryName}");
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, $"NativeLibraryManager: 预加载Windows库失败: {libraryName}");
                    // 继续加载其他库，不中断整个过程
                }
            }

            Log.Information($"NativeLibraryManager: Windows平台预加载完成，成功加载 {loadedCount}/{loadOrder.Length} 个库");
        }

        /// <summary>
        /// 预加载Linux平台的依赖库
        /// Linux平台主要通过设置路径，让系统自动解析依赖
        /// </summary>
        private static void PreloadLinuxLibraries()
        {
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            var libDir = Path.Combine(baseDir, "Lib");

            // Linux平台依赖库加载顺序
            var loadOrder = new[]
            {
                // 基础依赖库
                "libz.so",
                "libcrypto.so",
                "libssl.so",
                "libhpr.so",

                // 核心库
                "libHCCore.so",

                // 功能库
                "libAudioRender.so",
                "libSuperRender.so",
                "libPlayCtrl.so",

                // 主SDK库
                "libhcnetsdk.so"
            };

            int loadedCount = 0;
            foreach (var libraryName in loadOrder)
            {
                var libraryPath = Path.Combine(libDir, libraryName);

                if (!File.Exists(libraryPath))
                {
                    Log.Warning($"NativeLibraryManager: Linux依赖库不存在: {libraryPath}");
                    continue;
                }

                try
                {
                    // Linux平台尝试预加载
                    var handle = NativeLibrary.Load(libraryPath);
                    if (handle != IntPtr.Zero)
                    {
                        loadedCount++;
                        Log.Debug($"✓ 预加载Linux库: {libraryName}");
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, $"NativeLibraryManager: 预加载Linux库失败: {libraryName}");
                    // Linux平台依赖系统库管理，预加载失败不一定影响最终使用
                }
            }

            Log.Information($"NativeLibraryManager: Linux平台预加载完成，成功加载 {loadedCount}/{loadOrder.Length} 个库");
        }

        /// <summary>
        /// 预加载macOS平台的依赖库
        /// </summary>
        private static void PreloadMacOSLibraries()
        {
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            var libDir = Path.Combine(baseDir, "Lib");

            // macOS平台依赖库加载顺序（类似Linux但使用.dylib）
            var loadOrder = new[]
            {
                "libHCCore.dylib",
                "libAudioRender.dylib",
                "libSuperRender.dylib",
                "libPlayCtrl.dylib",
                "libHCNetSDK.dylib"
            };

            int loadedCount = 0;
            foreach (var libraryName in loadOrder)
            {
                var libraryPath = Path.Combine(libDir, libraryName);

                if (!File.Exists(libraryPath))
                {
                    Log.Warning($"NativeLibraryManager: macOS依赖库不存在: {libraryPath}");
                    continue;
                }

                try
                {
                    var handle = NativeLibrary.Load(libraryPath);
                    if (handle != IntPtr.Zero)
                    {
                        loadedCount++;
                        Log.Debug($"✓ 预加载macOS库: {libraryName}");
                    }
                }
                catch (Exception ex)
                {
                    Log.Warning(ex, $"NativeLibraryManager: 预加载macOS库失败: {libraryName}");
                }
            }

            Log.Information($"NativeLibraryManager: macOS平台预加载完成，成功加载 {loadedCount}/{loadOrder.Length} 个库");
        }

        /// <summary>
        /// 配置SDK初始化参数
        /// 主要用于Linux平台设置组件库和SSL库路径
        /// </summary>
        private static void ConfigureSDKInitialization()
        {
            try
            {
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    ConfigureLinuxSDKPaths();
                }
                else
                {
                    Log.Debug("NativeLibraryManager: 当前平台无需特殊SDK配置");
                }
            }
            catch (Exception ex)
            {
                Log.Error(ex, "NativeLibraryManager: 配置SDK初始化参数失败");
                throw;
            }
        }

        /// <summary>
        /// 配置Linux平台的SDK路径
        /// 根据官方文档调用NET_DVR_SetSDKInitCfg设置路径
        /// </summary>
        private static void ConfigureLinuxSDKPaths()
        {
            var baseDir = AppDomain.CurrentDomain.BaseDirectory;
            var libDir = Path.Combine(baseDir, "Lib");
            var comDir = Path.Combine(libDir, "HCNetSDKCom");

            Log.Information("NativeLibraryManager: 开始配置Linux SDK路径");

            try
            {
                // 注意：这些调用需要在NET_DVR_Init()之前执行
                // 但由于我们在库加载阶段，实际的NET_DVR_SetSDKInitCfg调用
                // 需要在SDK库加载后才能进行

                // 1. 设置HCNetSDKCom组件库路径
                if (Directory.Exists(comDir))
                {
                    Log.Information($"NativeLibraryManager: HCNetSDKCom路径: {comDir}");
                    // 实际的NET_DVR_SetSDKInitCfg调用将在SDK初始化时进行
                }
                else
                {
                    Log.Warning($"NativeLibraryManager: HCNetSDKCom目录不存在: {comDir}");
                }

                // 2. 检查SSL库路径
                var sslPath = Path.Combine(libDir, "libssl.so");
                var cryptoPath = Path.Combine(libDir, "libcrypto.so");

                if (File.Exists(sslPath))
                {
                    Log.Information($"NativeLibraryManager: SSL库路径: {sslPath}");
                }
                else
                {
                    Log.Warning($"NativeLibraryManager: SSL库不存在: {sslPath}");
                }

                if (File.Exists(cryptoPath))
                {
                    Log.Information($"NativeLibraryManager: Crypto库路径: {cryptoPath}");
                }
                else
                {
                    Log.Warning($"NativeLibraryManager: Crypto库不存在: {cryptoPath}");
                }

                // 存储路径信息供后续使用
                _linuxSDKPaths = new LinuxSDKPaths
                {
                    ComPath = comDir,
                    SslPath = sslPath,
                    CryptoPath = cryptoPath,
                    IsConfigured = true
                };

                Log.Information("NativeLibraryManager: Linux SDK路径配置完成");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "NativeLibraryManager: 配置Linux SDK路径失败");
                throw;
            }
        }

        /// <summary>
        /// Linux SDK路径配置信息
        /// </summary>
        private static LinuxSDKPaths? _linuxSDKPaths;

        /// <summary>
        /// 获取Linux SDK路径配置
        /// 供外部调用NET_DVR_SetSDKInitCfg时使用
        /// </summary>
        /// <returns>Linux SDK路径配置</returns>
        public static LinuxSDKPaths? GetLinuxSDKPaths()
        {
            return _linuxSDKPaths;
        }

        /// <summary>
        /// 应用Linux SDK路径配置
        /// 在NET_DVR_Init()之前调用此方法
        /// </summary>
        public static bool ApplyLinuxSDKConfiguration()
        {
            if (!RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
            {
                Log.Debug("NativeLibraryManager: 非Linux平台，跳过SDK路径配置");
                return true;
            }

            if (_linuxSDKPaths == null || !_linuxSDKPaths.IsConfigured)
            {
                Log.Warning("NativeLibraryManager: Linux SDK路径未配置");
                return false;
            }

            try
            {
                Log.Information("NativeLibraryManager: 开始应用Linux SDK配置");

                // 调用实际的NET_DVR_SetSDKInitCfg设置路径
                bool allSuccess = true;

                // 1. 设置HCNetSDKCom组件库路径
                if (Directory.Exists(_linuxSDKPaths.ComPath))
                {
                    var comPathStruct = new SukiUI.Demo.Bll.CHCNetSDK.NET_DVR_LOCAL_SDK_PATH
                    {
                        sPath = _linuxSDKPaths.ComPath
                    };

                    IntPtr comPathPtr = Marshal.AllocHGlobal(Marshal.SizeOf(comPathStruct));
                    try
                    {
                        Marshal.StructureToPtr(comPathStruct, comPathPtr, false);
                        bool result = SukiUI.Demo.Bll.CHCNetSDK.NET_DVR_SetSDKInitCfg(
                            SukiUI.Demo.Bll.CHCNetSDK.NET_SDK_INIT_CFG_SDK_PATH, comPathPtr);

                        if (result)
                        {
                            Log.Information($"NativeLibraryManager: 成功设置组件库路径: {_linuxSDKPaths.ComPath}");
                        }
                        else
                        {
                            Log.Error($"NativeLibraryManager: 设置组件库路径失败: {_linuxSDKPaths.ComPath}");
                            allSuccess = false;
                        }
                    }
                    finally
                    {
                        Marshal.FreeHGlobal(comPathPtr);
                    }
                }

                // 2. 设置libcrypto.so路径
                if (File.Exists(_linuxSDKPaths.CryptoPath))
                {
                    IntPtr cryptoPathPtr = Marshal.StringToHGlobalAnsi(_linuxSDKPaths.CryptoPath);
                    try
                    {
                        bool result = SukiUI.Demo.Bll.CHCNetSDK.NET_DVR_SetSDKInitCfg(
                            SukiUI.Demo.Bll.CHCNetSDK.NET_SDK_INIT_CFG_LIBEAY_PATH, cryptoPathPtr);

                        if (result)
                        {
                            Log.Information($"NativeLibraryManager: 成功设置Crypto库路径: {_linuxSDKPaths.CryptoPath}");
                        }
                        else
                        {
                            Log.Error($"NativeLibraryManager: 设置Crypto库路径失败: {_linuxSDKPaths.CryptoPath}");
                            allSuccess = false;
                        }
                    }
                    finally
                    {
                        Marshal.FreeHGlobal(cryptoPathPtr);
                    }
                }

                // 3. 设置libssl.so路径
                if (File.Exists(_linuxSDKPaths.SslPath))
                {
                    IntPtr sslPathPtr = Marshal.StringToHGlobalAnsi(_linuxSDKPaths.SslPath);
                    try
                    {
                        bool result = SukiUI.Demo.Bll.CHCNetSDK.NET_DVR_SetSDKInitCfg(
                            SukiUI.Demo.Bll.CHCNetSDK.NET_SDK_INIT_CFG_SSLEAY_PATH, sslPathPtr);

                        if (result)
                        {
                            Log.Information($"NativeLibraryManager: 成功设置SSL库路径: {_linuxSDKPaths.SslPath}");
                        }
                        else
                        {
                            Log.Error($"NativeLibraryManager: 设置SSL库路径失败: {_linuxSDKPaths.SslPath}");
                            allSuccess = false;
                        }
                    }
                    finally
                    {
                        Marshal.FreeHGlobal(sslPathPtr);
                    }
                }

                if (allSuccess)
                {
                    Log.Information("NativeLibraryManager: Linux SDK配置应用成功");
                }
                else
                {
                    Log.Warning("NativeLibraryManager: Linux SDK配置部分失败");
                }

                return allSuccess;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "NativeLibraryManager: 应用Linux SDK配置失败");
                return false;
            }
        }

        /// <summary>
        /// 获取SDK初始化配置信息
        /// </summary>
        /// <returns>配置信息</returns>
        public static SDKInitializationInfo GetSDKInitializationInfo()
        {
            var info = new SDKInitializationInfo
            {
                Platform = GetCurrentPlatform(),
                IsInitialized = _isInitialized,
                IsLibraryLoaded = _isLibraryLoaded,
                BaseDirectory = AppDomain.CurrentDomain.BaseDirectory
            };

            try
            {
                // 检查库文件状态
                var checkResult = CheckAllLibraryFiles();
                info.LibraryCheckResult = checkResult;
                info.AllRequiredLibrariesPresent = checkResult.IsAllLibrariesPresent;

                // 平台特定信息
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    info.LinuxSDKPaths = _linuxSDKPaths;
                    info.IsLinuxSDKConfigured = _linuxSDKPaths?.IsConfigured ?? false;
                }

                // 预加载统计
                info.PreloadedLibrariesCount = GetPreloadedLibrariesCount();

                return info;
            }
            catch (Exception ex)
            {
                info.ErrorMessage = ex.Message;
                Log.Error(ex, "NativeLibraryManager: 获取SDK初始化信息失败");
                return info;
            }
        }

        /// <summary>
        /// 获取预加载库的数量
        /// </summary>
        /// <returns>预加载库数量</returns>
        private static int GetPreloadedLibrariesCount()
        {
            // 这里可以实现更精确的统计，目前返回估算值
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                return 10; // Windows平台预期加载的库数量
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                return 9;  // Linux平台预期加载的库数量
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                return 5;  // macOS平台预期加载的库数量
            else
                return 0;
        }

        /// <summary>
        /// 验证SDK配置完整性
        /// </summary>
        /// <returns>验证结果</returns>
        public static SDKConfigurationValidation ValidateSDKConfiguration()
        {
            var validation = new SDKConfigurationValidation
            {
                Platform = GetCurrentPlatform(),
                ValidationTime = DateTime.Now
            };

            try
            {
                Log.Information("NativeLibraryManager: 开始验证SDK配置完整性");

                // 1. 检查库管理器初始化状态
                validation.IsManagerInitialized = _isInitialized;
                if (!_isInitialized)
                {
                    validation.Issues.Add("库管理器未初始化");
                }

                // 2. 检查必需库文件
                var libraryCheck = CheckAllLibraryFiles();
                validation.LibraryCheckPassed = libraryCheck.IsAllLibrariesPresent;
                validation.MissingLibraries.AddRange(libraryCheck.MissingLibraries);

                if (!libraryCheck.IsAllLibrariesPresent)
                {
                    validation.Issues.Add($"缺失 {libraryCheck.MissingFiles} 个必需库文件");
                }

                // 3. 检查组件库
                validation.ComponentLibraryExists = libraryCheck.ComponentLibraryExists;
                if (!libraryCheck.ComponentLibraryExists)
                {
                    validation.Issues.Add("HCNetSDKCom组件库目录不存在");
                }

                // 4. 平台特定验证
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    validation.LinuxConfigurationValid = ValidateLinuxConfiguration();
                    if (!validation.LinuxConfigurationValid)
                    {
                        validation.Issues.Add("Linux SDK路径配置无效");
                    }
                }

                // 5. 总体评估
                validation.IsValid = validation.Issues.Count == 0;
                validation.Severity = validation.Issues.Count == 0 ? "正常" :
                                    validation.Issues.Count <= 2 ? "警告" : "严重";

                Log.Information($"NativeLibraryManager: SDK配置验证完成 - {validation.Severity}");
                return validation;
            }
            catch (Exception ex)
            {
                validation.Issues.Add($"验证过程异常: {ex.Message}");
                validation.IsValid = false;
                validation.Severity = "错误";
                Log.Error(ex, "NativeLibraryManager: SDK配置验证失败");
                return validation;
            }
        }

        /// <summary>
        /// 验证Linux配置
        /// </summary>
        /// <returns>是否有效</returns>
        private static bool ValidateLinuxConfiguration()
        {
            if (_linuxSDKPaths == null || !_linuxSDKPaths.IsConfigured)
                return false;

            bool isValid = true;

            // 检查组件库路径
            if (!Directory.Exists(_linuxSDKPaths.ComPath))
            {
                Log.Warning($"Linux组件库路径不存在: {_linuxSDKPaths.ComPath}");
                isValid = false;
            }

            // 检查SSL库路径
            if (!File.Exists(_linuxSDKPaths.SslPath))
            {
                Log.Warning($"Linux SSL库文件不存在: {_linuxSDKPaths.SslPath}");
                isValid = false;
            }

            // 检查Crypto库路径
            if (!File.Exists(_linuxSDKPaths.CryptoPath))
            {
                Log.Warning($"Linux Crypto库文件不存在: {_linuxSDKPaths.CryptoPath}");
                isValid = false;
            }

            return isValid;
        }
    }

    /// <summary>
    /// SDK初始化信息
    /// </summary>
    public class SDKInitializationInfo
    {
        public string Platform { get; set; } = "";
        public bool IsInitialized { get; set; } = false;
        public bool IsLibraryLoaded { get; set; } = false;
        public string BaseDirectory { get; set; } = "";
        public LibraryCheckResult? LibraryCheckResult { get; set; }
        public bool AllRequiredLibrariesPresent { get; set; } = false;
        public LinuxSDKPaths? LinuxSDKPaths { get; set; }
        public bool IsLinuxSDKConfigured { get; set; } = false;
        public int PreloadedLibrariesCount { get; set; } = 0;
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// 生成初始化信息报告
        /// </summary>
        /// <returns>报告字符串</returns>
        public string GenerateReport()
        {
            var report = new System.Text.StringBuilder();

            report.AppendLine("=== SDK初始化信息报告 ===");
            report.AppendLine($"生成时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"平台: {Platform}");
            report.AppendLine($"基础目录: {BaseDirectory}");
            report.AppendLine();

            report.AppendLine("初始化状态:");
            report.AppendLine($"  管理器已初始化: {(IsInitialized ? "是" : "否")}");
            report.AppendLine($"  库已加载: {(IsLibraryLoaded ? "是" : "否")}");
            report.AppendLine($"  预加载库数量: {PreloadedLibrariesCount}");
            report.AppendLine();

            report.AppendLine("库文件状态:");
            report.AppendLine($"  所有必需库存在: {(AllRequiredLibrariesPresent ? "是" : "否")}");
            if (LibraryCheckResult != null)
            {
                report.AppendLine($"  总文件数: {LibraryCheckResult.TotalFiles}");
                report.AppendLine($"  存在文件数: {LibraryCheckResult.ExistingFiles}");
                report.AppendLine($"  缺失文件数: {LibraryCheckResult.MissingFiles}");
                report.AppendLine($"  组件库存在: {(LibraryCheckResult.ComponentLibraryExists ? "是" : "否")}");
            }
            report.AppendLine();

            if (Platform == "Linux" && LinuxSDKPaths != null)
            {
                report.AppendLine("Linux SDK配置:");
                report.AppendLine($"  配置状态: {(IsLinuxSDKConfigured ? "已配置" : "未配置")}");
                report.AppendLine($"  组件库路径: {LinuxSDKPaths.ComPath}");
                report.AppendLine($"  SSL库路径: {LinuxSDKPaths.SslPath}");
                report.AppendLine($"  Crypto库路径: {LinuxSDKPaths.CryptoPath}");
                report.AppendLine();
            }

            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                report.AppendLine($"错误信息: {ErrorMessage}");
                report.AppendLine();
            }

            report.AppendLine("=== 报告结束 ===");
            return report.ToString();
        }
    }

    /// <summary>
    /// SDK配置验证结果
    /// </summary>
    public class SDKConfigurationValidation
    {
        public string Platform { get; set; } = "";
        public DateTime ValidationTime { get; set; } = DateTime.Now;
        public bool IsValid { get; set; } = false;
        public string Severity { get; set; } = "未知";
        public bool IsManagerInitialized { get; set; } = false;
        public bool LibraryCheckPassed { get; set; } = false;
        public bool ComponentLibraryExists { get; set; } = false;
        public bool LinuxConfigurationValid { get; set; } = true;
        public List<string> Issues { get; set; } = new List<string>();
        public List<string> MissingLibraries { get; set; } = new List<string>();

        /// <summary>
        /// 生成验证报告
        /// </summary>
        /// <returns>验证报告</returns>
        public string GenerateValidationReport()
        {
            var report = new System.Text.StringBuilder();

            report.AppendLine("=== SDK配置验证报告 ===");
            report.AppendLine($"验证时间: {ValidationTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"平台: {Platform}");
            report.AppendLine($"验证结果: {(IsValid ? "✓ 通过" : "✗ 失败")}");
            report.AppendLine($"严重程度: {Severity}");
            report.AppendLine();

            report.AppendLine("检查项目:");
            report.AppendLine($"  ✓ 管理器初始化: {(IsManagerInitialized ? "通过" : "失败")}");
            report.AppendLine($"  ✓ 库文件检查: {(LibraryCheckPassed ? "通过" : "失败")}");
            report.AppendLine($"  ✓ 组件库存在: {(ComponentLibraryExists ? "通过" : "失败")}");

            if (Platform == "Linux")
            {
                report.AppendLine($"  ✓ Linux配置: {(LinuxConfigurationValid ? "通过" : "失败")}");
            }
            report.AppendLine();

            if (Issues.Count > 0)
            {
                report.AppendLine("发现的问题:");
                foreach (var issue in Issues)
                {
                    report.AppendLine($"  • {issue}");
                }
                report.AppendLine();
            }

            if (MissingLibraries.Count > 0)
            {
                report.AppendLine("缺失的库文件:");
                foreach (var missing in MissingLibraries)
                {
                    report.AppendLine($"  • {missing}");
                }
                report.AppendLine();
            }

            if (!IsValid)
            {
                report.AppendLine("建议操作:");
                report.AppendLine("1. 检查SDK安装包完整性");
                report.AppendLine("2. 确保库文件在正确目录");
                report.AppendLine("3. 验证文件权限设置");
                report.AppendLine("4. 重新运行初始化流程");
            }

            report.AppendLine("=== 验证报告结束 ===");
            return report.ToString();
        }
    }

    /// <summary>
    /// Linux SDK路径配置
    /// </summary>
    public class LinuxSDKPaths
    {
        public string ComPath { get; set; } = "";
        public string SslPath { get; set; } = "";
        public string CryptoPath { get; set; } = "";
        public bool IsConfigured { get; set; } = false;
    }

    /// <summary>
    /// 库文件检查结果
    /// </summary>
    public class LibraryCheckResult
    {
        public string Platform { get; set; } = "";
        public int TotalFiles { get; set; } = 0;
        public int ExistingFiles { get; set; } = 0;
        public int MissingFiles => TotalFiles - ExistingFiles;

        /// <summary>
        /// 所有必需库是否都存在（只检查必需库，忽略可选库）
        /// </summary>
        public bool IsAllLibrariesPresent => !MissingLibraries.Any() && ComponentLibraryExists;

        /// <summary>
        /// 必需库文件数量
        /// </summary>
        public int RequiredFiles => LibraryDetails.Count(d => d.IsRequired);

        /// <summary>
        /// 存在的必需库文件数量
        /// </summary>
        public int ExistingRequiredFiles => LibraryDetails.Count(d => d.IsRequired && d.Exists);
        public List<string> MissingLibraries { get; set; } = new List<string>();
        public List<LibraryInfo> LibraryDetails { get; set; } = new List<LibraryInfo>();
        public string ComponentLibraryPath { get; set; } = "";
        public bool ComponentLibraryExists { get; set; } = false;
        public int ComponentLibraryCount { get; set; } = 0;
        public string ErrorMessage { get; set; } = "";

        /// <summary>
        /// 生成详细的检查报告
        /// </summary>
        /// <returns>检查报告</returns>
        public string GenerateReport()
        {
            var report = new System.Text.StringBuilder();

            report.AppendLine($"=== {Platform} 平台库文件检查报告 ===");
            report.AppendLine($"检查时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine();

            report.AppendLine($"总体状态: {(IsAllLibrariesPresent ? "✓ 通过" : "✗ 失败")}");
            report.AppendLine($"总文件数: {TotalFiles}");
            report.AppendLine($"存在文件: {ExistingFiles}");
            report.AppendLine($"缺失文件: {MissingFiles}");
            report.AppendLine();

            if (ComponentLibraryExists)
            {
                report.AppendLine($"✓ HCNetSDKCom组件库: 存在 ({ComponentLibraryCount} 个文件)");
                report.AppendLine($"  路径: {ComponentLibraryPath}");
            }
            else
            {
                report.AppendLine($"✗ HCNetSDKCom组件库: 不存在");
                report.AppendLine($"  预期路径: {ComponentLibraryPath}");
            }
            report.AppendLine();

            if (MissingLibraries.Count > 0)
            {
                report.AppendLine("缺失的库文件:");
                foreach (var missing in MissingLibraries)
                {
                    var detail = LibraryDetails.FirstOrDefault(d => d.Name == missing || d.Name.EndsWith(missing));
                    if (detail != null)
                    {
                        report.AppendLine($"  ✗ {missing} ({detail.Description})");
                        report.AppendLine($"    预期路径: {detail.ExpectedPath}");
                    }
                    else
                    {
                        report.AppendLine($"  ✗ {missing}");
                    }
                }
                report.AppendLine();
            }

            var existingLibs = LibraryDetails.Where(d => d.Exists).ToList();
            if (existingLibs.Count > 0)
            {
                report.AppendLine("存在的库文件:");
                foreach (var existing in existingLibs)
                {
                    report.AppendLine($"  ✓ {existing.Name} ({existing.Description})");
                }
                report.AppendLine();
            }

            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                report.AppendLine($"错误信息: {ErrorMessage}");
                report.AppendLine();
            }

            // 提供解决建议
            if (MissingFiles > 0)
            {
                report.AppendLine("解决建议:");
                report.AppendLine($"1. 确保从官方SDK包中复制所有必需的库文件到 {Platform} 对应目录");
                if (Platform == "Windows")
                {
                    report.AppendLine("2. Windows库文件应放置在 bin/ 目录下");
                    report.AppendLine("3. HCNetSDKCom文件夹必须与主库文件在同一目录");
                }
                else if (Platform == "Linux")
                {
                    report.AppendLine("2. Linux库文件应放置在 Lib/ 目录下");
                    report.AppendLine("3. 确保.so文件具有执行权限");
                    report.AppendLine("4. 可能需要安装系统依赖: libssl-dev, libcrypto-dev");
                }
                report.AppendLine("5. 检查SDK版本是否与应用程序兼容");
            }

            report.AppendLine("=== 检查报告结束 ===");
            return report.ToString();
        }
    }

    /// <summary>
    /// 库文件信息
    /// </summary>
    public class LibraryInfo
    {
        public string Name { get; set; } = "";
        public string Description { get; set; } = "";
        public string ExpectedPath { get; set; } = "";
        public bool Exists { get; set; } = false;
        public bool IsRequired { get; set; } = false;
        public long FileSize { get; set; } = 0;
        public DateTime LastModified { get; set; } = DateTime.MinValue;
    }
}
