<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.ButtonsView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:content="using:SukiUI.Content"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:showMeTheXaml="clr-namespace:ShowMeTheXaml;assembly=ShowMeTheXaml.Avalonia"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:ButtonsViewModel"
             mc:Ignorable="d">
    <UserControl.Styles>
        <Style Selector="Button">
            <Setter Property="Content" Value="Button" />
            <Setter Property="suki:ButtonExtensions.ShowProgress" Value="{Binding IsBusy}" />
            <Setter Property="Command" Value="{Binding ButtonClickedCommand}" />
            <Setter Property="IsEnabled" Value="{Binding IsEnabled}" />
        </Style>
        <Style Selector="Button.Icon">
            <Setter Property="Content">
                <Template>
                    <PathIcon Data="{x:Static content:Icons.Star}"
                              Foreground="{Binding Foreground, RelativeSource={RelativeSource FindAncestor, AncestorType=Button}}"/>
                </Template>
            </Setter>
        </Style>
    </UserControl.Styles>
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Buttons">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        SukiUI has a handful of button styles, available in both the standard primary color, but also in the theme's accent color.
                    </TextBlock>
                    <TextBlock>
                        Clicking on any one of the buttons will make them all "Busy" for 3 seconds, and how this is achieved can be seen in the XAML for the Busy Button.
                    </TextBlock>
                    <StackPanel Margin="0,25,0,0" Orientation="Horizontal">
                        <TextBlock Margin="0,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="Buttons Enabled: " />
                        <ToggleSwitch Margin="15,0,0,0"
                                      Classes="Switch"
                                      IsChecked="{Binding IsEnabled}" />

                    </StackPanel>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard>
                    <suki:GroupBox Header="Standard Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button1">
                            <Button />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Basic Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button2">
                            <Button Classes="Basic" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Flat Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button3">
                            <Button Classes="Flat" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Flat Rounded Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button4">
                            <Button Classes="Rounded Flat" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Outlined Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button5">
                            <Button Classes="Outlined" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Standard Accent Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button6">
                            <Button Classes="Accent" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Basic Accent Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button7">
                            <Button Classes="Basic Accent" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Flat Accent Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button8">
                            <Button Classes="Flat Accent" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Flat Rounded Accent Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button9">
                            <Button Classes="Rounded Flat Accent" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Outlined Accent Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button10">
                            <Button Classes="Outlined Accent" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Busy Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button11">
                            <Button suki:ButtonExtensions.ShowProgress="True" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard>
                    <suki:GroupBox Header="Large Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button12">
                            <Button Classes="Flat Large" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Standard Icon Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button13">
                            <Button Classes="Icon"/>
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Basic Icon Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button14">
                            <Button Classes="Basic Icon" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Flat Icon Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button15">
                            <Button Classes="Flat Icon" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Outlined Icon Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button16">
                            <Button Classes="Outlined Icon" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Basic Accent Icon Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button17">
                            <Button Classes="Basic Accent Icon" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Flat Accent Icon Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button18">
                            <Button Classes="Flat Accent Icon" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Outlined Accent Icon Button">
                        <showMeTheXaml:XamlDisplay UniqueId="Button19">
                            <Button Classes="Outlined Accent Icon" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</UserControl>