﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:sys="using:System">
    <Design.PreviewWith>
        <Border Width="400" Height="400">
            <SplitView DisplayMode="Inline" IsPaneOpen="True">
                <SplitView.Pane>
                    <Border Background="Green" />
                </SplitView.Pane>
                <SplitView.Content>
                    <Border Background="Blue" />
                </SplitView.Content>
            </SplitView>
        </Border>
    </Design.PreviewWith>

    <x:Double x:Key="SplitViewOpenPaneThemeLength">320</x:Double>
    <x:Double x:Key="SplitViewCompactPaneThemeLength">48</x:Double>
    <SolidColorBrush x:Key="SplitViewLightDismissOverlayBackground"
                     Opacity="0.6"
                     Color="{DynamicResource ThemeControlLowColor}" />
    <sys:TimeSpan x:Key="SplitViewPaneAnimationOpenDuration">00:00:0.4</sys:TimeSpan>
    <sys:TimeSpan x:Key="SplitViewPaneAnimationCloseDuration">00:00:0.4</sys:TimeSpan>

    <!-- Not used here (directly), but preserving for reference
        <x:String x:Key="SplitViewPaneAnimationOpenPreDuration">00:00:00.19999</x:String>-->

    <ControlTheme x:Key="{x:Type SplitView}" TargetType="SplitView">
        <Setter Property="OpenPaneLength" Value="{DynamicResource SplitViewOpenPaneThemeLength}" />
        <Setter Property="CompactPaneLength" Value="{DynamicResource SplitViewCompactPaneThemeLength}" />
        <Setter Property="PaneBackground" Value="{DynamicResource ThemeControlHighlightLowBrush}" />

        <!--  Left  -->
        <Style Selector="^:left">
            <Setter Property="Template">
                <ControlTemplate TargetType="SplitView">
                    <Grid Name="Container" Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.PaneColumnGridLength}" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Panel Name="PART_PaneRoot"
                               HorizontalAlignment="Left"
                               Background="Transparent"
                               ClipToBounds="True"
                               ZIndex="100">
                            <ContentPresenter x:Name="PART_PanePresenter"
                                              Content="{TemplateBinding Pane}"
                                              ContentTemplate="{TemplateBinding PaneTemplate}" />
                            <Rectangle Name="HCPaneBorder"
                                       Width="1"
                                       HorizontalAlignment="Right"
                                       Fill="Transparent" />
                        </Panel>

                        <Panel Name="ContentRoot">
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}" />
                            <Rectangle Name="LightDismissLayer" />
                        </Panel>

                    </Grid>
                </ControlTemplate>
            </Setter>
        </Style>

        <!--  Overlay  -->
        <Style Selector="^:overlay:left /template/ Panel#PART_PaneRoot">
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
            <!--  ColumnSpan should be 2  -->
            <Setter Property="Grid.ColumnSpan" Value="1" />
            <Setter Property="Grid.Column" Value="0" />
        </Style>
        <Style Selector="^:overlay:left /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="1" />
            <Setter Property="Grid.ColumnSpan" Value="2" />
        </Style>

        <!--  CompactInline  -->
        <Style Selector="^:compactinline:left /template/ Panel#PART_PaneRoot">
            <Setter Property="Grid.ColumnSpan" Value="1" />
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
        </Style>
        <Style Selector="^:compactinline:left /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="1" />
            <Setter Property="Grid.ColumnSpan" Value="1" />
        </Style>

        <!--  CompactOverlay  -->
        <Style Selector="^:compactoverlay:left /template/ Panel#PART_PaneRoot">
            <!--  ColumnSpan should be 2  -->
            <Setter Property="Grid.ColumnSpan" Value="1" />
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
        </Style>
        <Style Selector="^:compactoverlay:left /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="1" />
            <Setter Property="Grid.ColumnSpan" Value="1" />
        </Style>

        <!--  Inline  -->
        <Style Selector="^:inline:left /template/ Panel#PART_PaneRoot">
            <Setter Property="Grid.ColumnSpan" Value="1" />
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
        </Style>
        <Style Selector="^:inline:left /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="1" />
            <Setter Property="Grid.ColumnSpan" Value="1" />
        </Style>

        <!--  Right  -->
        <Style Selector="^:right">
            <Setter Property="Template">
                <ControlTemplate TargetType="SplitView">
                    <Grid Name="Container" Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.PaneColumnGridLength}" />
                        </Grid.ColumnDefinitions>

                        <Panel Name="PART_PaneRoot"
                               HorizontalAlignment="Right"
                               Background="{TemplateBinding PaneBackground}"
                               ClipToBounds="True"
                               ZIndex="100">
                            <ContentPresenter x:Name="PART_PanePresenter"
                                              Content="{TemplateBinding Pane}"
                                              ContentTemplate="{TemplateBinding PaneTemplate}" />
                            <Rectangle Name="HCPaneBorder"
                                       Width="1"
                                       HorizontalAlignment="Left"
                                       Fill="Transparent" />
                        </Panel>

                        <Panel Name="ContentRoot">
                            <ContentPresenter x:Name="PART_ContentPresenter"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}" />
                            <Rectangle Name="LightDismissLayer" />
                        </Panel>

                    </Grid>
                </ControlTemplate>
            </Setter>
        </Style>

        <!--  Overlay  -->
        <Style Selector="^:overlay:right /template/ Panel#PART_PaneRoot">
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
            <Setter Property="Grid.ColumnSpan" Value="2" />
            <Setter Property="Grid.Column" Value="1" />
        </Style>
        <Style Selector="^:overlay:right /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="Grid.ColumnSpan" Value="2" />
        </Style>

        <!--  CompactInline  -->
        <Style Selector="^:compactinline:right /template/ Panel#PART_PaneRoot">
            <Setter Property="Grid.ColumnSpan" Value="1" />
            <Setter Property="Grid.Column" Value="1" />
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
        </Style>
        <Style Selector="^:compactinline:right /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="Grid.ColumnSpan" Value="1" />
        </Style>

        <!--  CompactOverlay  -->
        <Style Selector="^:compactoverlay:right /template/ Panel#PART_PaneRoot">
            <Setter Property="Grid.ColumnSpan" Value="2" />
            <Setter Property="Grid.Column" Value="1" />
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
        </Style>
        <Style Selector="^:compactoverlay:right /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="Grid.ColumnSpan" Value="1" />
        </Style>

        <!--  Inline  -->
        <Style Selector="^:inline:right /template/ Panel#PART_PaneRoot">
            <Setter Property="Grid.ColumnSpan" Value="1" />
            <Setter Property="Grid.Column" Value="1" />
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
        </Style>
        <Style Selector="^:inline:right /template/ Panel#ContentRoot">
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="Grid.ColumnSpan" Value="1" />
        </Style>

        <!--  Open/Close Pane animation  -->
        <Style Selector="^:open /template/ Panel#PART_PaneRoot">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Easing="SineEaseOut"
                                      Property="Width"
                                      Duration="{StaticResource SplitViewPaneAnimationOpenDuration}" />
                </Transitions>
            </Setter>
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=OpenPaneLength}" />
        </Style>
        <Style Selector="^:open /template/ Rectangle#LightDismissLayer">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Easing="SineEaseOut"
                                      Property="Opacity"
                                      Duration="{StaticResource SplitViewPaneAnimationOpenDuration}" />
                </Transitions>
            </Setter>
            <Setter Property="Opacity" Value="1.0" />
        </Style>

        <Style Selector="^:closed /template/ Panel#PART_PaneRoot">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Easing="SineEaseOut"
                                      Property="Width"
                                      Duration="{StaticResource SplitViewPaneAnimationCloseDuration}" />
                </Transitions>
            </Setter>
            <Setter Property="Width" Value="{Binding RelativeSource={RelativeSource TemplatedParent}, Path=TemplateSettings.ClosedPaneWidth}" />
        </Style>
        <Style Selector="^:closed /template/ Rectangle#LightDismissLayer">
            <Setter Property="Transitions">
                <Transitions>
                    <DoubleTransition Easing="SineEaseOut"
                                      Property="Opacity"
                                      Duration="{StaticResource SplitViewPaneAnimationCloseDuration}" />
                </Transitions>
            </Setter>
            <Setter Property="Opacity" Value="0.0" />
        </Style>

        <Style Selector="^ /template/ Rectangle#LightDismissLayer">
            <Setter Property="IsVisible" Value="False" />
            <Setter Property="Fill" Value="Transparent" />
        </Style>
        <Style Selector="^:lightdismiss /template/ Rectangle#LightDismissLayer">
            <Setter Property="Fill" Value="{DynamicResource SplitViewLightDismissOverlayBackground}" />
        </Style>

        <Style Selector="^:overlay:open /template/ Rectangle#LightDismissLayer">
            <Setter Property="IsVisible" Value="True" />
        </Style>
        <Style Selector="^:compactoverlay:open /template/ Rectangle#LightDismissLayer">
            <Setter Property="IsVisible" Value="True" />
        </Style>
    </ControlTheme>
</ResourceDictionary>