<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI"
                    xmlns:dialogs="clr-namespace:SukiUI.Dialogs">
    <ControlTheme TargetType="suki:SukiDialog" x:Key="SukiDialogTheme">
        <Setter Property="ClipToBounds" Value="False"/> 
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                    
                    <Border Padding="0"  Margin="0,55,0,0"
                            ClipToBounds="True"  MinWidth="450" 
                            
                            CornerRadius="25">
                        <Panel>
                            
                            <Border IsVisible="{TemplateBinding ShowCardBackground}" CornerRadius="25" Background="{DynamicResource SukiCardBackground}">
                                <Panel Background="{DynamicResource PopupGradientBrush}" Margin="-5">
                               
                                </Panel>
                        
                            </Border>



                            <Grid RowDefinitions="Auto,Auto,*,Auto"
                                  ColumnDefinitions="*"
                                  Margin="30,30,30,5" 
                                  IsVisible="{Binding !IsViewModelOnly, RelativeSource={RelativeSource TemplatedParent}}">
                                  
                                <Border Grid.Row="0" Margin="0,10,0,0" IsVisible="{TemplateBinding IconColor, Converter={x:Static ObjectConverters.IsNotNull}}"></Border>
                                  <TextBlock Grid.Row="1" Margin="0,24,0,0" IsVisible="{TemplateBinding Title,Converter={x:Static StringConverters.IsNotNullOrEmpty}}" HorizontalAlignment="Center" FontSize="22" FontWeight="DemiBold" Text="{TemplateBinding Title}" />
                                  <ContentControl Grid.Row="2" Margin="0,24,0,0" MaxWidth="{TemplateBinding Content, Converter={x:Static dialogs:DialogContentMaxWidthValueConverter.Instance}}" Content="{TemplateBinding Content}" >
                                      <ContentControl.Styles>
                                          <Style Selector="TextBlock">
                                              <Setter  Property="FontSize" Value="14" />
                                              <Setter  Property="TextWrapping" Value="Wrap" />
                                          </Style>
                                      </ContentControl.Styles>
                                  </ContentControl>
                                  <ItemsControl Grid.Row="3" Margin="0,26,0,0" ItemsSource="{TemplateBinding ActionButtons}">
                                      <ItemsControl.Styles>
                                          <Style Selector="Button">
                                              <Setter Property="Margin" Value="15,0,0,25" />
                                          </Style>
                                      </ItemsControl.Styles>
                                      <ItemsControl.ItemsPanel>
                                          <ItemsPanelTemplate>
                                              <StackPanel Orientation="Horizontal" HorizontalAlignment="Right"/>
                                          </ItemsPanelTemplate>
                                      </ItemsControl.ItemsPanel>

                                  </ItemsControl>
                            </Grid>
                        
                            <ContentControl Content="{TemplateBinding ViewModel}" Margin="15"
                                            IsVisible="{TemplateBinding IsViewModelOnly}" />
                        </Panel>
                    </Border>
                    <Border CornerRadius="50"  HorizontalAlignment="Center"
                                    VerticalAlignment="Top" 
                                    Margin="-15,-15,0,0" Width="100" Height="100">
                    <Panel >
                        <Border Width="90" HorizontalAlignment="Center"
                                Height="100"
                                IsVisible="{TemplateBinding IconColor, Converter={x:Static ObjectConverters.IsNotNull}}"
                      
                                BoxShadow="{DynamicResource SukiBigPopupShadow}"
                                CornerRadius="50" />
                        <Ellipse HorizontalAlignment="Center" VerticalAlignment="Center" Width="100" Height="100" Fill="{TemplateBinding IconColor}"/>
                        <PathIcon HorizontalAlignment="Center" VerticalAlignment="Center" Data="{TemplateBinding Icon}"
                                  Foreground="White"
                                  Width="28"
                                  Height="28" />
                        
                    </Panel>
                    </Border>
                </Panel>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
    <ControlTheme TargetType="suki:SukiDialog"
                  BasedOn="{StaticResource SukiDialogTheme}"
                  x:Key="{x:Type suki:SukiDialog}" />
</ResourceDictionary>