<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:content="clr-namespace:SukiUI.Content"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI">
    <ControlTheme x:Key="SukiDropDownButtonStyle" TargetType="DropDownButton">
        <Setter Property="BorderThickness" Value="1.2" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiMediumBorderBrush}" />
        <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{DynamicResource SukiBackground}" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="15" />
        <Setter Property="Padding" Value="20,8" />
        <Setter Property="FontWeight" Value="{DynamicResource DefaultDemiBold}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate>
                    <suki:GlassCard Name="RootBorder" Classes="Discrete"
                                    Padding="0"
                            ClipToBounds="True"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <suki:GlassCard.Transitions>
                            <Transitions>
                                <BrushTransition Property="BorderBrush" Duration="0:0:0.25" />
                            </Transitions>
                        </suki:GlassCard.Transitions>
                        <Grid Name="InnerGrid">
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" />
                            </Grid.ColumnDefinitions>

                            <ContentPresenter Name="PART_ContentPresenter"
                                              Grid.Column="0"
                                              Padding="{TemplateBinding Padding}"
                                              HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                              VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                              Content="{TemplateBinding Content}"
                                              ContentTemplate="{TemplateBinding ContentTemplate}"
                                              FontFamily="{TemplateBinding FontFamily}"
                                              FontWeight="{TemplateBinding FontWeight}"
                                              RecognizesAccessKey="True">
                                <ContentPresenter.Transitions>
                                    <Transitions>
                                        <BrushTransition Property="Foreground" Duration="0:0:0.25" />
                                        <BrushTransition Property="BorderBrush" Duration="0:0:0.25" />
                                    </Transitions>
                                </ContentPresenter.Transitions>
                            </ContentPresenter>

                            <PathIcon Name="PART_Icon"
                                      Grid.Column="1"
                                      Width="12"
                                      Height="12"
                                      Margin="0,0,10,0"
                                      HorizontalAlignment="Right"
                                      VerticalAlignment="Center"
                                      Data="{x:Static content:Icons.ChevronDown}"
                                      Foreground="{DynamicResource SukiText}"
                                      IsHitTestVisible="False"
                                      UseLayoutRounding="False">
                                <PathIcon.Transitions>
                                    <Transitions>
                                        <BrushTransition Property="Foreground" Duration="0:0:0.25" />
                                    </Transitions>
                                </PathIcon.Transitions>
                            </PathIcon>

                        </Grid>
                    </suki:GlassCard>
                </ControlTemplate>
            </Setter.Value>
        </Setter>



        <Style Selector="^:pointerover">
            <Style Selector="^ /template/ TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>
            <Style Selector="^ /template/ ContentPresenter">
                <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>

            <Style Selector="^ /template/ Border">
                <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>

            <Style Selector="^ /template/ PathIcon#PART_Icon">
                <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>
        </Style>
        <Style Selector="^:pressed">
            <Style Selector="^ /template/ TextBlock">
                <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>
            <Style Selector="^ /template/ Border">
                <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />
                <Setter Property="RenderTransform">
                    <Setter.Value>
                        <ScaleTransform ScaleX="0.97" ScaleY="0.97" />
                    </Setter.Value>
                </Setter>
            </Style>
            <Style Selector="^ /template/ ContentPresenter">
                <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>
        </Style>
        <Style Selector="^:disabled">
            <Setter Property="Opacity" Value="{DynamicResource ThemeDisabledOpacity}" />
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type DropDownButton}"
                  BasedOn="{StaticResource SukiDropDownButtonStyle}"
                  TargetType="DropDownButton" />
</ResourceDictionary>