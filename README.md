<div id="header" align="center">
	<img src="https://raw.githubusercontent.com/kikipoulet/SukiUI/main/Images/OIG.N5o-removebg-preview.png" ></img> 
	<h3>Suki UI</h3>
	<h4><i>A Desktop UI Library for <a href="https://avaloniaui.net/">Avalonia</a></i></h4>
	<div id="badges" >
		<a href="https://kikipoulet.github.io/SukiUI/documentation/getting-started/installation.html"><img src="https://img.shields.io/badge/GET%20STARTED-purple?style=for-the-badge" alt="Get Started"/></a>
		<a href="https://www.nuget.org/packages/SukiUI"><img src="https://img.shields.io/nuget/vpre/SukiUI?style=for-the-badge" alt="Nuget Pre"/></a> 
		<a href="https://github.com/kikipoulet/SukiUI/blob/main/LICENSE"><img src="https://img.shields.io/github/license/kikipoulet/SukiUI?style=for-the-badge" alt="License"/></a>  
	</div>
</div>
<br/>


## ✨ 6.0 Release

I’m pleased to announce the 6.0 Release. This is our biggest release yet, and after a long journey, the library is now close to the initial idealized vision I had for it. I’m happy to say that both the style and various APIs are now finalized.

Moving forward, our focus will be on maintaining the library and potentially extending certain functionalities. No major changes are expected unless absolutely necessary.

Massive thanks to [sirdoombox](https://github.com/sirdoombox) for his incredible work. Without him, this library would definitely not be the same.
<br/><br/>

## 📄 Documentation



### [SukiUI Documentation](https://kikipoulet.github.io/SukiUI/)  🚀
<br/>

Many thanks to [McCree Lee](https://github.com/AuroraZiling) for his work on the documentation and the support for Chinese users.


<br/>

## 📱 UI Theme 

<br/>

### SukiUI is a theme for AvaloniaUI with support for Light/Dark themes, and different color themes.

<br/>

![sukicolorpresentation](https://github.com/user-attachments/assets/70fdaa99-6274-478a-bb2a-025a0b7f1182)


<br/>

### SukiUI offers a lot of customization options, such as the ability to change the background style for your application.

<br/>

![backgroundstyles](https://github.com/user-attachments/assets/88ea5b54-1e05-431c-be92-15dbbe61f1b9)

<br/>

## 🕹 Richly Animated Controls

<br/>

###  SukiUI contains additional fully animated controls.

<br/>

![animations](https://github.com/user-attachments/assets/7d6d0e94-b315-48d5-8556-01f12f8faaca)

 
<br/>

## ⚒ Complete UI Toolkit

We aim to provide a complete kit for your UI, encompassing all the necessary functionalities while maintaining a consistent design style. The SukiUI library is packed with various UI features to assist you in your UI development.

<br/>

### Toast System - Notification

SukiUI offer a versatile way to display unique toasts, to help you create nice interactions easily.


![toast](https://github.com/user-attachments/assets/2a07cf6d-f29a-469c-8964-239e7f734a09)

<br/>

### Dialogs - MessageBox

<br/>

![dialogslight](https://github.com/user-attachments/assets/51f05964-e847-4ed6-a1f7-78616eea9cee)


<br/> <br/>

### Dock

SukiUI includes styles for the [Dock](https://github.com/wieslawsoltes/Dock) library.


![dockgradient](https://github.com/user-attachments/assets/828cf7f1-ca76-4e36-ab27-921c35f93280)


