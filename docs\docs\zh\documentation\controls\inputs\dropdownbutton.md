# DropDownButton

点击`DropDownButton`后，将会弹出自定义的内容

## 展示

<img src="/controls/inputs/dropdownbutton.webp" height="300px" width="300px"/>

## 示例

```xml
<DropDownButton Content="Click To Open">
    <DropDownButton.Flyout>
        <Flyout>
            <!-- Content -->
        </Flyout>
    </DropDownButton.Flyout>
</DropDownButton>
```

## 参阅

[Demo: SukiUI.Demo/Features/ControlsLibrary/MiscView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/MiscView.axaml)