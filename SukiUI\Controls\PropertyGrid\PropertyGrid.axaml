<UserControl x:Class="SukiUI.Controls.PropertyGrid"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controls="clr-namespace:SukiUI.Controls"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             Name="self"
             d:DesignHeight="450"
             d:DesignWidth="800"
             mc:Ignorable="d">

    <!--  TODO inject these styles, so that one can customize them  -->
	
    <UserControl.Styles>
        <Style Selector="TextBlock.Label">
            <Setter Property="HorizontalAlignment" Value="Left" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Text" Value="{Binding DisplayName}" />
            <Setter Property="Grid.Column" Value="0" />
            <Setter Property="TextAlignment" Value="Center" />
        </Style>

        <Style Selector="NumericUpDown.NumberSelector">
            <Setter Property="HorizontalAlignment" Value="Right" /> 
            <Setter Property="MinWidth" Value="120"></Setter>
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="HorizontalContentAlignment" Value="Right" />
            <Setter Property="FontSize" Value="14" />
            <Setter Property="Value" Value="{Binding Value}" />
            <Setter Property="Grid.Column" Value="2" />
            <Setter Property="IsReadOnly" Value="{Binding IsReadOnly}" />
            <Setter Property="Height" Value="36" />
        </Style>

        <Style Selector="Grid.Row">
            <Setter Property="Height" Value="40" />
            <Setter Property="Margin" Value="12,4,4,0" />
        </Style>
    </UserControl.Styles>

    <ScrollViewer>
        <ItemsControl ItemsSource="{Binding #self.Instance.Categories}" />
    </ScrollViewer>
</UserControl>
