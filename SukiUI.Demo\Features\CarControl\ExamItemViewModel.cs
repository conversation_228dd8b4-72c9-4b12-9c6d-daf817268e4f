﻿using Avalonia.Controls.Converters;
using Avalonia.Media;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Xml.Linq;

namespace SukiUI.Demo.Features.CarControl
{
    public partial class ExamItemViewModel : ObservableObject
    {
        public ExamItemViewModel(string name, 
                                 int index,string state="0",double height=20,double margin=2)
        {
            Index = index;
            Name = name;
            State = state;
            Height= height;
            Margin = margin;
            //BackgroundColor = backgroundColor;
            //ForegroundColor = foregroundColor;
        }
        [ObservableProperty]
        private  int index;
        [ObservableProperty]
        private string name;

        [ObservableProperty]
        private IBrush backgroundColor;

        [ObservableProperty]
        private IBrush foregroundColor;

        [ObservableProperty]
        private string state;

        [ObservableProperty]
        private double height;
        [ObservableProperty]
        private double margin;

        partial void OnStateChanged(string value)
        {
            // 根据选中状态自动更新颜色
            //BackgroundColor = value ? Brushes.DarkGreen : Brushes.Green;
            //ForegroundColor = value ? Brushes.Yellow : Brushes.White;
            switch (value)
            {
                case "1":
                    BackgroundColor = Brushes.Gray;
                    ForegroundColor = Brushes.White;
                    break;
                case "2":
                    BackgroundColor = Brushes.Red;
                    ForegroundColor = Brushes.White;
                    break;
                case "3":
                    BackgroundColor = Brushes.Green;
                    ForegroundColor = Brushes.White;
                    break;
                default:
                    BackgroundColor = Brushes.Transparent;
                    ForegroundColor = Brushes.Transparent;
                    break;
            }
        }
    }
}
