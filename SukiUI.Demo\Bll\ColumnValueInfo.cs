﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SukiUI.Demo.Bll
{
    /// <summary>
    /// 字段模型基类
    /// </summary>
    public class ColumnValueInfo
    {
        public ColumnValueInfo()
        {
        }
        public ColumnValueInfo(string name, object value)
        {
            this.Name = name;
            this.Value = value;
        }

        /// <summary>
        /// 列名
        /// </summary>
        public string Name = "";
        /// <summary>
        /// 列值
        /// </summary>
        public object Value = null;
        /// <summary>
        /// 列值-字符串
        /// </summary>
        public string ValueStr
        {
            get
            {
                return Value == null ? "" : Value.ToString();
            }
        }
    }
}
