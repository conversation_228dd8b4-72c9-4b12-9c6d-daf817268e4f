<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.ProgressView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:showMeTheXaml="clr-namespace:ShowMeTheXaml;assembly=ShowMeTheXaml.Avalonia"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:ProgressViewModel"
             mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Progress Indicators">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        Here is a demo of all the available progress indicators in SukiUI, including the stepper.
                    </TextBlock>
                    <TextBlock>
                        The progress bars can be controlled with the controls below except the stepper which has it's own logic.
                    </TextBlock>
                    <StackPanel Margin="0,25,0,0" Orientation="Horizontal">
                        <TextBlock Margin="0,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="Show Progress Value : " />
                        <ToggleSwitch Margin="15,0,0,0"
                                      Classes="Switch"
                                      IsChecked="{Binding IsTextVisible}" />

                    </StackPanel>
                    <StackPanel Margin="0,25,0,0" Orientation="Horizontal">
                        <TextBlock Margin="0,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="Indeterminate : " />
                        <ToggleSwitch Margin="15,0,0,0"
                                      Classes="Switch"
                                      IsChecked="{Binding IsIndeterminate}" />

                    </StackPanel>
                    <StackPanel Margin="0,10,0,0" Orientation="Horizontal">
                        <Slider MinWidth="250"
                                MaxWidth="400"
                                HorizontalAlignment="Left"
                                IsSnapToTickEnabled="True"
                                Maximum="100"
                                Minimum="0"
                                TickFrequency="1"
                                Value="{Binding ProgressValue}" />
                        <TextBlock Margin="12,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="{Binding ProgressValue}" />
                    </StackPanel>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1" HorizontalScrollBarVisibility="Disabled">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard Margin="20">
                    <suki:GroupBox Header="Wave Progress">
                        <Grid>
                            <showMeTheXaml:XamlDisplay VerticalAlignment="Center" UniqueId="WaveProgress">
                                <suki:WaveProgress IsTextVisible="{Binding IsTextVisible}"
                                                   Value="{Binding ProgressValue}" />
                            </showMeTheXaml:XamlDisplay>
                        </Grid>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard>
                    <suki:GroupBox Header="Circle Progress">
                        <StackPanel Spacing="15">
                            <showMeTheXaml:XamlDisplay UniqueId="CircleProgress1">
                                <suki:CircleProgressBar Width="130"
                                                        Height="130"
                                                        IsIndeterminate="{Binding IsIndeterminate}"
                                                        StrokeWidth="11"
                                                        Value="{Binding ProgressValue}">
                                    <TextBlock Margin="0,2,0,0"
                                               Classes="h3"
                                               IsVisible="{Binding IsTextVisible}"
                                               Text="{Binding ProgressValue, StringFormat={}{0:#0}%}" />
                                </suki:CircleProgressBar>
                            </showMeTheXaml:XamlDisplay>
                            <showMeTheXaml:XamlDisplay UniqueId="CircleProgress2">
                                <suki:CircleProgressBar Width="130"
                                                        Height="130"
                                                        Classes="Accent"
                                                        IsIndeterminate="{Binding IsIndeterminate}"
                                                        StrokeWidth="11"
                                                        Value="{Binding ProgressValue}">
                                    <TextBlock Margin="0,2,0,0"
                                               Classes="h3"
                                               IsVisible="{Binding IsTextVisible}"
                                               Text="{Binding ProgressValue, StringFormat={}{0:#0}%}" />
                                </suki:CircleProgressBar>
                            </showMeTheXaml:XamlDisplay>
                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard Width="400">
                    <suki:GroupBox Header="Standard Progress Bar">
                        <suki:GroupBox.Styles>
                            <Style Selector="ProgressBar">
                                <Setter Property="Value" Value="{Binding ProgressValue}" />
                                <Setter Property="ShowProgressText" Value="{Binding IsTextVisible}" />
                            </Style>
                        </suki:GroupBox.Styles>
                        <StackPanel Spacing="15">
                            <showMeTheXaml:XamlDisplay UniqueId="Progress1">
                                <ProgressBar IsIndeterminate="{Binding IsIndeterminate}" />
                            </showMeTheXaml:XamlDisplay>
                            <showMeTheXaml:XamlDisplay UniqueId="Progress2">
                                <ProgressBar Classes="Accent" IsIndeterminate="{Binding IsIndeterminate}" />
                            </showMeTheXaml:XamlDisplay>
                            <StackPanel VerticalAlignment="Stretch"
                                        Orientation="Horizontal"
                                        Spacing="15">
                                <showMeTheXaml:XamlDisplay UniqueId="Progress3">
                                    <ProgressBar IsIndeterminate="{Binding IsIndeterminate}" Orientation="Vertical" />
                                </showMeTheXaml:XamlDisplay>
                                <showMeTheXaml:XamlDisplay UniqueId="Progress4">
                                    <ProgressBar Classes="Accent"
                                                 IsIndeterminate="{Binding IsIndeterminate}"
                                                 Orientation="Vertical" />
                                </showMeTheXaml:XamlDisplay>
                            </StackPanel>
                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard>
                    <suki:GroupBox Header="Loading Indicator">
                        <StackPanel Spacing="15">
                            <showMeTheXaml:XamlDisplay UniqueId="Loading0">
                                <suki:Loading LoadingStyle="Simple" />
                            </showMeTheXaml:XamlDisplay>
                            <showMeTheXaml:XamlDisplay UniqueId="Loading1">
                                <suki:Loading LoadingStyle="Glow" />
                            </showMeTheXaml:XamlDisplay>
                            <showMeTheXaml:XamlDisplay UniqueId="Loading2">
                                <suki:Loading LoadingStyle="Pellets" />
                            </showMeTheXaml:XamlDisplay>
                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard Width="500">
                    <suki:GroupBox Header="Stepper">
                        <StackPanel Spacing="15">
                            <showMeTheXaml:XamlDisplay Margin="0,20,0,0" UniqueId="Stepper">
                                <suki:Stepper Index="{Binding StepIndex}" Steps="{Binding Steps}" />
                            </showMeTheXaml:XamlDisplay>
                            <!--  Ignore your IDE, x:True and x:False are absolutely valid intrinsics in Avalonia.  -->
                            <Grid Margin="0,18,0,0" ColumnDefinitions="Auto,*,Auto">
                                <Button Grid.Column="0"
                                        Command="{Binding ChangeStepCommand}"
                                        CommandParameter="{x:False}"
                                        Content="Decrement" />
                                <Button Grid.Column="2"
                                        Command="{Binding ChangeStepCommand}"
                                        CommandParameter="{x:True}"
                                        Content="Increment" />
                            </Grid>
                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard Width="500">
                    <suki:GroupBox Header="Alternative Stepper">
                        <StackPanel Spacing="15">
                            <showMeTheXaml:XamlDisplay UniqueId="AltStepper">
                                <suki:Stepper AlternativeStyle="True"
                                              Index="{Binding StepIndex}"
                                              Steps="{Binding Steps}" />
                            </showMeTheXaml:XamlDisplay>
                            <!--  Ignore your IDE, x:True and x:False are absolutely valid intrinsics in Avalonia.  -->
                            <Grid ColumnDefinitions="Auto,*,Auto">
                                <Button Grid.Column="0"
                                        Command="{Binding ChangeStepCommand}"
                                        CommandParameter="{x:False}"
                                        Content="Decrement" />
                                <Button Grid.Column="2"
                                        Command="{Binding ChangeStepCommand}"
                                        CommandParameter="{x:True}"
                                        Content="Increment" />
                            </Grid>
                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</UserControl>