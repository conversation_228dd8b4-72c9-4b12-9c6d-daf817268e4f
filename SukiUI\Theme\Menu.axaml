<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:controls="clr-namespace:SukiUI.Controls"
                    xmlns:system="clr-namespace:System;assembly=netstandard">
    <ControlTheme x:Key="SukiTopLevelMenuItem" TargetType="MenuItem">
        <Setter Property="FontWeight" Value="{DynamicResource DefaultDemiBold}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Padding" Value="10 0" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="root"
                        Margin="5,5"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{DynamicResource SukiMenuBorderBrush}"
                        BorderThickness="0"
                        CornerRadius="8">
                    <Panel>
                        <controls:GlassCard CornerRadius="8" Opacity="0" Name="GlassMenuItemBackground" Classes="Control">
                            <controls:GlassCard.Transitions>
                                <Transitions>
                                    <DoubleTransition Property="Opacity" Duration="0:0:0.15"></DoubleTransition>
                                </Transitions>
                            </controls:GlassCard.Transitions>
                        </controls:GlassCard>
                        <ContentPresenter Name="PART_HeaderPresenter"
                                          Margin="{TemplateBinding Padding}"
                                          HorizontalContentAlignment="Left"
                                          VerticalContentAlignment="Center"
                                          Content="{TemplateBinding Header}">
                            <ContentPresenter.DataTemplates>
                                <DataTemplate DataType="system:String">
                                    <TextBlock FontWeight="{DynamicResource DefaultDemiBold}" Text="{CompiledBinding}" />
                                </DataTemplate>
                            </ContentPresenter.DataTemplates>
                        </ContentPresenter>
                        <Popup Name="PART_Popup"
                               ClipToBounds="True"
                               IsLightDismissEnabled="True"
                               IsOpen="{TemplateBinding IsSubMenuOpen,
                                                        Mode=TwoWay}"
                               Opacity="0"
                               OverlayInputPassThroughElement="{Binding $parent[Menu]}"
                               Placement="BottomEdgeAlignedLeft">
                            <LayoutTransformControl Name="PART_LayoutTransform" RenderTransformOrigin="50%, 0%">
                                <Grid Margin="-12,0,0,0">
                                    <Border Margin="16,8,8,8"
                                            BoxShadow="{DynamicResource SukiPopupShadow}"
                                            CornerRadius="6" />
                                    <Border Name="PART_Border"
                                            Margin="16,8,8,8"
                                            Background="{DynamicResource SukiCardBackground}"
                                            BorderBrush="{DynamicResource SukiMenuBorderBrush}"
                                            BorderThickness="1"
                                            BoxShadow="{DynamicResource SukiPopupShadow}"
                                            ClipToBounds="True"
                                            CornerRadius="6">
                                        <Panel Background="{DynamicResource PopupGradientBrush}">
                                            <ScrollViewer>
                                                <ItemsPresenter Name="PART_ItemsPresenter"
                                                                Grid.IsSharedSizeScope="True"
                                                                ItemsPanel="{TemplateBinding ItemsPanel}" />
                                            </ScrollViewer>
                                        </Panel>
                                    </Border>
                                </Grid>
                            </LayoutTransformControl>
                        </Popup>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>
        <Style Selector="^:pointerover /template/ controls|GlassCard#GlassMenuItemBackground">
            <Setter Property="Opacity" Value="1" />
        </Style>

        <Style Selector="^:open /template/ LayoutTransformControl#PART_LayoutTransform">
            <Style.Animations>
                <Animation Easing="{StaticResource MenuEasing}"
                           FillMode="Forward"
                           Duration="{StaticResource ShortAnimationDuration}">
                    <KeyFrame Cue="0%">
                        <Setter Property="ScaleTransform.ScaleY" Value="0.88" />
                        <Setter Property="ScaleTransform.ScaleX" Value="0.88" />
                        <Setter Property="Opacity" Value="0" />
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="ScaleTransform.ScaleY" Value="1" />
                        <Setter Property="ScaleTransform.ScaleX" Value="1" />
                        <Setter Property="Opacity" Value="1" />
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="SukiMenuStyle" TargetType="Menu">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="ItemContainerTheme" Value="{StaticResource SukiTopLevelMenuItem}" />
        <Setter Property="Template">
            <ControlTemplate>
                <LayoutTransformControl Name="PART_LayoutTransform" RenderTransformOrigin="50%, 0%">
                    <Border Height="45"
                                        Padding="{TemplateBinding Padding}"
                                        BorderBrush="{DynamicResource SukiBorderBrush}"
                                        BorderThickness="0"
                                        CornerRadius="{TemplateBinding CornerRadius}"
                                        >
                        <ItemsPresenter Name="PART_ItemsPresenter"
                                        ItemsPanel="{TemplateBinding ItemsPanel}"
                                        KeyboardNavigation.TabNavigation="Continue" />
                    </Border>
                </LayoutTransformControl>
            </ControlTemplate>
        </Setter>
        <Style Selector="^[IsEnabled=True] /template/ LayoutTransformControl#PART_LayoutTransform">
            <Style.Animations>
                <Animation Easing="{StaticResource MenuEasing}"
                           FillMode="Forward"
                           Duration="{StaticResource MediumAnimationDuration}">
                    <KeyFrame Cue="0%">
                        <Setter Property="ScaleTransform.ScaleY" Value="0.92" />
                        <Setter Property="ScaleTransform.ScaleX" Value="0.92" />
                        <Setter Property="Opacity" Value="0" />
                        <Setter Property="IsVisible" Value="True" />
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="ScaleTransform.ScaleY" Value="1" />
                        <Setter Property="ScaleTransform.ScaleX" Value="1" />
                        <Setter Property="Opacity" Value="1" />
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
        <Style Selector="^[IsEnabled=False] /template/ LayoutTransformControl#PART_LayoutTransform">
            <Style.Animations>
                <Animation Easing="{StaticResource MenuEasing}"
                           FillMode="Forward"
                           Duration="{StaticResource MediumAnimationDuration}">
                    <KeyFrame Cue="0%">
                        <Setter Property="ScaleTransform.ScaleY" Value="1" />
                        <Setter Property="ScaleTransform.ScaleX" Value="1" />
                        <Setter Property="Opacity" Value="1" />
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="ScaleTransform.ScaleY" Value="0.9" />
                        <Setter Property="ScaleTransform.ScaleX" Value="0.9" />
                        <Setter Property="Opacity" Value="0" />
                        <Setter Property="IsVisible" Value="False" />
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type Menu}"
                  BasedOn="{StaticResource SukiMenuStyle}"
                  TargetType="Menu" />
</ResourceDictionary>