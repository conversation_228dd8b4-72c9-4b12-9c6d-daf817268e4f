﻿using System.Data;


namespace SukiUI.Demo.Bll
{
    
   
        /// <summary>
        /// 数据表模型基类
        /// </summary>
        public class BaseDataModel
        {
            public BaseDataModel()
            {
            }
            public BaseDataModel(string _tn)
            {
                this._TableName = _tn;
            }
            /// <summary>
            /// 表名
            /// </summary>
            public string _TableName = "";
            /// <summary>
            /// 字段列表
            /// </summary>
            public List<ColumnValueInfo> columnDataList = new List<ColumnValueInfo>();

            /// <summary>
            /// 获取列值
            /// </summary>
            /// <param name="cName">字段名</param>
            /// <returns></returns>
            public object this[string cName]
            {
                get
                {
                    ColumnValueInfo bdc = columnDataList.Find(_bdc => _bdc.Name.Equals(cName));
                    if (bdc != null)
                    {
                        return bdc.Value;
                    }
                    return null;
                }
                set
                {
                    SetColumnValue(cName, value);
                }
            }

            public string GetValueStr(string cName)
            {
                object obj = this[cName];
                if (obj == null) return "";
                return obj.ToString();
            }

            /// <summary>
            /// 设置值，从数据库或准确值
            /// </summary>
            /// <param name="cName"></param>
            /// <param name="cValue"></param>
            public void SetColumnValue(string cName, object cValue)
            {
                cName = cName.ToLower();
                ColumnValueInfo bdc = columnDataList.Find(_bdc => _bdc.Name.Equals(cName));
                if (cValue == DBNull.Value) cValue = null;
                if (bdc != null)
                {
                    bdc.Value = cValue;
                }
                else
                {
                    columnDataList.Add(new ColumnValueInfo(cName, cValue));
                }
            }

            /// <summary>
            /// 复制
            /// </summary>
            /// <returns></returns>
            public BaseDataModel Clone()
            {
                BaseDataModel data = new BaseDataModel();
                data._TableName = this._TableName;
                for (int i = 0; i < this.columnDataList.Count; i++)
                {
                    data.columnDataList.Add(new ColumnValueInfo(this.columnDataList[i].Name, this.columnDataList[i].Value));
                }
                return data;
            }

            public string GetOraInsertSql()
            {
                string sqlstr = "insert into {0}({1}) values({2})";
                string cols1 = "";
                string cols2 = "";
                for (int i = 0; i < this.columnDataList.Count; i++)
                {
                    string cname = this.columnDataList[i].Name;
                    cols1 += "," + cname;
                    if (cname == "cznr")
                    {
                        cols2 += ",:" + cname + "";
                    }
                    else
                        cols2 += ",:" + cname;
                }
                if (cols1 != "") cols1 = cols1.Substring(1);
                if (cols2 != "") cols2 = cols2.Substring(1);
                return string.Format(sqlstr, this._TableName, cols1, cols2);
            }
            public string GetOraUpdateSql(string sqlwhere)
            {
                string sqlstr = "update {0} set {1} ";
                string cols1 = "";
                for (int i = 0; i < this.columnDataList.Count; i++)
                {
                    string cname = this.columnDataList[i].Name;
                    cols1 += "," + cname + "=:" + cname;
                }
                if (cols1 != "") cols1 = cols1.Substring(1);
                return string.Format(sqlstr, this._TableName, cols1) + sqlwhere;
            }
            //public List<OracleParameter> GetOraParas(string chead)
            //{
            //    List<OracleParameter> oplist = new List<OracleParameter>();
            //    for (int i = 0; i < this.columnDataList.Count; i++)
            //    {
            //        ColumnValueInfo cvi = this.columnDataList[i];
            //        oplist.Add(new OracleParameter(chead + cvi.Name, cvi.ValueStr == "" ? (object)DBNull.Value : cvi.Value));
            //    }
            //    return oplist;
            //}
            /// <summary>
            /// 以flist为准修改sourcelist
            /// </summary>
            /// <param name="sourcelist"></param>
            /// <param name="flist"></param>
            /// <param name="keyname"></param>
            public static void Merge_Reset(List<BaseDataModel> sourcelist, List<BaseDataModel> flist, string keyname)
            {
                sourcelist.RemoveAll(d => !flist.Exists(d2 => d2.GetValueStr(keyname) == d.GetValueStr(keyname)));
                for (int i = 0; i < flist.Count; i++)
                {
                    BaseDataModel fdata = flist[i];
                    BaseDataModel data = sourcelist.Find(d => d.GetValueStr(keyname) == fdata.GetValueStr(keyname));
                    if (data == null)
                    {
                        sourcelist.Add(fdata);
                    }
                    else
                    {
                        for (int j = 0; j < fdata.columnDataList.Count; j++)
                        {
                            data.SetColumnValue(fdata.columnDataList[j].Name, fdata.columnDataList[j].Value);
                        }
                    }
                }
            }

            /// <summary>
            /// 以flist为准修改sourcelist
            /// </summary>
            /// <param name="sourcelist"></param>
            /// <param name="flist"></param>
            public static void Merge_Reset(List<BaseDataModel> sourcelist, List<BaseDataModel> flist)
            {
                sourcelist.RemoveAll(d => !flist.Contains(d));
                sourcelist.AddRange(flist.FindAll(d => !sourcelist.Contains(d)));
            }

            public static DataTable ListToDataTable(List<BaseDataModel> datalist)
            {
                DataTable dt = new DataTable();
                if (datalist.Count == 0) return dt;

                for (int i = 0, len = datalist[0].columnDataList.Count; i < len; i++)
                {
                    dt.Columns.Add(datalist[0].columnDataList[i].Name);
                }
                for (int i = 0, leni = datalist.Count; i < leni; i++)
                {
                    DataRow dr = dt.NewRow();
                    for (int j = 0, lenj = dt.Columns.Count; j < lenj; j++)
                    {
                        string cname = dt.Columns[j].ColumnName;
                        dr[cname] = datalist[i][cname];
                    }
                    dt.Rows.Add(dr);
                }
                return dt;
            }
            /// <summary>
            /// 获取列值
            /// </summary>
            /// <param name="cName">字段名</param>
            /// <returns></returns>
            public object this[int index]
            {
                get
                {
                    ColumnValueInfo bdc = columnDataList[index];
                    if (bdc != null)
                    {
                        return bdc.Value;
                    }
                    return null;
                }

            }
        }
}



