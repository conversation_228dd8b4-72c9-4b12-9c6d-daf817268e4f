<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:suki="https://github.com/kikipoulet/SukiUI">
    <Style Selector="WrapPanel.PageContainer">
        <Setter Property="Margin" Value="15,15,0,0" />
        <Setter Property="Orientation" Value="Horizontal" />
        <Setter Property="suki:WrapPanelExtensions.AnimatedScroll" Value="True" />
        <Style Selector="^ &gt; suki|GlassCard">
            <Setter Property="Margin" Value="15,15,15,15" />
        </Style>
    </Style>
</Styles>
