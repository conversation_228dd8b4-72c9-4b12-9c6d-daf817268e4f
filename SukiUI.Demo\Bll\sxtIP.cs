﻿using SukiUI.Demo.Bll;

public class sxtIP
{
    DoublePoint sxtZB;

    public DoublePoint SxtZB
    {
        get { return sxtZB; }
        set { sxtZB = value; }
    }
    string sxtIp;

    public string SxtIp
    {
        get { return sxtIp; }
        set { sxtIp = value; }
    }
    int sxtTDH;

    public int SxtTDH
    {
        get { return sxtTDH; }
        set { sxtTDH = value; }
    }
    string sxtUser;

    public string SxtUser
    {
        get { return sxtUser; }
        set { sxtUser = value; }
    }

    string sxtPassWord;
    public string SxtPassWord
    {
        get { return sxtPassWord; }
        set { sxtPassWord = value; }
    }

    string sxtJL;
    public string SxtJL
    {
        get { return sxtJL; }
        set { sxtJL = value; }
    }

    string _yplxj;
    public string yplxj
    {
        get { return _yplxj; }
        set { _yplxj = value; }
    }
}