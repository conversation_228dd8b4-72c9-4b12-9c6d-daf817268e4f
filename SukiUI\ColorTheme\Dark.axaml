﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=netstandard">
    <Color x:Key="SukiBackground">Transparent</Color>
    <Color x:Key="SukiStrongBackground">#353535</Color>
    <Color x:Key="SukiLightBackground">Transparent</Color>
    <Color x:Key="SukiCardBackground">#323232</Color>
    <Color x:Key="SukiPopupBackground">#252525</Color>
    <Color x:Key="SukiGlassCardBackground">#49aaaaaa</Color>
    <Color x:Key="SukiGlassCardOpaqueBackground">#333333</Color>
    <Color x:Key="SukiControlTouchBackground">#505050</Color>
    <Color x:Key="SukiDialogBackground">#000000</Color>

    <Color x:Key="SukiBorderBrush">#353535</Color>
    <Color x:Key="SukiControlBorderBrush">#606060</Color>
    <Color x:Key="SukiMediumBorderBrush">#555555</Color>
    <Color x:Key="SukiLightBorderBrush">#454545</Color>
    <Color x:Key="SukiMenuBorderBrush">#454545</Color>
    <Color x:Key="GlassBorderBrush">#252729</Color>

    <Color x:Key="SukiText">#edffffff</Color>
    <Color x:Key="SukiLowText">#caffffff</Color>
    <Color x:Key="SukiDisabledText">#ca727272</Color>

    <BoxShadows x:Key="SukiLowShadow">0 0 3 0 #353535</BoxShadows>
    <BoxShadows x:Key="SukiSwitchShadow">0 1 5 0 #555555</BoxShadows>
    <BoxShadows x:Key="SukiSmallPopupShadow">0 0 4 1 #101010</BoxShadows>
    <BoxShadows x:Key="SukiPopupShadow">1 1 14 1 #101010</BoxShadows>
    <BoxShadows x:Key="SukiBigPopupShadow">1 4 17 0 #111111</BoxShadows>
    
    <LinearGradientBrush x:Key="PopupGradientBrush" StartPoint="0%,0%" EndPoint="100%,100%">
        <GradientStop Color="{DynamicResource SukiPrimaryColor3}" Offset="0"></GradientStop>
        <GradientStop Color="{DynamicResource SukiPrimaryColor10}" Offset="0.9"></GradientStop>

    </LinearGradientBrush>

    <system:Boolean x:Key="IsDark">True</system:Boolean>
    <system:Boolean x:Key="IsLight">False</system:Boolean>

    <system:Double x:Key="GlassOpacity">0.18</system:Double>
   
    <system:Double x:Key="ControlGlassOpacity">0.3</system:Double>
    <system:Double x:Key="DiscreteControlGlassOpacity">0.17</system:Double>
    <Color x:Key="ControlSukiGlassCardBackground">#49aaaaaa</Color>

</ResourceDictionary>