﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:content="clr-namespace:SukiUI.Content"
        xmlns:suki="https://github.com/kikipoulet/SukiUI"
        xmlns:theme="clr-namespace:SukiUI.Theme">

    <Design.PreviewWith>
        <Border Width="300"
                Padding="20"
                Background="{DynamicResource SukiBackground}">
            <StackPanel>
                <TextBox Width="200"
                         Margin="5"
                         Classes="Prefix"
                         Text="avaloniaui.net"
                         Watermark="https://" />
                <TextBox Width="200"
                         Margin="5"
                         Classes="Suffix"
                         Text="avaloniaui"
                         Watermark="@gmail.com" />
                <TextBox Width="200"
                         Margin="5"
                         Classes="BottomBar"
                         Text="Elem" />
                <TextBox Width="200"
                         Margin="5"
                         Text="Elem" />
                <TextBox Width="200"
                         Margin="5"
                         IsEnabled="False"
                         Text="Elem" />
                <TextBox Classes="NoShadow" Text="Elem" />

                <Panel>
                    <Border Margin="0,0,0,25" Classes="Card">
                        <StackPanel>
                            <TextBlock Margin="6,0,0,3"
                                       FontSize="13"
                                       FontWeight="{DynamicResource DefaultDemiBold}"
                                       Text="Username" />
                            <TextBox BorderBrush="Red" BorderThickness="1" />

                            <TextBlock Margin="6,18,0,3"
                                       FontSize="13"
                                       FontWeight="{DynamicResource DefaultDemiBold}"
                                       Text="Password" />
                            <TextBox Margin="0,0,0,20" />
                        </StackPanel>
                    </Border>
                    <Button Width="160"
                            HorizontalAlignment="Center"
                            VerticalAlignment="Bottom"
                            Classes="Rounded Flat">
                        <StackPanel HorizontalAlignment="Center"
                                    VerticalAlignment="Center"
                                    Orientation="Horizontal">
                            <PathIcon Data="{x:Static content:Icons.Login}" Foreground="White" />
                            <TextBlock Margin="10,0,0,0"
                                       FontSize="13"
                                       FontWeight="{DynamicResource DefaultDemiBold}"
                                       Foreground="White"
                                       Text="Sign In" />
                        </StackPanel>
                    </Button>
                </Panel>

            </StackPanel>
        </Border>
    </Design.PreviewWith>

    <Styles.Resources>
        <StreamGeometry x:Key="TextBoxClearButtonData">M 11.416016,10 20,1.4160156 18.583984,0 10,8.5839846 1.4160156,0 0,1.4160156 8.5839844,10 0,18.583985 1.4160156,20 10,11.416015 18.583984,20 20,18.583985 Z</StreamGeometry>
        <StreamGeometry x:Key="PasswordBoxRevealButtonData">m10.051 7.0032c2.215 0 4.0105 1.7901 4.0105 3.9984s-1.7956 3.9984-4.0105 3.9984c-2.215 0-4.0105-1.7901-4.0105-3.9984s1.7956-3.9984 4.0105-3.9984zm0 1.4994c-1.3844 0-2.5066 1.1188-2.5066 2.499s1.1222 2.499 2.5066 2.499 2.5066-1.1188 2.5066-2.499-1.1222-2.499-2.5066-2.499zm0-5.0026c4.6257 0 8.6188 3.1487 9.7267 7.5613 0.10085 0.40165-0.14399 0.80877-0.54686 0.90931-0.40288 0.10054-0.81122-0.14355-0.91208-0.54521-0.94136-3.7492-4.3361-6.4261-8.2678-6.4261-3.9334 0-7.3292 2.6792-8.2689 6.4306-0.10063 0.40171-0.50884 0.64603-0.91177 0.54571s-0.648-0.5073-0.54737-0.90901c1.106-4.4152 5.1003-7.5667 9.728-7.5667z</StreamGeometry>
        <StreamGeometry x:Key="PasswordBoxHideButtonData">m0.21967 0.21965c-0.26627 0.26627-0.29047 0.68293-0.07262 0.97654l0.07262 0.08412 4.0346 4.0346c-1.922 1.3495-3.3585 3.365-3.9554 5.7495-0.10058 0.4018 0.14362 0.8091 0.54543 0.9097 0.40182 0.1005 0.80909-0.1436 0.90968-0.5455 0.52947-2.1151 1.8371-3.8891 3.5802-5.0341l1.8096 1.8098c-0.70751 0.7215-1.1438 1.71-1.1438 2.8003 0 2.2092 1.7909 4 4 4 1.0904 0 2.0788-0.4363 2.8004-1.1438l5.9193 5.9195c0.2929 0.2929 0.7677 0.2929 1.0606 0 0.2663-0.2662 0.2905-0.6829 0.0726-0.9765l-0.0726-0.0841-6.1135-6.1142 0.0012-0.0015-1.2001-1.1979-2.8699-2.8693 2e-3 -8e-4 -2.8812-2.8782 0.0012-0.0018-1.1333-1.1305-4.3064-4.3058c-0.29289-0.29289-0.76777-0.29289-1.0607 0zm7.9844 9.0458 3.5351 3.5351c-0.45 0.4358-1.0633 0.704-1.7392 0.704-1.3807 0-2.5-1.1193-2.5-2.5 0-0.6759 0.26824-1.2892 0.7041-1.7391zm1.7959-5.7655c-1.0003 0-1.9709 0.14807-2.8889 0.425l1.237 1.2362c0.5358-0.10587 1.0883-0.16119 1.6519-0.16119 3.9231 0 7.3099 2.6803 8.2471 6.4332 0.1004 0.4018 0.5075 0.6462 0.9094 0.5459 0.4019-0.1004 0.6463-0.5075 0.5459-0.9094-1.103-4.417-5.0869-7.5697-9.7024-7.5697zm0.1947 3.5093 3.8013 3.8007c-0.1018-2.0569-1.7488-3.7024-3.8013-3.8007z</StreamGeometry>

        <MenuFlyout x:Key="DefaultTextBoxContextFlyout" Placement="Bottom">
            <MenuItem x:Name="TextBoxContextFlyoutCutItem"
                      Margin="10,0,0,0"
                      Command="{Binding $parent[TextBox].Cut}"
                      Header="{DynamicResource STRING_MENU_CUT}"
                      InputGesture="{x:Static TextBox.CutGesture}"
                      IsEnabled="{Binding $parent[TextBox].CanCut}" />
            <MenuItem x:Name="TextBoxContextFlyoutCopyItem"
                      Margin="10,0,0,0"
                      Command="{Binding $parent[TextBox].Copy}"
                      Header="{DynamicResource STRING_MENU_COPY}"
                      InputGesture="{x:Static TextBox.CopyGesture}"
                      IsEnabled="{Binding $parent[TextBox].CanCopy}" />
            <MenuItem x:Name="TextBoxContextFlyoutPasteItem"
                      Margin="10,0,0,0"
                      Command="{Binding $parent[TextBox].Paste}"
                      Header="{DynamicResource STRING_MENU_PASTE}"
                      InputGesture="{x:Static TextBox.PasteGesture}"
                      IsEnabled="{Binding $parent[TextBox].CanPaste}" />
        </MenuFlyout>

        <ContextMenu x:Key="DefaultTextBoxContextMenu" x:Name="TextBoxContextMenu">
            <MenuItem x:Name="TextBoxContextMenuCutItem"
                      Margin="10,0,0,0"
                      Command="{Binding $parent[TextBox].Cut}"
                      Header="Cut"
                      InputGesture="{x:Static TextBox.CutGesture}"
                      IsEnabled="{Binding $parent[TextBox].CanCut}" />
            <MenuItem x:Name="TextBoxContextMenuCopyItem"
                      Margin="10,0,0,0"
                      Command="{Binding $parent[TextBox].Copy}"
                      Header="Copy"
                      InputGesture="{x:Static TextBox.CopyGesture}"
                      IsEnabled="{Binding $parent[TextBox].CanCopy}" />
            <MenuItem x:Name="TextBoxContextMenuPasteItem"
                      Margin="10,0,0,0"
                      Command="{Binding $parent[TextBox].Paste}"
                      Header="Paste"
                      InputGesture="{x:Static TextBox.PasteGesture}"
                      IsEnabled="{Binding $parent[TextBox].CanPaste}" />
        </ContextMenu>
    </Styles.Resources>

    <Style Selector="TextBox.FlatTextBox">
        <Setter Property="Background" Value="{DynamicResource SukiLightBorderBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiLightBorderBrush}" />

    </Style>


    <Style Selector="TextBox">
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
        <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{DynamicResource SukiBackground}" />
        <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
        <Setter Property="SelectionBrush" Value="{DynamicResource SukiPrimaryColor75}" />
        <Setter Property="MinHeight" Value="45" />
        <Setter Property="Padding" Value="6,8,5,6" />
        <Setter Property="ContextFlyout" Value="{StaticResource DefaultTextBoxContextFlyout}" />

        <Setter Property="Template">

            <ControlTemplate>

                <Border Padding="5">
                    <Border.Resources>


                        <theme:StringToDoubleConverter x:Key="StringToDoubleC" />
                    </Border.Resources>
                    <Panel>
                        <suki:GlassCard Name="border"
                                        Padding="0"
                                        BorderBrush="{TemplateBinding BorderBrush}"
                                        BorderThickness="{TemplateBinding BorderThickness}"
                                        CornerRadius="{TemplateBinding CornerRadius}">
                            <suki:GlassCard.Transitions>
                                <Transitions>
                                    <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                                </Transitions>
                            </suki:GlassCard.Transitions>
                        </suki:GlassCard>
                        <Border Name="borderbottom"
                                Margin="1,-1,1,-1"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="0"
                                ClipToBounds="True"
                                CornerRadius="1">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                                </Transitions>
                            </Border.Transitions>


                            <DockPanel Margin="{TemplateBinding Padding}"
                                       HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                       VerticalAlignment="{TemplateBinding VerticalContentAlignment}">

                                <TextBlock Name="floatingWatermark"
                                           DockPanel.Dock="Top"
                                           Text="{TemplateBinding Watermark}">
                                    <TextBlock.IsVisible>
                                        <MultiBinding Converter="{x:Static BoolConverters.And}">
                                            <Binding Path="UseFloatingWatermark" RelativeSource="{RelativeSource TemplatedParent}" />
                                            <Binding Converter="{x:Static StringConverters.IsNotNullOrEmpty}"
                                                     Path="Text"
                                                     RelativeSource="{RelativeSource TemplatedParent}" />
                                        </MultiBinding>
                                    </TextBlock.IsVisible>
                                </TextBlock>

                                <DataValidationErrors>

                                    <Grid ColumnDefinitions="Auto,*,Auto">
                                        <Panel>
                                            <StackPanel Orientation="Horizontal">
                                                <ContentPresenter Content="{TemplateBinding InnerLeftContent}" />
                                                <TextBlock Margin="3,1,3,0"
                                                           FontSize="14"
                                                           Foreground="{DynamicResource SukiLowText}"
                                                           Text="{TemplateBinding theme:TextBoxExtensions.Prefix}" />
                                            </StackPanel>
                                        </Panel>
                                        <ScrollViewer Grid.Column="1"
                                                      HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                                      VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}">
                                            <Panel>
                                                <TextBlock Name="watermark"
                                                           FontSize="{TemplateBinding FontSize}"
                                                           IsVisible="{TemplateBinding Text,
                                                                                       Converter={x:Static StringConverters.IsNullOrEmpty}}"
                                                           Opacity="0.5"
                                                           Text="{TemplateBinding Watermark}"
                                                           TextAlignment="{TemplateBinding TextAlignment}"
                                                           TextWrapping="{TemplateBinding TextWrapping}" />
                                                <TextPresenter Name="PART_TextPresenter"
                                                               Margin="0,0,0,0"
                                                               CaretBrush="{DynamicResource SukiLowText}"
                                                               CaretIndex="{TemplateBinding CaretIndex}"
                                                               PasswordChar="{TemplateBinding PasswordChar}"
                                                               RevealPassword="{TemplateBinding RevealPassword}"
                                                               SelectionBrush="{TemplateBinding SelectionBrush}"
                                                               SelectionEnd="{TemplateBinding SelectionEnd}"
                                                               SelectionForegroundBrush="{TemplateBinding SelectionForegroundBrush}"
                                                               SelectionStart="{TemplateBinding SelectionStart}"
                                                               Text="{TemplateBinding Text,
                                                                                      Mode=TwoWay}"
                                                               TextAlignment="{TemplateBinding TextAlignment}"
                                                               TextBlock.FontSize="{TemplateBinding FontSize}"
                                                               TextBlock.Foreground="{TemplateBinding Foreground}"
                                                               TextWrapping="{TemplateBinding TextWrapping}" />
                                            </Panel>
                                        </ScrollViewer>
                                        <StackPanel Grid.Column="2" Orientation="Horizontal">
                                            <theme:TextEraserButton Margin="5,0,2,0"
                                                                    Cursor="Hand"
                                                                    IsVisible="{TemplateBinding theme:TextBoxExtensions.AddDeleteButton}"
                                                                    Opacity="{TemplateBinding Text,
                                                                                              Converter={StaticResource StringToDoubleC}}"
                                                                    Text="{TemplateBinding Text,
                                                                                           Mode=TwoWay}">
                                                <theme:TextEraserButton.Transitions>
                                                    <Transitions>
                                                        <DoubleTransition Property="Opacity" Duration="0:0:0.35" />
                                                    </Transitions>
                                                </theme:TextEraserButton.Transitions>
                                            </theme:TextEraserButton>
                                            <ContentPresenter Content="{TemplateBinding InnerRightContent}" />
                                        </StackPanel>


                                    </Grid>
                                </DataValidationErrors>

                            </DockPanel>
                        </Border>
                    </Panel>
                </Border>

            </ControlTemplate>
        </Setter>
    </Style>
    <Style Selector="TextBox:pointerover /template/ suki|GlassCard#border">
        <Setter Property="BorderBrush" Value="{DynamicResource SukiBorderBrush}" />

    </Style>

    <Style Selector="TextBox.FlatTextBox /template/ suki|GlassCard#border">
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="ClipToBounds" Value="True" />

    </Style>


    <Style Selector="TextBox:pointerover /template/ Border#borderbottom">
        <Setter Property="BorderBrush" Value="{DynamicResource SukiBorderBrush}" />

    </Style>


    <Style Selector="TextBox.BottomBar /template/ Border#borderbottom">
        <Setter Property="BorderThickness" Value="0,0,0,1.5" />

    </Style>


    <Style Selector="TextBox:focus /template/ Border#borderbottom">
        <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />

    </Style>
    <Style Selector="TextBox:focus /template/ suki|GlassCard#border">
        <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />
        <Setter Property="BorderThickness" Value="1.2" />
    </Style>
    <Style Selector="TextBox:error /template/ suki|GlassCard#border">
        <Setter Property="BorderBrush" Value="{DynamicResource ErrorBrush}" />
    </Style>
    <Style Selector="TextBox /template/ DockPanel">
        <Setter Property="Cursor" Value="IBeam" />
    </Style>
    <Style Selector="TextBox:disabled /template/ suki|GlassCard#border">
        <Setter Property="Opacity" Value="{DynamicResource ThemeDisabledOpacity}" />
    </Style>
    <Style Selector="TextBox:disabled">
        <Setter Property="Foreground" Value="{DynamicResource SukiDisabledText}" />
    </Style>

    <Style Selector="TextBox.clearButton[AcceptsReturn=False][IsReadOnly=False]:focus:not(TextBox:empty)">
        <Setter Property="InnerRightContent">
            <Template>
                <Button Classes="textBoxClearButton" Command="{Binding $parent[TextBox].Clear}" />
            </Template>
        </Setter>
    </Style>

    <Style Selector="Button.textBoxClearButton">
        <Setter Property="Cursor" Value="Arrow" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Button">
                    <Border x:Name="PART_ButtonLayoutBorder"
                            Background="Transparent"
                            BorderThickness="{TemplateBinding BorderThickness}">
                        <Path x:Name="PART_GlyphElement"
                              Width="10"
                              Height="10"
                              HorizontalAlignment="Center"
                              VerticalAlignment="Center"
                              Data="{StaticResource TextBoxClearButtonData}"
                              Fill="{DynamicResource ThemeForegroundColor}"
                              Stretch="Uniform" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style Selector="TextBox.revealPasswordButton[AcceptsReturn=False][IsReadOnly=False]:not(TextBox:empty)">
        <Setter Property="InnerRightContent">
            <Template>
                <Panel Margin="4,0">
                    <Panel.Styles>
                        <Style Selector="ToggleButton[IsChecked=True]">
                            <Setter Property="(ToolTip.Tip)" Value="Hide Password" />
                        </Style>
                        <Style Selector="ToggleButton[IsChecked=False]">
                            <Setter Property="(ToolTip.Tip)" Value="Show Password" />
                        </Style>
                    </Panel.Styles>
                    <ToggleButton Background="Transparent"
                                  Classes="passwordBoxRevealButton"
                                  IsChecked="{Binding $parent[TextBox].RevealPassword, Mode=TwoWay}" />
                </Panel>
            </Template>
        </Setter>
    </Style>

    <Style Selector="ToggleButton.passwordBoxRevealButton">
        <Setter Property="Cursor" Value="Arrow" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToggleButton">
                    <Border x:Name="PART_ButtonLayoutBorder"
                            Width="12"
                            Background="Transparent">
                        <Panel>
                            <Path x:Name="PART_GlyphElement_Reveal"
                                  Width="12"
                                  Height="8"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  Data="{DynamicResource PasswordBoxRevealButtonData}"
                                  Fill="{DynamicResource ThemeForegroundColor}"
                                  Stretch="Uniform" />
                            <Path x:Name="PART_GlyphElement_Hide"
                                  Width="12"
                                  Height="12"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
                                  Data="{DynamicResource PasswordBoxHideButtonData}"
                                  Fill="{DynamicResource ThemeForegroundColor}"
                                  Stretch="Uniform" />
                        </Panel>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style Selector="ToggleButton.passwordBoxRevealButton:not(ToggleButton:checked) /template/ Path#PART_GlyphElement_Hide">
        <Setter Property="IsVisible" Value="False" />
    </Style>

    <Style Selector="ToggleButton.passwordBoxRevealButton:checked /template/ Path#PART_GlyphElement_Reveal">
        <Setter Property="IsVisible" Value="False" />
    </Style>

    <Style Selector="Button.textBoxClearButton, ToggleButton.passwordBoxRevealButton">
        <Setter Property="Focusable" Value="False" />
    </Style>

    <Style Selector="TextBox.NoShadow /template/ Border">
        <Setter Property="BoxShadow" Value="0 0 0 0 Gray" />
    </Style>

    <Style Selector="TextBox.NoShadow /template/ suki|GlassCard#border">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="IsVisible" Value="False" />
    </Style>

    <Style Selector="TextBox.NoShadow:focus /template/ suki|GlassCard#border">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
    </Style>

</Styles>