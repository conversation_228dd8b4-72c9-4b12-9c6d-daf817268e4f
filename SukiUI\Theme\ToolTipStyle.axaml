<ResourceDictionary xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="SukiToolTipTheme" TargetType="ToolTip">
        <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
        <Setter Property="Background" Value="{DynamicResource SukiCardBackground}" />
        <Setter Property="Margin" Value="15,15" />
        <Setter Property="Padding" Value="16,8" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="Placement" Value="Pointer" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border BoxShadow="{DynamicResource SukiPopupShadow}" Margin="{TemplateBinding Margin}"
                            Background="{TemplateBinding Background}"
                            CornerRadius="12">
                        <ContentPresenter Name="ContentPresenter"
                                          Margin="{TemplateBinding Padding}"
                                          Content="{TemplateBinding ContentControl.Content}"
                                          ContentTemplate="{TemplateBinding ContentControl.ContentTemplate}" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type ToolTip}"
                  BasedOn="{StaticResource SukiToolTipTheme}"
                  TargetType="ToolTip" />
</ResourceDictionary>
