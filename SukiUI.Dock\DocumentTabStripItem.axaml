﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:core="using:Dock.Model.Core"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI"
                    x:CompileBindings="True">
  <Design.PreviewWith>
    <Border Padding="20">
      <StackPanel Spacing="20">
        <DocumentTabStripItem>Leaf</DocumentTabStripItem>
        <DocumentTabStripItem IsSelected="True">Arch</DocumentTabStripItem>
        <DocumentTabStripItem Background="Yellow">Background</DocumentTabStripItem>
      </StackPanel>
    </Border>
  </Design.PreviewWith>

  <ContextMenu x:Key="DocumentTabStripItemContextMenu" x:DataType="core:IDockable" x:CompileBindings="True">
    <MenuItem Header="_Float"
              Command="{Binding Owner.Factory.FloatDockable}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanFloat}"/>
    <MenuItem Header="_Close"
              Command="{Binding Owner.Factory.CloseDockable}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanClose}"/>
    <MenuItem Header="Close other tabs"
              Command="{Binding Owner.Factory.CloseOtherDockables}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanClose}"/>
    <MenuItem Header="Close all tabs"
              Command="{Binding Owner.Factory.CloseAllDockables}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanClose}"/>
    <MenuItem Header="_Close tabs to the left"
              Command="{Binding Owner.Factory.CloseLeftDockables}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanClose}"/>
    <MenuItem Header="_Close tabs to the right"
              Command="{Binding Owner.Factory.CloseRightDockables}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanClose}"/>
  </ContextMenu>
  
  <ControlTheme x:Key="{x:Type DocumentTabStripItem}" TargetType="DocumentTabStripItem">

    <Setter Property="(TextElement.FontSize)" Value="{DynamicResource DockFontSizeNormal}" />
    <Setter Property="FontWeight" Value="Normal" />
    <Setter Property="MinHeight" Value="24" />
    <Setter Property="VerticalContentAlignment" Value="Center" />
    <Setter Property="Background" Value="Transparent" />
    <Setter Property="Foreground" Value="{DynamicResource DockThemeForegroundBrush}" />
    <Setter Property="BorderBrush" Value="{DynamicResource DockThemeBorderLowBrush}" />
    <Setter Property="BorderThickness" Value="0" />
    <Setter Property="Margin" Value="0" />
    <Setter Property="Padding" Value="4 0 4 0" />

    <Setter Property="Template" x:DataType="core:IDockable">
      <ControlTemplate>
        <Panel Margin="0,5" >
          <Border Name="SelectedBorder" IsVisible="False"
            Margin="6,0"
            Padding="0"
            
            CornerRadius="8">
            <suki:GlassCard Classes="Control" CornerRadius="8">
             
              </suki:GlassCard>
          </Border>
        
        <Border Margin="12,4" Background="{TemplateBinding Background}"
                TextElement.FontFamily="{TemplateBinding FontFamily}"
                TextElement.FontSize="{TemplateBinding FontSize}"
                TextElement.FontWeight="{TemplateBinding FontWeight}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="0"
                Padding="{TemplateBinding Padding}"
                ContextMenu="{DynamicResource DocumentTabStripItemContextMenu}">
            <DockableControl TrackingMode="Tab">
              <StackPanel Background="Transparent"
                          Orientation="Horizontal"
                          Spacing="2"
                          DockProperties.IsDragArea="True"
                          DockProperties.IsDropArea="True">
                <Panel Margin="2">
                  <ContentPresenter FontWeight="DemiBold" ContentTemplate="{Binding $parent[DocumentControl].HeaderTemplate}"
                                    Content="{Binding}" />
                </Panel>
                <Button x:Name="PART_CloseButton"
                        Command="{Binding Owner.Factory.CloseDockable}"
                        CommandParameter="{Binding}"
                        IsVisible="{Binding CanClose}">
                  <Button.Styles>
                    <Style Selector="Button">
                      <Setter Property="BorderThickness" Value="0" />
                      <Setter Property="Padding" Value="0" />
                      <Setter Property="Margin" Value="0" />
                      <Setter Property="Width" Value="14" />
                      <Setter Property="Height" Value="14" />
                      <Setter Property="Background" Value="Transparent" />
                      
                    </Style>
                  </Button.Styles>
                  <Path x:Name="PART_ClosePath">
                    <Path.Styles>
                      <Style Selector="Path">
                        <Setter Property="Fill" Value="{DynamicResource SukiLowText}"></Setter>
                        <Setter Property="Margin" Value="2,4,2,1" />
                        <Setter Property="Stretch" Value="Uniform" />
                        <Setter Property="UseLayoutRounding" Value="False" />
                        <Setter Property="Data"
                                Value="M19,6.41L17.59,5L12,10.59L6.41,5L5,6.41L10.59,12L5,17.59L6.41,19L12,13.41L17.59,19L19,17.59L13.41,12L19,6.41Z" />
                      </Style>
                    </Path.Styles>
                  </Path>
                </Button>
              </StackPanel>
            </DockableControl>
          </Border>
       
        </Panel>
      </ControlTemplate>
    </Setter>

    <Style Selector="^:selected /template/ Border#SelectedBorder">
      <Setter Property="IsVisible" Value="True"></Setter>
      
    </Style>



  </ControlTheme>

</ResourceDictionary>
