﻿using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text;
using Microsoft.Extensions.Configuration;
using Newtonsoft.Json;


namespace SukiUI.Demo.Bll
{
    public class AppStaticData
    {
        /// <summary>
        /// 远程监控配置
        /// </summary>
        public static IConfiguration config;
        public static SiheyiConfig siheyiconfig=new SiheyiConfig();
        public static List<Sensor> SensorsSxtList = new List<Sensor>();
        public static List<sxtIP> IpsSxtList = null;
        //车辆列表
        private static List<BaseDataModel> _ClList = null;
        /// <summary>
        /// 车辆列表
        /// </summary>
        public static List<BaseDataModel> ClList
        {
            get
            {
                if (_ClList != null) return _ClList;
                _ClList = JKTrans.GetClList();
                return _ClList;
            }
        }
     


       

        //扣分列表
        private static List<BaseDataModel> _kfxmList = null;
        /// <summary>
        /// 扣分列表
        /// </summary>
        public static List<BaseDataModel> KfxmList
        {
            get
            {
                if (_kfxmList != null) return _kfxmList;
                _kfxmList = JKTrans.GetKfxmList();
                return _kfxmList;
            }
        }
        //考官列表
        private static List<BaseDataModel> _KsyList = null;
        /// <summary>
        /// 考官列表
        /// </summary>
        public static List<BaseDataModel> KsyList
        {
            get
            {
                if (_KsyList != null) return _KsyList;
                _KsyList = JKTrans.GetKsyList();
                return _KsyList;
            }
        }

      


      


        #region 录像机缓存数据
        /// <summary>
        /// 场地录像机登录用户ID
        /// </summary>
        public static int LxjUserId_cd = -1;
        /// <summary>
        /// 录像机登录用户ID
        /// </summary>
        public static int LxjUserId = -1;
        /// <summary>
        /// 摄像头登录用户ID
        /// </summary>
        public static int SxtUserId = -1;
        /// <summary>
        /// 摄像头登录用户ID
        /// </summary>
        public static int SxtUserId_cw = -1;
        #endregion
        #region 合码器
        /// <summary>
        /// 合码器登录用户ID
        /// </summary>
        /// 
        public static int HmqUserId = -1;

        /// <summary>
        /// 窗片窗口列表
        /// </summary>

        public static List<string> PicWinList;
        //上传图片宽度
        public static int SctpWidth = 480;
        //上传图片高度
        public static int SctpHeight = 264;

      

        ///<summary>
        ///区域/项目摄像头列表
        /// </summary>
        //public static List<Sensor> SensorsSxtList
        //{
        //    get
        //    {
        //        List<Sensor> sensors = new List<Sensor>();
        //        try
        //        {
        //            var sensorStr = File.ReadAllText("siheyi.json");
        //            sensors = JsonConvert.DeserializeObject<List<Sensor>>(sensorStr);

        //        }
        //        catch (Exception e)
        //        {
        //            //LogHelper.ErrorLog("加载siheyi.json文件出错", e);
        //            throw;

        //        }

        //        return sensors;

        //    }

        //}
        public static List<JudgeArea> JudgeAreaList
        {
            get
            {
                List<JudgeArea> judgeAreas = new List<JudgeArea>();
                try
                {
                    var JudgeStr = File.ReadAllText("ppqy.json");
                    judgeAreas = JsonConvert.DeserializeObject<List<JudgeArea>>(JudgeStr);

                }
                catch (Exception e)
                {
                    //LogHelper.ErrorLog("加载ppqy.json文件出错", e);
                    throw;

                }

                return judgeAreas;

            }

        }


        #endregion

        public static List<string> k2xms(string kscx)
        {
            if (kscx.ToUpper().Contains("A") || kscx.ToUpper().Contains("D"))
            {
                var  list = new List<string>
                {
                    "倒库",
                    "桩考",
                    "坡道",
                    "侧方",
                    "单边桥",
                    "曲线",
                    "直角",
                    "限宽门",
                    "连续障碍",
                    "起伏路",
                    "窄路",
                    "高速公路",
                    "山路",
                    "隧道",
                    "雨雾天",
                    "湿滑路",
                    "障碍物",
                    ""
                };
                return list;
            }
           
           
            else if (kscx.ToUpper().Contains("C"))
            {

                return Xck2xms;
               

            }
            else
            {

                return Motok2xms;

            }
             
          

        }
        public static List<string> Xck2xms= new List<string>
                {
                    "倒库",
                    "",//桩考
                    "坡道",
                    "侧方",
                    "",//
                    "曲线",
                    "直角",
                     "",//"限宽门",
                     "",//"连续障碍",
                     "",// "起伏路",
                     "",//"窄路",
                     "",//"高速公路",
                     "",// "山路",
                     "",//"隧道",
                   "", //"雨雾天",
                   "", //"湿滑路",
                   "",// "障碍物",
                   "", //"车辆故障"
                };

        private static readonly List<string> Motok2xms= new List<string>
                {
                    "",
                    "桩考",//桩考
                    "坡道",
                    "",
                    "单边桥",
                    "",
                    "",
                    "",//"限宽门",
                    "",//"连续障碍",
                    "",// "起伏路",
                    "",//"窄路",
                    "",//"高速公路",
                    "",// "山路",
                    "",//"隧道",
                    "", //"雨雾天",
                    "", //"湿滑路",
                    "",// "障碍物",
                    "", //"车辆故障"
                };
        

        
    /// <summary>
    /// 获取考试项目
    /// </summary>
    /// <param name="kscx">车型1大车，2小车，3摩托车</param>
    /// <returns></returns>
        public static List<string> k3xms(string kscx)
        {


            if (kscx.ToUpper().Contains("A") || kscx.ToUpper().Contains("B"))
            {

                var list = new List<string>
                {
                    "上车准备",
                    "起步",
                    "直线",
                    "加减挡",
                    "变更车道",
                    "靠边",
                    "通过路口",
                    "左转",
                    "右转",
                    "人行横道",
                    "学校区域",
                    "公共汽车站",
                    "会车",
                    "超车",
                    "掉头",
                    "夜间行驶"
                };
                return list;

            }

            else
            {
                return MotoK3xms;
            }

        }
        
        private  static readonly List<string> MotoK3xms= new List<string>
                {
                    "上车准备",
                    "起步",
                    "直线",
                    "加减挡",
                    "变更车道",
                    "靠边",
                    "通过路口",
                    "左转",
                    "右转",
                    "人行横道",
                    "学校区域",
                    "公共汽车站",
                    "会车",
                    "超车",
                    "掉头",
                    "夜间行驶"
                };
        //记录全局考试员身份证
         public static string KsySfzmhm { get; set; }
         public static string KsyXm {
            get;
            set;
        }
         //考官列表
         private static List<BaseDataModel> _UserList = null;
         /// <summary>
         /// 考官列表
         /// </summary>
         public static List<BaseDataModel> UserList
         {
             get
             {
                 if (_UserList != null) return _UserList;
                 _UserList = JKTrans.GetAllUserList();
                 return _UserList;
             }
         }
    }
}
