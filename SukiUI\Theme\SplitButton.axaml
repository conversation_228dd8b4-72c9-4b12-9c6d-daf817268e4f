﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:converters="using:Avalonia.Controls.Converters"
                     xmlns:controls="clr-namespace:SukiUI.Controls"
                    xmlns:theme="clr-namespace:SukiUI.Theme"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI">

  <!-- This style is based on the Fluent v1 style. -->

  <Design.PreviewWith>
    <Border Padding="20">
      <SplitButton Content="test"/>
    </Border>
  </Design.PreviewWith>

  <x:Double x:Key="SplitButtonPrimaryButtonSize">24</x:Double>
  <x:Double x:Key="SplitButtonSecondaryButtonSize">24</x:Double>
  <x:Double x:Key="SplitButtonSeparatorWidth">1</x:Double>
  <x:Double x:Key="SplitButtonMinHeight">24</x:Double>
  <Thickness x:Key="SplitButtonBorderThemeThickness">1</Thickness>

  <converters:MarginMultiplierConverter x:Key="PrimaryButtonBorderMultiplier" Left="True" Top="True" Bottom="True"
                                        Indent="1" />
  <converters:MarginMultiplierConverter x:Key="SecondaryButtonBorderMultiplier" Right="True" Top="True" Bottom="True"
                                        Indent="1" />
  <converters:MarginMultiplierConverter x:Key="SeparatorBorderMultiplier" Top="True" Bottom="True" Indent="1" />
  <converters:CornerRadiusFilterConverter x:Key="TopCornerRadiusFilterConverter" Filter="TopLeft, TopRight" />
  <converters:CornerRadiusFilterConverter x:Key="RightCornerRadiusFilterConverter" Filter="TopRight, BottomRight" />
  <converters:CornerRadiusFilterConverter x:Key="BottomCornerRadiusFilterConverter" Filter="BottomLeft, BottomRight" />
  <converters:CornerRadiusFilterConverter x:Key="LeftCornerRadiusFilterConverter" Filter="TopLeft, BottomLeft" />
  
  <ControlTheme x:Key="SimpleSplitButtonComponent" TargetType="Button">
    <Setter Property="Template">
      <ControlTemplate>
        <ContentPresenter x:Name="PART_ContentPresenter"
                          Background="{TemplateBinding Background}"
                          BorderBrush="{TemplateBinding BorderBrush}"
                          BorderThickness="{TemplateBinding BorderThickness}"
                          CornerRadius="{TemplateBinding CornerRadius}"
                          Content="{TemplateBinding Content}"
                          ContentTemplate="{TemplateBinding ContentTemplate}"
                          Padding="{TemplateBinding Padding}"
                          RecognizesAccessKey="True"
                          HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                          VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}" />
      </ControlTemplate>
    </Setter>

    <Style Selector="^:pointerover /template/ ContentPresenter">

    </Style>

    <Style Selector="^:pressed /template/ ContentPresenter">
     
    </Style>

    <Style Selector="^:disabled /template/ ContentPresenter">
      <Setter Property="Opacity" Value="0.8"></Setter>
    </Style>

    <Style Selector="^[Tag=flyout-open] /template/ ContentPresenter">
        <Setter Property="Opacity" Value="0.9"></Setter>
    </Style>

    <Style Selector="^[Tag=checked]">
      <Style Selector="^ /template/ ContentPresenter">
        <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor}" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />
        <Setter Property="Foreground" Value="White" />
      </Style>

      <Style Selector="^:pointerover /template/ ContentPresenter">
        
      </Style>

      <Style Selector="^:pressed /template/ ContentPresenter">
          <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor}" />
          <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />
          <Setter Property="Foreground" Value="White" />
      </Style>

    
    </Style>

    <Style Selector="^[Tag=checked-flyout-open] /template/ ContentPresenter">
      <Setter Property="Opacity" Value="0.9"></Setter>
    </Style>
  </ControlTheme>

  <ControlTheme x:Key="{x:Type SplitButton}" TargetType="SplitButton">
      <Setter Property="CornerRadius" Value="8" />
      <Setter Property="BorderThickness" Value="1" />
      <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
      <Setter Property="Background" Value="Transparent" />
      <Setter Property="Padding" Value="18,10,18,8" />
      <Setter Property="FontWeight" Value="{DynamicResource DefaultDemiBold}" />
      <Setter Property="FontSize" Value="14" />
      <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
      
      <Setter Property="MinHeight" Value="{DynamicResource SplitButtonMinHeight}" />
    <Setter Property="HorizontalAlignment" Value="Left" />
    <Setter Property="VerticalAlignment" Value="Center" />
    <Setter Property="HorizontalContentAlignment" Value="Stretch" />
    <Setter Property="VerticalContentAlignment" Value="Center" />
    <Setter Property="KeyboardNavigation.IsTabStop" Value="True" />
    <Setter Property="Focusable" Value="True" />

    <Setter Property="Template">
      <ControlTemplate>
        <Grid>
          <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
           
            <ColumnDefinition Width="Auto" />
          </Grid.ColumnDefinitions>

          <Button x:Name="PART_PrimaryButton"
                  Grid.Column="0"
                  Theme="{StaticResource SimpleSplitButtonComponent}"
                  MinWidth="{DynamicResource SplitButtonPrimaryButtonSize}"
                  Foreground="{TemplateBinding Foreground}"
                  Background="{TemplateBinding Background}"
                  BorderThickness="{TemplateBinding BorderThickness, Converter={StaticResource PrimaryButtonBorderMultiplier}}"
                  BorderBrush="{TemplateBinding BorderBrush}"
                  Content="{TemplateBinding Content}"
                  ContentTemplate="{TemplateBinding ContentTemplate}"
                  Command="{TemplateBinding Command}"
                  CommandParameter="{TemplateBinding CommandParameter}"
                  CornerRadius="{TemplateBinding CornerRadius, Converter={StaticResource LeftCornerRadiusFilterConverter}}"
                  FontFamily="{TemplateBinding FontFamily}"
                  FontSize="{TemplateBinding FontSize}"
                  FontWeight="{TemplateBinding FontWeight}"
                  HorizontalAlignment="Stretch"
                  VerticalAlignment="Stretch"
                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                  Padding="{TemplateBinding Padding}"
                  Focusable="False"
                  KeyboardNavigation.IsTabStop="False" />

    
          <Button x:Name="PART_SecondaryButton"
                  Grid.Column="1"
                  Theme="{StaticResource SimpleSplitButtonComponent}"
                  MinWidth="{DynamicResource SplitButtonSecondaryButtonSize}"
                  BorderBrush="{TemplateBinding BorderBrush}"
                  Foreground="{TemplateBinding Foreground}"
                  Background="{TemplateBinding Background}"
                  BorderThickness="{TemplateBinding BorderThickness, Converter={StaticResource SecondaryButtonBorderMultiplier}}"
                  CornerRadius="{TemplateBinding CornerRadius, Converter={StaticResource RightCornerRadiusFilterConverter}}"
                  Padding="10,4,10,0"
                  HorizontalContentAlignment="Center"
                  VerticalContentAlignment="Center"
                  HorizontalAlignment="Stretch"
                  VerticalAlignment="Stretch"
                  Focusable="False"
                  KeyboardNavigation.IsTabStop="False">
            <PathIcon Height="12" Width="12"
                      Data="M1939 486L2029 576L1024 1581L19 576L109 486L1024 1401L1939 486Z" />
          </Button>
        </Grid>
      </ControlTemplate>
    </Setter>

    <Style Selector="^:flyout-open /template/ Button">
      <Setter Property="Tag" Value="flyout-open" />
    </Style>

    <Style Selector="^:checked /template/ Button">
      <Setter Property="Tag" Value="checked" />
    </Style>

    <Style Selector="^:checked:flyout-open /template/ Button">
      <Setter Property="Tag" Value="checked-flyout-open" />
    </Style>
  </ControlTheme>
</ResourceDictionary>