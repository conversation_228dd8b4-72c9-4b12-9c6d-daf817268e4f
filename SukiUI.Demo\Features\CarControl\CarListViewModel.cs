﻿using Avalonia.Controls;
using AvaloniaEdit;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using CommunityToolkit.Mvvm.Messaging;
using DynamicData;
using ExCSS;
using Mapsui.Utilities;
using Material.Icons;
using Newtonsoft.Json;
using Serilog;
using SukiUI.Demo.Bll;
using SukiUI.Dialogs;
using SukiUI.Toasts;
using System;
using System.Collections.Generic;
using System.Collections.ObjectModel;
using System.IO;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;

namespace SukiUI.Demo.Features.CarControl
{

    public partial class CarListViewModel : DemoPageBase
    {
        public ObservableCollection<CarControlViewModel> CarControls { get; set; } = new ObservableCollection<CarControlViewModel>();
        public ObservableCollection<CarModel> Cars { get; set; } = new ObservableCollection<CarModel>();
        public static string GPSPath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory,"map", "comData.ini");
        private StreamReader sr = new StreamReader(GPSPath, Encoding.Default); //读取GPS数据
        private string readGps()
        {

            var line = "";
            if ((line = sr.ReadLine()) != null)
            {
                if (line == "[data]") line = sr.ReadLine();
            }
            else
            {
                sr = new StreamReader(GPSPath, Encoding.Default);
                line = sr.ReadLine();
                if (line == "[data]") line = sr.ReadLine();
            }
            //Log.Information(line);
            return line;
        }
        public CarListViewModel() : base("车辆列表", MaterialIconKind.Car, int.MinValue)
        {
            //IsActive = true;
            #region 启动UDP接收数据
            Task.Run(async () =>
            {
                await Task.Delay(15000);
                if (AppStaticData.siheyiconfig.IsDebug.Value)
                {
                    ReadDebugData();

                }
                else
                {
                    var udp = new SiHeYiUdpServer(IPAddress.Any, AppStaticData.siheyiconfig.JtdkCzudp.Value);
                    udp.DataReceived += OnDataRecive;
                    udp.Start();
                }
            });
            //测试模式
           

            #endregion

            #region 初始化消息，待主窗体初始化完成后，再执行子窗体的初始化
            //此功能为早于SukiuiDemoViewModel创建，执行，所以等接收到SukiuiDemo创建完成后，再执行初始化，便于调用主窗体消息处理函数
            //  IsActive = true;
            //  WeakReferenceMessenger.Default.Register<CarListViewModel, string, string>(
            // this,
            //  "Inited",
            // (recipient, message) => DoInitLoadData(message)

            //);
            #endregion
            //DoInitLoadData();
            //IsActive = true;//isActive设为True,才可以接收到消息,如果没有注册时设为True则会报错

            //CarControls.Add(new CarControlViewModel("01", "4101", "粤11111"));
            //CarControls.Add(new CarControlViewModel("02", "4102", "粤22222"));
            //CarControls.Add(new CarControlViewModel("03", "4103", "粤33333"));
        }

        private void ReadDebugData()
        {
            //Task.Factory.StartNew(() =>
            //Task.Run(() =>
            {
                while (true)
                {
                    Thread.Sleep(200);
                    var line = readGps();
                    if (line != null)
                    {
                        WeakReferenceMessenger.Default.Send(line, "Udp");
                    }
                }
            }
            //}, TaskCreationOptions.LongRunning);
        }

        private void OnDataRecive(string message)
        {
            Console.WriteLine("收到数据：" + message);
            WeakReferenceMessenger.Default.Send(message, "Udp");
        }
        /// <summary>
        /// 初始化合码器
        /// </summary>
        private void InitHeMaQi()
        {
            if (m_bInitSDK)
                return;
            NvrInit();
           
            //这里使用硬盘录像机配置代替合码器，使用LXJUserID代替合码器IDe
            AppStaticData.HmqUserId = login_V30(AppStaticData.siheyiconfig.Yplxjaddress.Value, 8000, AppStaticData.siheyiconfig.Yplxjuser.Value,
                AppStaticData.siheyiconfig.Yplxjpwd.Value);
          
            //AppStaticData.PicWinList = HeMaQiHelper.GetAllPicWinPar(AppStaticData.HmqUserId);
        }
        public int login_V30(string ip, int port, string userName, string password)
        {
            if (!m_bInitSDK)
            {
                return -1;
            }
            //登录设备 Login the device
            CHCNetSDK.NET_DVR_DEVICEINFO_V30 m_struDeviceInfo = new CHCNetSDK.NET_DVR_DEVICEINFO_V30();
            var m_lUserID = CHCNetSDK.NET_DVR_Login_V30(ip, port, userName, password, ref m_struDeviceInfo);

            if (m_lUserID == -1)
            {
               var iLastErr = CHCNetSDK.NET_DVR_GetLastError();
               var strErr = "NET_DVR_Login_V30 failed, error code= " + iLastErr;
                //登录失败，输出错误号 Failed to login and output the error code
                //FileTools.WriteLog("hmq.txt", $"无法登录到合码器");
                AppManager.AddKeepLiveData("5", $"无法登录到合码器:{strErr}");
                //MessageBox.Show(strErr + "请确认合码器已开启，");
                //Environment.Exit(0);
                WeakReferenceMessenger.Default.Send($"无法登录到合码器。{strErr}", "XiaoXi");
                //Thread.Sleep(5000);
                if (!AppStaticData.siheyiconfig.IsDebug.Value)
                {
                    //Environment.Exit(0);
                    WeakReferenceMessenger.Default.Send($"Dialog-Exit无法登录到合码器。{strErr}", "XiaoXi");
                }


            }
            else
            {
                // 登录成功，将连接添加到资源管理器
                try
                {
                    var sdkManager = SukiUI.Demo.Infrastructure.SDKResourceManager.Instance;
                    var connectionId = $"{ip}:{port}_{userName}";
                    var deviceInfo = $"设备: {ip}:{port}, 用户: {userName}";

                    sdkManager.AddConnection(connectionId, m_lUserID, deviceInfo);
                    Log.Information($"设备登录成功并已添加到资源管理器 - {deviceInfo}");
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "添加设备连接到资源管理器失败");
                }
            }

            return m_lUserID;
        }
        public bool NvrInit()
        {
            try
            {
                #region 测试动态库加载
                // 1. 基本检查
                //var carListViewModel = new CarListViewModel();
                var status = CheckLibraryIntegrity();

                // 2. 详细报告
                var detailReport = GenerateDetailedLibraryReport();

                // 3. 诊断信息
                var diagnostics = GetLibraryDiagnostics();

                // 4. 独立测试
                TestLibraryCheck.RunQuickTest();
                #endregion

                // 使用SDK资源管理器进行初始化
                var sdkManager = SukiUI.Demo.Infrastructure.SDKResourceManager.Instance;
                m_bInitSDK = sdkManager.InitializeSDK();

                if (m_bInitSDK == false)
                {
                    //MessageBox.Show("初始化合码器失败");
                    //FileTools.WriteLog("hmq.txt", "sdk初始化失败");
                    //AppManager.AddKeepLiveData("1", "合码器SDK初始化失败");

                    // 获取详细的错误信息
                    var errorCode = CHCNetSDK.NET_DVR_GetLastError();
                    WeakReferenceMessenger.Default.Send($"Dialog-Exit初始化合码器失败，错误代码: {errorCode}", "XiaoXi");

                    return false;
                }
                else
                {
                    CHCNetSDK.NET_DVR_SetConnectTime(5000, 1);
                    return true;
                }
            }
            catch (Exception ex)
            {
                WeakReferenceMessenger.Default.Send($"Dialog-Exit SDK初始化异常: {ex.Message}", "XiaoXi");
                return false;
            }
        }

        /// <summary>
        /// 测试跨平台库加载功能
        /// </summary>
        /// <returns>测试结果</returns>
        public string TestCrossPlatformLibrary()
        {
            try
            {
                return CrossPlatformLibraryTest.TestLibraryLoading();
            }
            catch (Exception ex)
            {
                return $"测试异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取库加载状态信息
        /// </summary>
        /// <returns>状态信息</returns>
        public string GetLibraryStatus()
        {
            var status = new System.Text.StringBuilder();

            try
            {
                status.AppendLine("=== 库加载状态 ===");
                status.AppendLine($"NativeLibraryManager: {SukiUI.Demo.Infrastructure.NativeLibraryManager.GetLibraryInfo()}");
                status.AppendLine($"SDK初始化状态: {(m_bInitSDK ? "已初始化" : "未初始化")}");

                if (m_bInitSDK)
                {
                    var version = CHCNetSDK.NET_DVR_GetSDKVersion();
                    status.AppendLine($"SDK版本: {version}");
                }
            }
            catch (Exception ex)
            {
                status.AppendLine($"获取状态时发生异常: {ex.Message}");
            }

            return status.ToString();
        }

        /// <summary>
        /// 生成详细的库文件检查报告
        /// </summary>
        /// <returns>详细检查报告</returns>
        public string GenerateDetailedLibraryReport()
        {
            try
            {
                return CrossPlatformLibraryTest.GenerateDetailedLibraryReport();
            }
            catch (Exception ex)
            {
                return $"生成详细报告异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 获取库文件诊断信息
        /// </summary>
        /// <returns>诊断信息</returns>
        public string GetLibraryDiagnostics()
        {
            try
            {
                return CrossPlatformLibraryTest.GetLibraryDiagnostics();
            }
            catch (Exception ex)
            {
                return $"获取诊断信息异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 检查库文件完整性
        /// </summary>
        /// <returns>检查结果摘要</returns>
        public string CheckLibraryIntegrity()
        {
            try
            {
                var checkResult = SukiUI.Demo.Infrastructure.NativeLibraryManager.CheckAllLibraryFiles();
                var summary = new System.Text.StringBuilder();

                summary.AppendLine($"=== 库文件完整性检查 ===");
                summary.AppendLine($"平台: {checkResult.Platform}");
                summary.AppendLine($"状态: {(checkResult.IsAllLibrariesPresent ? "✓ 完整" : "✗ 不完整")}");
                summary.AppendLine($"文件统计: {checkResult.ExistingFiles}/{checkResult.TotalFiles}");

                if (checkResult.MissingFiles > 0)
                {
                    summary.AppendLine($"缺失文件数: {checkResult.MissingFiles}");
                    summary.AppendLine("主要缺失文件:");
                    foreach (var missing in checkResult.MissingLibraries.Take(10))
                    {
                        summary.AppendLine($"  - {missing}");
                    }
                    if (checkResult.MissingLibraries.Count > 10)
                    {
                        summary.AppendLine($"  ... 还有 {checkResult.MissingLibraries.Count - 10} 个文件");
                    }
                }

                summary.AppendLine($"HCNetSDKCom组件库: {(checkResult.ComponentLibraryExists ? "✓ 存在" : "✗ 缺失")}");

                return summary.ToString();
            }
            catch (Exception ex)
            {
                return $"检查库文件完整性异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 初始化数据，获取车辆列表，扣分列表。API访问，最好放到控件的Loaded中执许
        /// </summary>
        public void DoInitLoadData(string msg = "")
        {
            try
            {                
                //初始化合码器
                InitHeMaQi();
                //删除上传文件夹
                DelGjUploadFolder();
            }
            catch (Exception ex)
            {
                WeakReferenceMessenger.Default.Send($"初始化失败：{ex.Message}", "XiaoXi");
            }

            try
            {
                //WeakReferenceMessenger.Default.Send("获取扣分列表成功", "XiaoXi");
                List<BaseDataModel> _datalistKf = AppStaticData.KfxmList;
                //WeakReferenceMessenger.Default.Send($"获取扣分项目列表成功", "XiaoXi");
            }
            catch (Exception ex)
            {

                WeakReferenceMessenger.Default.Send($"获取扣分项目列表失败:{ex.Message}", "XiaoXi");
            }
            try
            {
                List<BaseDataModel> _datalistCl = AppStaticData.ClList;
                //WeakReferenceMessenger.Default.Send("获取车辆列表成功", "XiaoXi");
                InitBindClView();

            }
            catch (Exception ex)
            {

                WeakReferenceMessenger.Default.Send($"获取车辆列表失败：{ex.Message}", "XiaoXi");
            }
            #region 获取学员列表
            Task.Factory.StartNew(() =>
            {
                try
                {
                    BindXYList();
                }
                catch (Exception ex)
                {

                    WeakReferenceMessenger.Default.Send($"获取学员列表失败：{ex.Message}", "XiaoXi");
                }

            });
            #endregion

        }
        private void DelGjUploadFolder()
        {
            var fileFolder = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GjPic");
            try
            {
                if (!Directory.Exists(fileFolder))
                {
                    Log.Error($"{fileFolder}不存在");
                    Directory.CreateDirectory(fileFolder);
                }
                else
                {
                    DeleteFile(fileFolder);
                    Directory.CreateDirectory(fileFolder);
                }
            }
            catch (Exception e)
            {
                WeakReferenceMessenger.Default.Send("删除GjPic文件夹出错", "XiaoXi");
            }

        }
        /// <summary>
        /// 根据路径删除文件
        /// </summary>
        /// <param name="path"></param>
        public void DeleteFile(string path)
        {
            FileAttributes attr = File.GetAttributes(path);
            if (attr == FileAttributes.Directory)
            {
                Directory.Delete(path, true);
            }
            else
            {
                File.Delete(path);
            }
        }
        public void InitBindClView()
        {
            if (CarControls.Count > 0)
            {
                return;
            }
            CarControls.Clear();
            var cllist = AppStaticData.ClList;
            if (cllist == null || cllist.Count == 0) return;

            //string carstr = "28,29";//后期更换为配置文件中的值
            var hmqckksxh= AppStaticData.siheyiconfig.Hmqckksxh.Value;
            string carstr = AppStaticData.siheyiconfig.Showcars.Value;
            string[] cars = carstr.Replace("，", ",").Trim().Split(',');
            for (int i = 0; i < cars.Length; i++)
            {
                string carnum = cars[i].Trim();
                if (carnum == "") continue;
                #region 设置合码窗口信息
                var PicWin = (i + 1) + "_" + (i + 65);
                if (AppStaticData.siheyiconfig.Sf6816M.Value != 1)
                {
                    try
                    {
                        var reswin = i + 1;
                        //新的窗口定位方式，墙号1，DIv号，
                        PicWin = 1 + "_" + (reswin + hmqckksxh);
                        //老的窗口定位方式
                        //PicWin = AppStaticData.PicWinList[i + hmqckksxh];
                    }
                    catch (Exception e)
                    {


                    }
                }
                else //6816M
                {
                    var reswin = i + 1;
                    PicWin = 1 + "_" + (reswin + hmqckksxh);

                }
                #endregion
                var _data = cllist.FirstOrDefault(_d => _d.GetValueStr("clxh") == carnum);
                if (_data != null)
                {
                    _data.SetColumnValue("picwin", PicWin);
                    _data.SetColumnValue("picwinindex", i + hmqckksxh);
                    //CarControls.Add(new CarControlViewModel(_data.GetValueStr("clxh"), _data.GetValueStr("clbm"), _data.GetValueStr("cphm"), _data.GetValueStr("kscx")));
                    CarControls.Add(new CarControlViewModel(_data));

                }
            }
        }
        private List<BaseDataModel> oldXyList = new List<BaseDataModel>();
        private bool m_bInitSDK;

        private void BindXYList()
        {
            #region  控制学员数据刷新频率，避免数据库压力过大
            //var xysxsj = "3000";
            var timer = AppStaticData.siheyiconfig.Xysxsj.Value;
            //var timer = int.Parse(xysxsj);
            while (true)
            {

                Thread.Sleep(timer);
                try
                {
                    var list = JKTrans.GetXyList();
                    #region 考试结束
                    if (oldXyList!=null && oldXyList.Count > 0)
                    {
                       
                        oldXyList.ForEach(_d => {
                            var _data = !list.Any(_d1 => _d1.GetValueStr("sfzmhm") == _d.GetValueStr("sfzmhm"));
                            if (_data )
                            {
                                _d.SetColumnValue("ksjs", "1");
                                WeakReferenceMessenger.Default.Send(_d, "Xueyuan");
                            }
                        });
                       
                    }
                    #endregion
                    //发送学员信息，这时考虑一次性发送，还是分批次发送
                    list.ForEach(_d => {
                       
                        WeakReferenceMessenger.Default.Send(_d, "Xueyuan");
                    });
                    oldXyList = list;
                }
                catch (Exception e)
                {
                    WeakReferenceMessenger.Default.Send($"获取学员列表失败：{e.Message}", "XiaoXi");
                }
            }

            #endregion
        }

        //[RelayCommand]
        //private void Stop()
        //{
        //    //消息测试
        //    //var toa = App.provider.GetService<ISukiToastManager>();
        //    //toa.CreateSimpleInfoToast()
        //    //    .WithTitle("车辆已停止")
        //    //    .WithContent("车辆已停止")
        //    //    .Queue();
        //    WeakReferenceMessenger.Default.Send("停止", "XiaoXi");

        //}

        
    }
    public class CarModel
    {
        public string No { get; set; } = "01";
        public string Plate { get; set; } = "豫A12345";
    }
}
