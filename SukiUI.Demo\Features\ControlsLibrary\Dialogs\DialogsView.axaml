<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.Dialogs.DialogsView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:dialogs="clr-namespace:SukiUI.Demo.Features.ControlsLibrary.Dialogs"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="dialogs:DialogsViewModel"
             mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Dialogs">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        SukiUI provides a rich dialog experience out of the box, allowing the display of any arbitrary control, view or ViewModel (with suitable ViewLocator defined) as a dialog.
                    </TextBlock>
                    <TextBlock>
                        As the API for this resides entirely in C#, the source for the examples can be viewed on GitHub at the URL below.
                    </TextBlock>
                    <HyperlinkButton NavigateUri="https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/Dialogs/DialogsViewModel.cs"
                                     Content="Source Here." />
                    <TextBlock>
                        Click any of the following buttons to open up a dialog.
                    </TextBlock>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard>
                    <suki:GroupBox Header="Standard Dialog">
                        <Button VerticalAlignment="Top" Margin="15,10,15,0"
                                Command="{Binding OpenStandardDialogCommand}"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Long Dialog">
                        <Button VerticalAlignment="Top" Margin="15,10,15,0"
                                Command="{Binding OpenLongDialogCommand}"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Multi Option Dialog">
                        <Button VerticalAlignment="Top" Margin="15,10,15,0"
                                Command="{Binding OpenMultiOptionDialogCommand}"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Background Closable Dialog">
                        <Button VerticalAlignment="Top" Margin="15,10,15,0"
                                Command="{Binding OpenBackgroundCloseDialogCommand}"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Messagebox Style Dialogs">
                        <StackPanel Spacing="5">
                            <ComboBox ItemsSource="{Binding NotificationTypes}" SelectedItem="{Binding SelectedType}"/>
                            <Button VerticalAlignment="Top" Margin="15,10,15,0"
                                    Command="{Binding OpenMessageBoxStyleDialogCommand}"
                                    Content="Show Dialog" />
                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="ViewModel Dialog">
                        <Button VerticalAlignment="Top" Margin="15,10,15,0"
                                Command="{Binding OpenViewModelDialogCommand}"
                                Content="Show Dialog" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Window Dialog Demo">
                        <Button VerticalAlignment="Top" Margin="15,10,15,0"
                                Command="{Binding OpenDialogWindowDemoCommand}"
                                Content="Open" />
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>

</UserControl>