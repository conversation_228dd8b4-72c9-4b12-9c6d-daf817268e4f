﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:system="clr-namespace:System;assembly=netstandard">
    <Color x:Key="SukiBackground">Transparent</Color>
    <Color x:Key="SukiStrongBackground">#fcfcfc</Color>
    <Color x:Key="SukiLightBackground">Transparent</Color>
    <Color x:Key="SukiCardBackground">White</Color>
    <Color x:Key="SukiPopupBackground">#fefefe</Color>
    <Color x:Key="SukiGlassCardBackground">#fdfefefe</Color>
    <Color x:Key="SukiGlassCardOpaqueBackground">#fcfcfc</Color>
    <Color x:Key="SukiControlTouchBackground">#FFFFFF</Color>
    <Color x:Key="SukiDialogBackground">Gray</Color>

    <Color x:Key="SukiBorderBrush">#ebebeb</Color>
    <Color x:Key="SukiControlBorderBrush">#cecece</Color>
    <Color x:Key="SukiMediumBorderBrush">#e2e2e2</Color>
    <Color x:Key="SukiLightBorderBrush">#f1f1f1</Color>
    <Color x:Key="SukiMenuBorderBrush">#d7d7d7</Color>
    <Color x:Key="GlassBorderBrush">#e3e3e7</Color>

    <Color x:Key="SukiText">#222222</Color>
    <Color x:Key="SukiLowText">#555555</Color>
    <Color x:Key="SukiDisabledText">#ABABAB</Color>

    <BoxShadows x:Key="SukiLowShadow">0 0 3 0 #eaeaea</BoxShadows>
    <BoxShadows x:Key="SukiSwitchShadow">0 1 5 0 #aaaaaa</BoxShadows>
    <BoxShadows x:Key="SukiSmallPopupShadow">0 2 5 0 LightGray</BoxShadows>
    <BoxShadows x:Key="SukiPopupShadow">1 1 8 0 LightGray</BoxShadows>
    <BoxShadows x:Key="SukiBigPopupShadow">1 4 17 0 #999999</BoxShadows>
    
    <LinearGradientBrush x:Key="PopupGradientBrush" StartPoint="0%,0%" EndPoint="100%,100%">
        <GradientStop Color="#03111111" Offset="0"></GradientStop>
        <GradientStop Color="#02111111" Offset="1"></GradientStop>

    </LinearGradientBrush>

    <system:Boolean x:Key="IsDark">False</system:Boolean>
    <system:Boolean x:Key="IsLight">True</system:Boolean>

    <system:Double x:Key="GlassOpacity">0.48</system:Double>
    
    <system:Double x:Key="ControlGlassOpacity">0.05</system:Double>
    <system:Double x:Key="DiscreteControlGlassOpacity">0.4</system:Double>
    <Color x:Key="ControlSukiGlassCardBackground">#fd111111</Color>
    
</ResourceDictionary>
