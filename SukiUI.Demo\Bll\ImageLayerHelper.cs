﻿using Avalonia.Controls.Shapes;
using BruTile;
using Mapsui;
using Mapsui.Extensions;
using Mapsui.Layers;
using Mapsui.Nts;
using Mapsui.Nts.Extensions;
using Mapsui.Projections;
using Mapsui.Providers;
using Mapsui.Styles;
using NetTopologySuite.Geometries;
using SkiaSharp;
using System.Xml.Linq;


namespace SukiUI.Demo.Bll
{






    // 需要引用的命名空间
   

    public  class  ImageLayerHelper 
    {
        public static ILayer CreateImageLayer2(string layername , NetTopologySuite.Geometries.Polygon polygon,float CourseAngle)
        {
            // 1. 构造一个 Polygon
            //var polygon = new NetTopologySuite.Geometries.Polygon(new LinearRing(new[]
            //{
            //    new Coordinate(0, 0),
            //    new Coordinate(0, 1000000),
            //    new Coordinate(1000000, 1000000),
            //    new Coordinate(1000000, 0),
            //    new Coordinate(0, 0)
            //}));

            // 2. 计算外接矩形
            //var envelope = polygon.Envelope;
            var envelope = polygon.EnvelopeInternal;
            
            var mrect =new MRect (envelope.MinX,envelope.MinY,envelope.MaxX,envelope.MaxY);
            
            // 3. 加载图片为 SKBitmap
            // 假设图片路径为 Assets/test.png，需确保图片已复制到输出目录
            var imagePath = System.IO.Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "PNG","Car.png");
            using var stream = File.OpenRead(imagePath);
            var skBitmap = SKBitmap.Decode(stream);
            var xz= RotateBitmap(skBitmap,90);
            var skBitmapnew = ImageHelper.SKBitmapToByteArray(xz);
            //var skBitmap = stream.ToBytes();

            // 4. 创建 Raster
            var raster = new  MRaster(skBitmapnew, mrect);

            // 5. 创建 RasterFeature
            var rasterFeature = new RasterFeature(raster)
            {
                Styles = { new RasterStyle() }

            };

            // 6. 创建 ImageLayer
            var imageLayer = new ImageLayer
            {
                Name = layername,
                DataSource = new MemoryProvider(rasterFeature)

            };
            var imageLayer1=  new MemoryLayer()
            {
                Features = new List<RasterFeature> { rasterFeature },
                //Name = "Raster Image",
                Name = layername,
                Opacity = 0.9,
                Style = null,
            };
            return imageLayer1;
          
        }
        /// <summary>
        /// 生成旋转后的新图片
        /// </summary>
        /// <param name="srcBitmap"></param>
        /// <param name="angleDegrees"></param>
        /// <returns></returns>
        public static SKBitmap RotateBitmap(SKBitmap srcBitmap, float angleDegrees)
        {
            int w = srcBitmap.Width;
            int h = srcBitmap.Height;
            var rotatedBitmap = new SKBitmap(w, h);

            using (var canvas = new SKCanvas(rotatedBitmap))
            {
                float cx = w / 2f;
                float cy = h / 2f;
                canvas.Clear(SKColors.Transparent);
                canvas.Translate(cx, cy);
                canvas.RotateDegrees(angleDegrees);
                canvas.Translate(-cx, -cy);
                canvas.DrawBitmap(srcBitmap, 0, 0);
            }
            return rotatedBitmap;
        }
       
        public static ILayer CreateImageLayer(string layername, NetTopologySuite.Geometries.Polygon polygon, float CourseAngle,int bitmapId)
        {              

            PointFeature _vehicleFeature = CreateImageFeature(polygon,CourseAngle,bitmapId);           

            var _vehicleLayer = new MemoryLayer
            {
                Name = layername,
                Features = new List<IFeature> { _vehicleFeature },
                Style = null,
                IsMapInfoLayer = true
            };

            return _vehicleLayer;
        }

        public static PointFeature CreateImageFeature(NetTopologySuite.Geometries.Polygon polygon, float CourseAngle, int bitmapId)
        {
            var envelopeinternal = polygon.EnvelopeInternal;          
            var centre = envelopeinternal.Centre;
            var _vehicleFeature = new PointFeature(centre.X, centre.Y);
            SymbolStyle _symbolStyle = CreateImageLayerStyle(CourseAngle, bitmapId);
            _vehicleFeature.Styles.Add(_symbolStyle);
            return _vehicleFeature;
        }

        public static SymbolStyle CreateImageLayerStyle(float CourseAngle, int bitmapId)
        {
            var _symbolStyle = new Mapsui.Styles.SymbolStyle
            {
                RotateWithMap = true,
                BitmapId = bitmapId,
                SymbolRotation = CourseAngle, // 初始角度              
                SymbolOffset = new Offset(0, 0), // 中心点
                SymbolScale = 1.0,
                //Opacity = 0.9
            };
            return _symbolStyle;
        }
        //private static int LoadEmbeddedBitmap(string resourceName)
        //{
        //    // 从程序集资源加载图像
        //    var assembly = typeof(MainWindow).Assembly;
        //    using var stream = assembly.GetManifestResourceStream($"RotatingMarkerDemo.Assets.{resourceName}");

        //    return BitmapRegistry.Instance.Register(stream);
        //}

    }
}
