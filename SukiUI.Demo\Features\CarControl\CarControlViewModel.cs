
using System.Collections.ObjectModel;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;

using SukiUI.Demo.Features.Dashboard;
using CommunityToolkit.Mvvm.Messaging;
using SukiUI.Demo.Bll;

using Avalonia.Media.Imaging;

using Avalonia.Threading;

using System.Text;
using Avalonia.Controls;
using Mapsui.Utilities;
using ExCSS;
using System.Drawing;
using Serilog;
using AvaloniaEdit.Editing;




namespace SukiUI.Demo.Features.CarControl;

public partial class CarControlViewModel : ObservableRecipient, IRecipient<BaseDataModel>
{
    #region 车辆属性
    //车辆编码
    [ObservableProperty] private string _carNumber = "41042519821112";
    //车贴号
    [ObservableProperty] private string _carNo = "01";
    //车型
    [ObservableProperty] private string _carType = "C1";
    //车牌号
    private string _carPlate = "豫A123456";
    //车牌号和车贴号
    [ObservableProperty] private string _carPlateAndNo = "豫A123456";
    [ObservableProperty] private string _dangwei = "空档";
    [ObservableProperty] private string _chesu = "0";
    [ObservableProperty] private string _zhuansu = "0";
    #endregion
    #region 考试属性
    //考试次数
    [ObservableProperty] private string _kscs = "0";
    //考试开始时间
    [ObservableProperty] private string _kscj = "100";
    //考试线路
    [ObservableProperty] private string _kslx = "0";
    [ObservableProperty] private bool _iskslx = true;
    [ObservableProperty] private string _ksy = "";
    //考试车辆状态，是否有学员在考试
    [ObservableProperty] private bool _carStatus = false;
    [ObservableProperty] private double _batteryLevel = 75;

    #endregion
    #region 学员信息
    private BaseDataModel xydata;
    //空格使用\u00A0替代或者使用。 
    //[ObservableProperty] private string _xm= "姓\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0\u00A0名：";
    [ObservableProperty] private string _xm = "";
    [ObservableProperty] private string _sfzmhm = "";
    [ObservableProperty] private string _kc = "";
    [ObservableProperty] private Bitmap? _gawzp;
    [ObservableProperty] private Bitmap? _mjzp;
    #endregion
    #region 考试时间
    //考试开始时间
    [ObservableProperty] private string _kskssj = string.Empty;
    //考试时长
    [ObservableProperty] private string _kssc = string.Empty;
    //项目时长
    [ObservableProperty] private string _xmsc = string.Empty;
    //[ObservableProperty] private string _xmsc1 = string.Empty;


    #endregion
    #region 摄像头
    /// <summary>
    ///     初始化摄像头
    /// </summary>
    private sxtIP _sxt1Temp = new sxtIP();
    private sxtIP _sxt2Temp = new sxtIP();
    private sxtIP _sxt3Temp = new sxtIP();
    private sxtIP _sxt4Temp = new sxtIP();
    #endregion   
    /// <summary>
    /// 金局摄像头
    /// </summary>
    private sxtIP InitSxtIp;
    //是否显示考试时间
    [ObservableProperty] private bool _iskssj = true;
    //是否上传图片，目前根据设置的考试科目与接收的信号中的科目信息是否相同来判断
    [ObservableProperty] private bool _isUpload = false;

    public BaseDataModel NewBmd = new BaseDataModel();
    [ObservableProperty]
    private ObservableCollection<ExamItemViewModel> examItems = new ObservableCollection<ExamItemViewModel>();
    [ObservableProperty]
    private ObservableCollection<KsxhItemViewModel> ksxhs = new ObservableCollection<KsxhItemViewModel>();

    //public CarControlViewModel(string carNo = "01", string carNumber = "41002", string carPlate = "豫A123456", string carType = "C1")
    public CarControlViewModel(BaseDataModel CarData)
    {
       
        CarType = CarData.GetValueStr("kscx");
        CarNo= CarData.GetValueStr("clxh");
        CarNumber= CarData.GetValueStr("clbm");
        _carPlate= CarData.GetValueStr("cphm");
        CarPlateAndNo = $"{_carPlate}-{CarNo}";
        picWin = CarData.GetValueStr("picwin");
        picWinIndex = int.Parse(CarData.GetValueStr("picwinindex"));
        //初始化窗体参数
        InitPicNo();
        //InitExamItems();
        //InitKsxhs();
        //GetIps();
        //获取IPS配置信息
        sxtList = AppStaticData.IpsSxtList;
        //车内摄像头配置
        sxtpz_cn = AppStaticData.siheyiconfig.Yplxjcnsppz.Value.Replace("，", ",").Trim(',').Split(',');
        //车外摄像头配置
        sxtpz_cw = AppStaticData.siheyiconfig.Yplxjcwsppz.Value.Replace("，", ",").Trim(',').Split(',');

        //车外摄像头配置2
        sxtpz_3 = AppStaticData.siheyiconfig.Yplxjcwsppz2.Value.Replace("，", ",").Trim(',').Split(',');

        //第四路摄像头配置
        sxtpz_4 = AppStaticData.siheyiconfig.Yplxjcwsppz3.Value.Replace("，", ",").Trim(',').Split(',');
        //初始化摄像头，不知放这里会不会重复设置
        IniChanel();
        #region 接收学员信息
        IsActive = true;//这里设为True，下方语句才能正确接收消息
        //WeakReferenceMessenger.Default.Register<CarControlViewModel, BaseDataModel, string>(
        //    this,
        //    "Xueyuan",
        //    (recipient, message) => ReceiveMessageXueyuan(message)

        // );
        #endregion
        AddLog("系统初始化完成");
    }

    private void InitPicNo()
    {

        winCount = GetWinCount();
       
    }
    public void IniChanel()
    {
        //var winCount = GetWinCount();
        var sxtcn = sxtpz_cn.FirstOrDefault(s => s.Split('_')[0] == _carPlate);

        uint m_dwPlatNum = 1;
        //默认1
        uint m_dwSubWinNo = 1;
        #region 6816M
        //支持6816M
        if (AppStaticData.siheyiconfig.Sf6816M.Value== 1)
        {
            //是动态解码的话，墙号与对道号对应。设置图片的话，子窗口号与通道号对应
            m_dwPlatNum = (uint)(picWinIndex + 1);

        }
        #endregion
        if (1 <= winCount)
        {
            if (sxtcn != null)
            {
                var sxt = sxtcn.Split('_');
                _sxt1Temp.yplxj = sxt[1];
                _sxt1Temp.SxtUser = sxt[2];
                _sxt1Temp.SxtPassWord = sxt[3];
                _sxt1Temp.SxtTDH = Convert.ToInt32(sxt[4]);
                #region ldw 2018-06-18 兼容5画面

                //uint m_dwWinNo = (uint)(picWinIndex * 2 + 1);//每个窗体的第一个视频窗口为车内视频。                //uint m_dwWinNo = (uint)(picWinIndex * 2 + 1);//每个窗体的第一个视频窗口为车内视频。
                var m_dwWinNo =
                    (uint)(picWinIndex * winCount +
                           1); //每个窗体的第一个视频窗口为车内视频。                //uint m_dwWinNo = (uint)(picWinIndex * 2 + 1);//每个窗体的第一个视频窗口为车内视频。

                #endregion
                #region 6816M
                //支持6816M
                if (AppStaticData.siheyiconfig.Sf6816M.Value == 1)
                {
                    m_dwWinNo = 1;

                }
                #endregion
                var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) + (m_dwWinNo & 0xff);
                if (!HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, sxt[1], sxt[2],
                    sxt[3], int.Parse(sxt[4])))
                    WeakReferenceMessenger.Default.Send($"车内摄像头{sxtcn}设置失败","XiaoXi");
            }
        }

        if (2 <= winCount)
        {
            var sxtcw = sxtpz_cw.FirstOrDefault(s => s.Split('_')[0] == _carPlate);
            if (sxtcw != null)
            {
                var sxt = sxtcw.Split('_');

                _sxt2Temp.yplxj = sxt[1];
                _sxt2Temp.SxtUser = sxt[2];
                _sxt2Temp.SxtPassWord = sxt[3];
                _sxt2Temp.SxtTDH = Convert.ToInt32(sxt[4]);
                #region ldw 2018-06-18 兼容5画面

                //uint m_dwWinNo = (uint)(picWinIndex * 2 + 2);//每个窗体的第二个视频窗口为可切换窗口。

                var m_dwWinNo = (uint)(picWinIndex * winCount + 2); //每个窗体的第二个视频窗口为可切换窗口。

                #endregion
                #region 6816M
                //支持6816M
                if (AppStaticData.siheyiconfig.Sf6816M.Value== 1)
                {
                    m_dwWinNo = 2;

                }
                #endregion
                var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) + (m_dwWinNo & 0xff);
                if (!HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, sxt[1], sxt[2],
                    sxt[3], int.Parse(sxt[4])))
                    WeakReferenceMessenger.Default.Send($"车外摄像头{sxtcw}设置失败","XiaoXi");

            }
        }

        if (3 <= winCount)
        {
            var sxt3 = sxtpz_3.FirstOrDefault(s => s.Split('_')[0] == _carPlate);
            if (sxt3 != null)
            {
                var sxt = sxt3.Split('_');
                _sxt3Temp.yplxj = sxt[1];
                _sxt3Temp.SxtUser = sxt[2];
                _sxt3Temp.SxtPassWord = sxt[3];
                _sxt3Temp.SxtTDH = Convert.ToInt32(sxt[4]);
                #region ldw 2018-06-18 兼容5画面

                var m_dwWinNo = (uint)(picWinIndex * winCount + 3); //每个窗体的第三个视频窗口。
                //LogHelper.WriteLog($"窗口号:{m_dwWinNo}:{sxt3}");
                #endregion
                #region 6816M
                //支持6816M
                if (AppStaticData.siheyiconfig.Sf6816M.Value == 1)
                {
                    m_dwWinNo = 3;

                }
                #endregion
                var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) + (m_dwWinNo & 0xff);
                if (!HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, sxt[1], sxt[2],
                    sxt[3], int.Parse(sxt[4])))
                    WeakReferenceMessenger.Default.Send($"第三摄像头{sxt3}设置失败","XiaoXi");
            }
        }
        if (4 <= winCount)
        {
            var sxt4 = sxtpz_4.FirstOrDefault(s => s.Split('_')[0] == _carPlate);
            if (sxt4 != null)
            {
                var sxt = sxt4.Split('_');
                _sxt4Temp.yplxj = sxt[1];
                _sxt4Temp.SxtUser = sxt[2];
                _sxt4Temp.SxtPassWord = sxt[3];
                _sxt4Temp.SxtTDH = Convert.ToInt32(sxt[4]);
                #region ldw 2018-06-25 兼容6画面

                var m_dwWinNo = (uint)(picWinIndex * winCount + 4); //每个窗体的第四个视频窗口。
                Log.Information($"窗口号:{m_dwWinNo}:{sxt4}");
                #endregion
                #region 6816M
                //支持6816M
                if (AppStaticData.siheyiconfig.Sf6816M.Value == 1)
                {
                    m_dwWinNo = 4;

                }
                #endregion
                var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) + (m_dwWinNo & 0xff);
                if (!HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, sxt[1], sxt[2],
                    sxt[3], int.Parse(sxt[4])))
                    WeakReferenceMessenger.Default.Send($"第四摄像头{sxt4}设置失败","XiaoXi");
            }
        }
    }
    public void InitExamItems(double panelHeight)
    {
        //string examItemsText = "上车准备,起步,直线行驶,加减挡,变更车道,靠边,通过路口,左转,右转,人行横道,学校区域,公共汽车站,会车,超车,掉头,夜间行驶,,";
        //string examItemsText = "上车准备,起步,直线行驶,,,,,,,,,,,,,,,,,";
        ExamItems.Clear();
        string examItemsText = AppStaticData.siheyiconfig.Ksxms.Value;
        string[] items = examItemsText.Split(',');
        if (items.Length < 18)
        {
            WeakReferenceMessenger.Default.Send("Dialog-Exit考试项目小于18项，请检查配置项！", "XiaoXi");
            //Thread.Sleep(5000);


        }
        int count = items.Where(s => !string.IsNullOrEmpty(s)).Count();
        int rowCount = count > 9 ? 2 : 1;
        int perRow = (int)Math.Ceiling((double)count / rowCount);

        double margin =2+1; //不知为何数量少时，小于9个也会变成两列，后面再优化吧,先在计算时减去30
        
        //double margin = 2+30/ perRow; //不知为何数量少时，小于9个也会变成两列，后面再优化吧,先在计算时减去30
        //计算高时不知道为何会不准
        double buttonWidth = (panelHeight - perRow * margin * 2) / perRow;
        for (int i = 0; i < items.Length; i++)
        {
            var item = items[i];
            if (!string.IsNullOrEmpty(item))
            {
                // 初始化默认颜色
                ExamItems.Add(new ExamItemViewModel(
                    name: item,
                    index: i,
                    height: buttonWidth,
                    margin: margin
                    

                //backgroundColor: Brushes.Green,
                //foregroundColor: Brushes.White
                ));
            }
        }
    }

   
    public void InitKsxhs(double panelWidth)
    {

        //string xhs = "左转,右转,雾灯,,,,,,,,,,,,,,,,,,";
        Ksxhs.Clear();
        string xhs = AppStaticData.siheyiconfig.Xsxhs.Value;
        string[] items = xhs.Split(',');
        if (items.Length < 21)
        {
            WeakReferenceMessenger.Default.Send($"Dialog-Exit信号数量小于21，请检查", "XiaoXi");

        }
        int count = items.Where(s=>!string.IsNullOrEmpty(s)).Count();
        int rowCount = count > 10 ? 2 : 1;
        int perRow = (int)Math.Ceiling((double)count / rowCount);
        
        double margin =2+1; // Button左右Margin
        double yuliu = 10;
        double buttonWidth = (panelWidth - perRow * margin * 2) / perRow;
        
        for (int i = 0; i < items.Length; i++)
        {
            var item = items[i];
            if (!string.IsNullOrEmpty(item))
            {
                // 初始化默认颜色
                Ksxhs.Add(new KsxhItemViewModel(
                    name: item,
                    index: i,
                    width: buttonWidth > 0 ? buttonWidth : 80,
                    margin: margin
                //backgroundColor: Brushes.Green,
                //foregroundColor: Brushes.White
                ));
            }
        }
    }
    //主要更新考试项目状态、考车信号
    public void UpdateOthers(BaseDataModel bmd)
    {
        NewBmd = bmd;
        //Task.Factory.StartNew(() =>
        //{
        UpdateExamItemsStatus();
        UpdateKsxhsStatus();
        CastNvr(NewBmd);

        //});
    }
    private sxtIP oldSxtIp = new sxtIP();
    /// <summary>
    ///     科目二切换摄像头
    /// </summary>
    public void CastNvr(BaseDataModel bmd)
    {
        #region 切换摄像头

        //if (bmd.GetValueStr("kczt") != "0")
        {
            try
            {
                //使用IPS切换摄像头
                if (AppStaticData.siheyiconfig.SfIps.Value == 1)
                {
                    CastNvr(bmd.GetValueStr("gpsdxjl"), bmd.GetValueStr("gpsbxjl"));
                }
                //使用区域摄像头切换
                else

                {
                    //if (this.labKSCX.Text.ToUpper().Contains("C6") || this.labKSCX.Text.ToUpper().Contains("A2"))
                    //{
                    //    CastNVRBySensorSxt(bmd.GetValueStr("gpsdxjl"), bmd.GetValueStr("gpsbxjl"), bmd.GetValueStr("gpsdxjl2"), bmd.GetValueStr("gpsbxjl2"));
                    //}

                    if (AppStaticData.siheyiconfig.Wbqh.Value == 1 && (this.CarType.ToUpper().Contains("C6") || this.CarType.ToUpper().Contains("A2")))
                    {
                        CastNVRBySensorSxt(bmd.GetValueStr("gpsdxjl"), bmd.GetValueStr("gpsbxjl"), bmd.GetValueStr("gpsdxjl2"), bmd.GetValueStr("gpsbxjl2"));
                    }
                    else
                        CastNVRBySensorSxt(bmd.GetValueStr("gpsdxjl"), bmd.GetValueStr("gpsbxjl"));
                }
            }
            catch (Exception e)
            {
                //LogHelper.ErrorLog("切换摄像头出错", e);
               Log.Error($"切换摄像头出错{e.Message}{Environment.NewLine}{e.StackTrace}");
            }


            //var newgps = new DoublePoint(double.Parse(bmd.GetValueStr("gpsdxjl")),
            //    double.Parse(bmd.GetValueStr("gpsbxjl")));
            //                var ph = new PintHelp();
            //                foreach (var s in sxtList)
            //                    if (sxtTDH != s.SxtTDH)
            //                        if (ph.getDistance(newgps, s.SxtZB) < double.Parse(s.SxtJL))
            //                        {
            //                            uint m_dwPlatNum = 1;
            //                            //默认1
            //                            uint m_dwSubWinNo = 1;

            //#region ldw 2016-06-18 兼容5画面

            //                            var wincount = GetWinCount();
            //                            var m_dwWinNo = (uint) (picWinIndex * wincount + 2); //每个窗体的第二个视频窗口为可切换窗口。
            //                            //uint m_dwWinNo = (uint)(picWinIndex * 2 + 2);//每个窗体的第二个视频窗口为可切换窗口。

            //#endregion

            //                            var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) +
            //                                               (m_dwWinNo & 0xff);
            //                            #region 铁达

            //                            if (s.SxtTDH==1111)
            //                            {
            //                                HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt1Temp.yplxj, _sxt1Temp.SxtUser,
            //                                    _sxt1Temp.SxtPassWord, _sxt1Temp.SxtTDH);
            //                            }
            //                            else if (s.SxtTDH == 2222)
            //                            {
            //                                HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt2Temp.yplxj, _sxt2Temp.SxtUser,
            //                                    _sxt2Temp.SxtPassWord, _sxt2Temp.SxtTDH);
            //                            }
            //                            else if (s.SxtTDH == 3333)
            //                            {
            //                                HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt3Temp.yplxj, _sxt3Temp.SxtUser,
            //                                    _sxt3Temp.SxtPassWord, _sxt3Temp.SxtTDH);
            //                            }
            //                            else if (s.SxtTDH == 4444)
            //                            {
            //                                HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt4Temp.yplxj, _sxt4Temp.SxtUser,
            //                                    _sxt4Temp.SxtPassWord, _sxt4Temp.SxtTDH);
            //                            }

            //                            #endregion

            //                            else
            //                            { 
            //                                HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, s.yplxj, s.SxtUser,
            //                                s.SxtPassWord, s.SxtTDH);
            //                            }
            //                            break;
            //                        }
        }

        #endregion
    }
    /// <summary>
    /// 非挂车型切换
    /// </summary>
    /// <param name="gpsdxjl">车头东向距离</param>
    /// <param name="gpsbxjl">车头北向距离</param>
    private void CastNVRBySensorSxt(string gpsdxjl, string gpsbxjl)
    {
        double mapZoom = 25;
        var p = new DoublePoint(Convert.ToSingle(gpsdxjl), Convert.ToSingle(gpsbxjl));
        //如果在小区域内则切换小区域摄像头
        var SmallSensors = AppStaticData.SensorsSxtList.Where(s => !s.Name.ToLower().StartsWith("qj-"))
            .ToList();
        var incheck = SmallSensors.FirstOrDefault(s => PointInpolygonCheck.IsInPolygon(p, s.Data));
        //如果不在小区域 内则切换大区域摄像头
        if (incheck == null)
        {
            //不在小区域具体的项目中，则缩放地图
            mapZoom = 100;
            var BigSensors = AppStaticData.SensorsSxtList.Where(s => s.Name.ToLower().StartsWith("qj-"));
            incheck = BigSensors.FirstOrDefault(s => PointInpolygonCheck.IsInPolygon(p, s.Data));
        }
        //如果不在大区域 ，也不在小区域，则切换默认的场地摄像头
        if (AppStaticData.siheyiconfig.Mrqjqh.Value) { 

            if (incheck == null && _sxt2Temp != null)
            {
                incheck = new Sensor();
                incheck.sxtIP = _sxt2Temp;
            }
        }
        if (incheck != null)
        {
            var s = incheck.sxtIP;
            //如果切换摄像头与老摄像头不同时才切换
            //if (oldSxtIp.yplxj != s.yplxj || oldSxtIp.SxtTDH != s.SxtTDH || noCastTimes>10)//可能引起摄像头卡顿
            if (oldSxtIp.yplxj != s.yplxj || oldSxtIp.SxtTDH != s.SxtTDH)
            {

                var result = false;
                uint m_dwPlatNum = 1;
                //默认1
                uint m_dwSubWinNo = 1;


                #region ldw 2016-06-18 兼容5画面

                //var wincount = GetWinCount();
                var m_dwWinNo = (uint)(picWinIndex * winCount + 2); //每个窗体的第二个视频窗口为可切换窗口。
                                                                    //uint m_dwWinNo = (uint)(picWinIndex * 2 + 2);//每个窗体的第二个视频窗口为可切换窗口。

                #endregion
                #region 6816M
                //支持6816M
                if (AppStaticData.siheyiconfig.Sf6816M.Value== 1)
                {

                    m_dwPlatNum = (uint)(picWinIndex + 1);
                    m_dwWinNo = 2;
                    Log.Information( $"{DateTime.Now}:{s.yplxj}:{s.SxtTDH}：PlatNum{m_dwPlatNum}：m_dwSubWinNo{m_dwSubWinNo}：m_dwWinNo{m_dwWinNo}：picwin{picWin}");
                    //FileTools.WriteLog("carnvr.txt", $"{DateTime.Now}:{s.yplxj}:{s.SxtTDH}：PlatNum{m_dwPlatNum}：m_dwSubWinNo{m_dwSubWinNo}：m_dwWinNo{m_dwWinNo}：picwin{picWin}");
                }
                #endregion

                var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) +
                                   (m_dwWinNo & 0xff);

                #region 铁达

                if (s.SxtTDH == 1111)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt1Temp.yplxj,
                        _sxt1Temp.SxtUser,
                        _sxt1Temp.SxtPassWord, _sxt1Temp.SxtTDH);
                }
                else if (s.SxtTDH == 2222)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt2Temp.yplxj,
                        _sxt2Temp.SxtUser,
                        _sxt2Temp.SxtPassWord, _sxt2Temp.SxtTDH);
                }
                else if (s.SxtTDH == 3333)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt3Temp.yplxj,
                        _sxt3Temp.SxtUser,
                        _sxt3Temp.SxtPassWord, _sxt3Temp.SxtTDH);
                }
                else if (s.SxtTDH == 4444)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt4Temp.yplxj,
                        _sxt4Temp.SxtUser,
                        _sxt4Temp.SxtPassWord, _sxt4Temp.SxtTDH);
                }

                #endregion

                else
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, s.yplxj, s.SxtUser,
                        s.SxtPassWord, s.SxtTDH);
                }

                if (result)
                {
                    oldSxtIp = s;
                    //noCastTimes = 0;

                }
            }
            else
            {
                //noCastTimes++;
            }
        }
        //ChangeMapZoom(mapZoom);

    }

    private static int GetWinCount()
    {
        try
        {
            var winCount = Convert.ToInt32(AppStaticData.siheyiconfig.Spcksl.Value);
            if (winCount == 0) winCount = 2;
            return winCount;
        }
        catch (Exception e)
        {
            return 2;
        }

    }

    /// <summary>
    /// 带挂车型切换
    /// </summary>
    /// <param name="gpsdxjl">车头东向距离</param>
    /// <param name="gpsbxjl">车头北向距离</param>
    /// <param name="gpsdxjl2">车尾东向距离</param>
    /// <param name="gpsbxjl2">车尾北向向距离</param>
    private void CastNVRBySensorSxt(string gpsdxjl, string gpsbxjl, string gpsdxjl2, string gpsbxjl2)
    {
        double mapZoom = 25;
        //车头东北天
        var p = new DoublePoint(Convert.ToSingle(gpsdxjl), Convert.ToSingle(gpsbxjl));
        //车尾东北天，在挂车时使用
        var Wp = new DoublePoint(Convert.ToSingle(gpsdxjl2), Convert.ToSingle(gpsdxjl2));
        //如果在小区域内则切换小区域摄像头
        var SmallSensors = AppStaticData.SensorsSxtList.Where(s => !s.Name.ToLower().StartsWith("qj-"))
            .ToList();
        var incheck = SmallSensors.FirstOrDefault(s => PointInpolygonCheck.IsInPolygon(p, s.Data));
        //如果不在小区域 内则切换大区域摄像头
        if (incheck == null)
        {
            //不在小区域具体的项目中，则缩放地图
            mapZoom = 100;
            var BigSensors = AppStaticData.SensorsSxtList.Where(s => s.Name.ToLower().StartsWith("qj-"));
            incheck = BigSensors.FirstOrDefault(s => PointInpolygonCheck.IsInPolygon(Wp, s.Data));
        }
        //如果不在大区域 ，也不在小区域，则切换默认的场地摄像头
        if (AppStaticData.siheyiconfig.Mrqjqh.Value)
        {
            if (incheck == null && _sxt2Temp != null)
            {
                incheck = new Sensor();
                incheck.sxtIP = _sxt2Temp;
            }
        }
        //if (incheck == null && _sxt2Temp != null)
        //{
        //    incheck = new Sensor();
        //    incheck.sxtIP = _sxt2Temp;
        //}

        if (incheck != null)
        {
            var s = incheck.sxtIP;
            //如果切换摄像头与老摄像头不同时才切换
            //if (oldSxtIp.yplxj != s.yplxj || oldSxtIp.SxtTDH != s.SxtTDH || noCastTimes>10)//可能引起摄像头卡顿
            if (oldSxtIp.yplxj != s.yplxj || oldSxtIp.SxtTDH != s.SxtTDH)
            {

                var result = false;
                uint m_dwPlatNum = 1;
                //默认1
                uint m_dwSubWinNo = 1;


                #region ldw 2016-06-18 兼容5画面

                //var wincount = GetWinCount();
                var m_dwWinNo = (uint)(picWinIndex * winCount + 2); //每个窗体的第二个视频窗口为可切换窗口。
                                                                    //uint m_dwWinNo = (uint)(picWinIndex * 2 + 2);//每个窗体的第二个视频窗口为可切换窗口。

                #endregion
                #region 6816M
                //支持6816M
                if (AppStaticData.siheyiconfig.Sf6816M.Value == 1)
                {

                    m_dwPlatNum = (uint)(picWinIndex + 1);
                    m_dwWinNo = 2;
                    //FileTools.WriteLog("carnvr.txt", $"{DateTime.Now}:{s.yplxj}:{s.SxtTDH}：PlatNum{m_dwPlatNum}：m_dwSubWinNo{m_dwSubWinNo}：m_dwWinNo{m_dwWinNo}：picwin{picWin}");
                    Log.Information($"{DateTime.Now}:{s.yplxj}:{s.SxtTDH}：PlatNum{m_dwPlatNum}：m_dwSubWinNo{m_dwSubWinNo}：m_dwWinNo{m_dwWinNo}：picwin{picWin}");           
                }
                #endregion

                var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) +
                                   (m_dwWinNo & 0xff);

                #region 铁达

                if (s.SxtTDH == 1111)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt1Temp.yplxj,
                        _sxt1Temp.SxtUser,
                        _sxt1Temp.SxtPassWord, _sxt1Temp.SxtTDH);
                }
                else if (s.SxtTDH == 2222)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt2Temp.yplxj,
                        _sxt2Temp.SxtUser,
                        _sxt2Temp.SxtPassWord, _sxt2Temp.SxtTDH);
                }
                else if (s.SxtTDH == 3333)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt3Temp.yplxj,
                        _sxt3Temp.SxtUser,
                        _sxt3Temp.SxtPassWord, _sxt3Temp.SxtTDH);
                }
                else if (s.SxtTDH == 4444)
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt4Temp.yplxj,
                        _sxt4Temp.SxtUser,
                        _sxt4Temp.SxtPassWord, _sxt4Temp.SxtTDH);
                }

                #endregion

                else
                {
                    result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, s.yplxj, s.SxtUser,
                        s.SxtPassWord, s.SxtTDH);
                }

                if (result)
                {
                    oldSxtIp = s;
                    //noCastTimes = 0;

                }
            }
            else
            {
                //noCastTimes++;
            }
        }
        //ChangeMapZoom(mapZoom);

    }
    /// <summary>
    ///     获取IPS
    /// </summary>
   

    public void CastNvr(string gpsdxjl, string gpsbxjl)
    {
        #region 切换摄像头

        //if (bmd.GetValueStr("kczt") != "0")
        {
            var newgps = new DoublePoint(double.Parse(gpsdxjl),
                double.Parse(gpsbxjl));
            var ph = new PintHelp();
            foreach (var s in sxtList)
                //多个相同的摄像头不再切换
                if (oldSxtIp.yplxj != s.yplxj || oldSxtIp.SxtTDH != s.SxtTDH)

                    if (ph.getDistance(newgps, s.SxtZB) < double.Parse(s.SxtJL))
                    {

                        var result = false;
                        uint m_dwPlatNum = 1;
                        //默认1
                        uint m_dwSubWinNo = 1;

                        #region ldw 2016-06-18 兼容5画面

                        //var wincount = GetWinCount();
                        var m_dwWinNo = (uint)(picWinIndex * winCount + 2); //每个窗体的第二个视频窗口为可切换窗口。
                                                                            //uint m_dwWinNo = (uint)(picWinIndex * 2 + 2);//每个窗体的第二个视频窗口为可切换窗口。

                        #endregion
                        #region 6816M
                        //支持6816M
                        if (AppStaticData.siheyiconfig.Sf6816M.Value == 1)
                        {
                            m_dwPlatNum = (uint)(picWinIndex + 1);
                            m_dwWinNo = 2;
                        }
                        #endregion
                        var dwDecChanNum = ((m_dwPlatNum & 0xff) << 24) + ((m_dwSubWinNo & 0xff) << 16) +
                                           (m_dwWinNo & 0xff);
                        #region 铁达

                        if (s.SxtTDH == 1111)
                        {
                            result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt1Temp.yplxj, _sxt1Temp.SxtUser,
                                _sxt1Temp.SxtPassWord, _sxt1Temp.SxtTDH);
                        }
                        else if (s.SxtTDH == 2222)
                        {
                            result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt2Temp.yplxj, _sxt2Temp.SxtUser,
                                _sxt2Temp.SxtPassWord, _sxt2Temp.SxtTDH);
                        }
                        else if (s.SxtTDH == 3333)
                        {
                            result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt3Temp.yplxj, _sxt3Temp.SxtUser,
                                _sxt3Temp.SxtPassWord, _sxt3Temp.SxtTDH);
                        }
                        else if (s.SxtTDH == 4444)
                        {
                            result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, _sxt4Temp.yplxj, _sxt4Temp.SxtUser,
                                _sxt4Temp.SxtPassWord, _sxt4Temp.SxtTDH);
                        }

                        #endregion

                        else
                        {
                            result = HeMaQiHelper.StartDecode(AppStaticData.HmqUserId, dwDecChanNum, s.yplxj, s.SxtUser,
                                s.SxtPassWord, s.SxtTDH);
                        }
                        if (result)
                        {
                            oldSxtIp = s;
                        }
                        break;
                    }
        }

        #endregion
    }
    private List<string> qjdList = new List<string>() { "1", "2", "3", "4", "5" };
    private void UpdateDw(string dang)
    {
          //C2,C5车型，C6
        if ((CarType.Contains("C2") || CarType.Contains("C5") || CarType.Contains("C6"))
            && !string.IsNullOrEmpty(AppStaticData.siheyiconfig.C2Dw.Value))
        {
            Dangwei=AppStaticData.siheyiconfig.C2Dw.Value;
        }
        else
        {
            var dw = dang.ToString();
            if (dw == "9") dw = "倒档";
            else if (dw == "0") dw = "空档";
            //大车，挂上档，但没松离合的情况
            else if (dw == "8") dw = "半联动";
            //如果为小车c1,1至5档显示为前进档。
            else if (AppStaticData.siheyiconfig.Xsqjd.Value == 1 && AppStaticData.siheyiconfig.Kskm.Value == 2 && CarType.ToUpper().Contains("C1"))
            {
                if (qjdList.Contains(dw))
                {
                    dw = "前进档";
                }

            }
            Dangwei = dw;


        }
    }
    private void UpdateExamItemsStatus()
    {
        var xmzts = NewBmd.GetValueStr("xmzt");
        if (string.IsNullOrEmpty(xmzts))
        {
            return;
        }
        foreach (var item in ExamItems)
        {
            var state = xmzts[item.Index].ToString();
            item.State = state;

        }
    }
    //获取字符串中的档位、并删除档位、气压
    private string GetAndUpdateDw(string str)
    {
        var sb = new StringBuilder();
        for (int i = 0; i < str.Length; i++)
        {
            //档位
            if (i == 10 )
            {
                UpdateDw(str[i].ToString());
            }
            //气压
            else if (i == 17)
            {
                sb.Append("");
            }
            else
            {
                sb.Append(str[i]);
            }
        }
        return sb.ToString();
    }
    private void UpdateKsxhsStatus()
    {
        var xhzts = NewBmd.GetValueStr("sbxhzt");
        Chesu = NewBmd.GetValueStr("sd");
        Zhuansu= NewBmd.GetValueStr("fdjzs");
        if (string.IsNullOrEmpty(xhzts))
        {
            return;
        }
        //更新档位，并移除档位，气压不需显示的字段
        var xhztnew= GetAndUpdateDw(xhzts);
        foreach (var item in Ksxhs)
        {
            var state = xhztnew[item.Index].ToString();
            item.State = state;
        }
    }
    /// <summary>
    /// 当IsActive为True时为执行此方法
    /// </summary>
    protected override void OnActivated()
    {
        //Messenger.Register<CarControlViewModel, BaseDataModel, string>(
        //    this,
        //    "Xueyuan",
        //    (recipient, message) => ReceiveMessageXueyuan(message)

        // );
        Messenger.RegisterAll(this, "Xueyuan");
    }
    private void ReceiveMessageXueyuan(BaseDataModel message)
    {
        var czbm = message.GetValueStr("t_fcczbm");
        if (czbm != CarNumber)
        {
            return;
        }
        //考试结束的话清除考试信息
        if (message.GetValueStr("ksjs") == "1")
        {
            SetCjKf();//考试结束后，再更新一次考次成绩、扣分信息
            Task.Factory.StartNew(() =>
            {
                ResetXyinfo();
            });

            return;
        }
        CarStatus = true;
        //判断是否为同一学员,新学员时更新学员信息
        if (xydata == null || (xydata != null && xydata.GetValueStr("sfzmhm") != message.GetValueStr("sfzmhm")))
        {
           
            Xm = $"{message.GetValueStr("xm")}";
            Sfzmhm = $"{message.GetValueStr("sfzmhm")}";
            GetImage(message);
            Iskslx = message.GetValueStr("kskm") == "2";
            var line = message.GetValueStr("t_ldbh");
            Kslx = line;
            //新四合一暂不显示考试员
            //var ksysfz = message.GetValueStr("t_gawksy") == ""
            //                ? message.GetValueStr("t_fcksy")
            //                : message.GetValueStr("t_gawksy");
            //Ksy = AppManager.GetKsyXmBySfzmhm(ksysfz);
            Kskssj = message.GetValueStr("kskssj");
            //考试车型       
            CarType = message.GetValueStr("kscx");

        }
        xydata = message;
        //更新考试成绩，考试扣分信息
        SetCjKf();
        //更新变化信息       
        Kscs = GetBcyykscs().ToString();
        //更新考试时时长
        SetKssj();
    }



    private string GetSscjByDataBase(string kfxms)
    {


        var bmd = NewBmd;
        var sscj = bmd["sscj"];
        try
        {
            if (sscj == null)
            {
                return AppManager.GetKscjFromKfxms(kfxms).ToString();

            }
            else
            {

                var temp = Convert.ToInt32(sscj);
                if (temp > 100)
                {
                    temp = 100;
                }
                else if (temp < 0)
                {
                    temp = 0;
                }
                return temp.ToString();
            }
        }
        catch (Exception e)
        {

            return AppManager.GetKscjFromKfxms(kfxms).ToString();

        }



    }
    private string oldkfxms = "";
    private void SetCjKf(bool isjs = false)
    {
        var kfxms = NewBmd.GetValueStr("kfxm").Replace("#", ",");
        if (kfxms == oldkfxms)
        {
            return;
        }
        else
        {
            oldkfxms = kfxms;
        }
        //考试成绩
        Kscj = GetSscjByDataBase(kfxms);
        var kfMs = AppManager.GetKfxmMCs2(kfxms);
        Dispatcher.UIThread.Invoke(() =>
        {
            Kfxxs.Clear();

            foreach (var item in kfMs)
            {
                Kfxxs.Add(new InvoiceViewModel(item.GetValueStr("j_code"), item.GetValueStr("j_name"), int.Parse(item.GetValueStr("j_value")), false));

            }
        });


    }
    private void SetKssj()
    {
        //if (!Properties.Settings.Default.ShowKssj)
        //{
        //    return;
        //}

        if (xydata != null)
        {

            var kskssj = "";
            //if (this.labKSCX.Text.ToUpper().Contains("A") || this.labKSCX.Text.ToUpper().Contains("B"))
            //{
            //    kskssj = string.IsNullOrEmpty(xydata.GetValueStr("ykkskssj")) ? xydata.GetValueStr("kskssj") : xydata.GetValueStr("ykkskssj");
            //}
            //不再根据车类判断，是否夜考。直接根据开始夜考字段判断
            if (xydata.GetValueStr("t_ksyk") == "1")
            {
                kskssj = xydata.GetValueStr("ykkskssj");
            }
            else
            {
                kskssj = xydata.GetValueStr("kskssj");
            }


            DateTime dt1 = DateTime.Now;
            var dt2 = Convert.ToDateTime(kskssj);
            TimeSpan ts = dt1.Subtract(dt2);

            //lblkssj.Text = "开始时间:" + string.Format("{0:F}", dt2);

            //lblkssc.Text = "考试时长：" + (int)(Math.Abs(ts.TotalMinutes))+"分钟";
            //lblkssc.Text = "考试时长：" + ts.ToString(@"hh\:mm\:ss");
            //kskssjDatabase = dt2;
            Kssc = ts.ToString(@"hh\:mm\:ss");
        }
    }
    private void ResetXyinfo()
    {
        //等待5秒后，清除学员信息
        Thread.Sleep(5000);
        xydata = null;
        CarStatus = false;
        Xm = string.Empty;
        Sfzmhm = string.Empty;
        Gawzp = null;
        Mjzp = null;
        Ksy = string.Empty;
        Kscs = "0";
        Kscj = "100";
        Kslx = string.Empty;
        Kskssj = string.Empty;
        Kssc = string.Empty;
        Xmsc = string.Empty;
        try
        {

            //绑定ObservableCollection更新集合，要放到主线程处理，否则会报invalid call thread
            Dispatcher.UIThread.Invoke(() => Kfxxs.Clear());
            //Kfxxs.Clear();

        }
        catch (Exception ex)
        {

            //throw;
        }


    }
    protected int GetBcyykscs()
    {
        if (xydata == null) return 0;
        var kscs = xydata.GetValueStr("bcyykscs");
        if (string.IsNullOrEmpty(kscs)) return 0;
        var bcyykscs = Convert.ToInt32(kscs);
        bcyykscs = bcyykscs > 1 ? 2 : 1;
        return bcyykscs;
    }
    private void GetImage(BaseDataModel xydata)
    {
        //Gawzp = null;
        //Mjzp = null;
        Gawzp = FileTools.Base64ToBitmapWithSkia(xydata.GetValueStr("t_zxzp"));
        Mjzp = FileTools.Base64ToBitmapWithSkia(xydata.GetValueStr("t_mjzp"));
    }
    //扣分信息
    [ObservableProperty]
    public ObservableCollection<InvoiceViewModel> kfxxs = new ObservableCollection<InvoiceViewModel>()
    {

        //new ("41408", "当后车发出超车信号时，具备让车条件不减速靠右让行", -10,false),
        //new ("41504", "掉头时，妨碍正常行驶的其他车辆和行人通行", -10, false),
       

    };
   
    public int winCount { get; private set; }
    
    private readonly List<sxtIP> sxtList = new List<sxtIP>();
    private readonly string[] sxtpz_cn;
    private readonly string[] sxtpz_cw;
    private readonly string[] sxtpz_3;
    private readonly string[] sxtpz_4;
    public readonly string picWin ;

    public readonly int picWinIndex;

 

    private void AddLog(string message)
    {


        //OperationLogs.Insert(0, $"[{DateTime.Now:HH:mm:ss}] {message}");


    }

    [RelayCommand]
    private async Task Start()
    {
        //CarStatus = "启动中...";
        await Task.Delay(1000);
        //CarStatus = "运行中";
        AddLog("车辆已启动");
    }

    [RelayCommand]
    private void Stop()
    {
        //消息测试
        //var toa = App.provider.GetService<ISukiToastManager>();
        //toa.CreateSimpleInfoToast()
        //    .WithTitle("车辆已停止")
        //    .WithContent("车辆已停止")
        //    .Queue();
        WeakReferenceMessenger.Default.Send("停止", "XiaoXi");
        CarStatus = false;
        AddLog("车辆已停止");
    }

    [RelayCommand]
    private async Task Charge()
    {
        var originalLevel = BatteryLevel;
        while (BatteryLevel < 100)
        {
            await Task.Delay(100);
            BatteryLevel = Math.Min(100, BatteryLevel + 5);
        }
        //AddLog($"充电完成 ({originalLevel:F0}% -> {BatteryLevel:F0}%)");
    }

    [RelayCommand]
    private void Maintenance()
    {
        AddLog("开始车辆检修");

    }

    public void Receive(BaseDataModel message)
    {
        ReceiveMessageXueyuan(message);
    }
}
