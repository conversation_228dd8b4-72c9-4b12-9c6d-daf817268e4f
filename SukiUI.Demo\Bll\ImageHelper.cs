﻿using System;
using System.IO;
using System.Threading;
using System.Threading.Tasks;
using Avalonia.Controls;
using Avalonia.Media.Imaging;
using Avalonia;
using SkiaSharp;
using Avalonia.Skia;      // 提供 Avalonia 到 Skia 的扩展方法
using Avalonia.Media;
using Serilog;
using SukiUI.Demo.Infrastructure;

namespace SukiUI.Demo.Bll
{
    public class ImageHelper
    {
        public static int TargetHeight { get; }
        public static int TargetWidth { get; }
        private static readonly Object lockobj = new object();
        private static readonly SemaphoreSlim _imageSemaphore = new SemaphoreSlim(1, 1); // 限制并发图像处理
        static ImageHelper()
        {
            var target = AppStaticData.siheyiconfig.SctpSize.Value.Split(",");
            var outwidth = 480;
            var outheight = 240;
            //TargetWidth =int.TryParse( target[0],out TargetWidth);
            int.TryParse(target[0], out outwidth);
            int.TryParse(target[1], out outheight);
            TargetWidth = outwidth;
            TargetHeight = outheight;
        }
        // 这个方法绘制出来的图片，Label和TextBlock的字体显示模糊
        public static void SavePanelAsImage(Control panel)
        {
            lock (lockobj)
            {
                try
                {
                    #region 确保布局完成，否则可能导致TextBlock文字模糊，测试这个不起主要作用，并且会引起布局不稳，先不开吧
                    //panel.Measure(Size.Infinity);
                    //panel.Arrange(new Rect(panel.DesiredSize));
                    #endregion
                    panel.UseLayoutRounding = true;
                    var bitmap = new RenderTargetBitmap(new PixelSize((int)panel.Bounds.Width, (int)panel.Bounds.Height));
                    #region 强制抗锯齿设置，否则字体显示模糊，主要是这个生效
                    RenderOptions.SetBitmapInterpolationMode(panel, BitmapInterpolationMode.HighQuality);
                    RenderOptions.SetEdgeMode(panel, EdgeMode.Aliased);
                    #endregion
                    bitmap.Render(panel);

                    var fileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GjPic", Guid.NewGuid().ToString() + ".jpg");
                    using (var stream = new FileStream(fileName, FileMode.Create))
                    {
                        bitmap.Save(stream, 96);
                    }

                    //Log.Information(@$"保存图片成功{fileName}");
                }
                catch (Exception ex)
                {

                    //Log.Error("图片保存失败:{@ex}", ex);
                }

            }
        }

        public static async Task<bool> SavePanelAsImage3Async(Control panel, string fileName)
        {
            if (!MemorySafetyHelper.CheckNotNull(panel, "panel") ||
                !MemorySafetyHelper.CheckNotNull(fileName, "fileName"))
            {
                Log.Error("Invalid parameters: panel or fileName is null");
                return false;
            }

            // 使用信号量限制并发图像处理
            await _imageSemaphore.WaitAsync();
            try
            {
                   
                    #region 确保布局完成，否则可能导致TextBlock文字模糊，测试这个不起主要作用，并且会引起布局不稳，先不开吧
                    //panel.Measure(Size.Infinity);
                    //panel.Arrange(new Rect(panel.DesiredSize));
                    #endregion
                    #region 强制抗锯齿设置，否则字体显示模糊，主要是这个生效
                    RenderOptions.SetBitmapInterpolationMode(panel, BitmapInterpolationMode.HighQuality);
                    RenderOptions.SetEdgeMode(panel, EdgeMode.Aliased);
                    #endregion
                    panel.UseLayoutRounding = true;
                    // 这里的宽高，可以根据配置参数来设置，也可以直接使用Panel的宽高，暂时以配置参数来吧

                    // 指定目标尺寸
                    //var targetWidth = 480;
                    //var targetHeight = 240;

                    //// 创建 RenderTargetBitmap 时指定目标尺寸
                    //var bitmap = new RenderTargetBitmap(new PixelSize(targetWidth, targetHeight));

                    //// 应用缩放变换（核心缩放逻辑）
                    //panel.RenderTransform = new ScaleTransform(
                    //    targetWidth / panel.Bounds.Width,
                    //    targetHeight / panel.Bounds.Height);

                    //var fileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GjPic", Guid.NewGuid().ToString() + ".jpg");
                    //using (var stream = new FileStream(fileName, FileMode.Create))
                    //{
                    //    bitmap.Save(stream, 96);
                    //}


                    // 添加更严格的参数验证
                    if (panel.Bounds.Width <= 0 || panel.Bounds.Height <= 0)
                    {
                        Log.Error("Invalid panel dimensions: Width={Width}, Height={Height}", panel.Bounds.Width, panel.Bounds.Height);
                        return false;
                    }

                    if (TargetWidth <= 0 || TargetHeight <= 0)
                    {
                        Log.Error("Invalid target dimensions: TargetWidth={TargetWidth}, TargetHeight={TargetHeight}", TargetWidth, TargetHeight);
                        return false;
                    }

                    RenderTargetBitmap sourceBitmap = null;
                    MemoryStream memoryStream = null;
                    SKBitmap skBitmap = null;
                    SKBitmap scaledBitmap = null;
                    SKImage image = null;
                    SKData data = null;

                    try
                    {
                        // 创建渲染目标位图
                        sourceBitmap = new RenderTargetBitmap(new PixelSize((int)panel.Bounds.Width, (int)panel.Bounds.Height));
                        sourceBitmap.Render(panel);

                        // 保存到内存流
                        memoryStream = new MemoryStream();
                        sourceBitmap.Save(memoryStream);

                        Log.Information("Bitmap saved to memory stream. Length: {Length} bytes", memoryStream.Length);

                        // 验证内存流
                        if (memoryStream.Length == 0)
                        {
                            Log.Error("Empty memory stream after saving bitmap");
                            return false;
                        }

                        memoryStream.Position = 0;

                        // 解码位图 - 使用更安全的方式
                        memoryStream.Position = 0; // 确保流位置在开始
                        skBitmap = SKBitmap.Decode(memoryStream);
                        if (skBitmap == null)
                        {
                            Log.Error("Failed to decode bitmap - SKBitmap.Decode returned null. Memory stream length: {Length}", memoryStream.Length);

                            // 尝试备用方法：直接保存原始位图
                            Log.Information("Attempting fallback method: direct bitmap save");
                            try
                            {
                                using (var fileStream = new FileStream(fileName, FileMode.Create, FileAccess.Write))
                                {
                                    memoryStream.Position = 0;
                                    memoryStream.CopyTo(fileStream);
                                    fileStream.Flush();
                                }
                                Log.Information("Fallback method succeeded: saved original bitmap directly");
                                return true;
                            }
                            catch (Exception fallbackEx)
                            {
                                Log.Error("Fallback method also failed: {Error}", fallbackEx.Message);
                                return false;
                            }
                        }

                        if (skBitmap.Width == 0 || skBitmap.Height == 0)
                        {
                            Log.Error("Invalid bitmap dimensions: Width={Width}, Height={Height}", skBitmap.Width, skBitmap.Height);
                            return false;
                        }

                        // 缩放位图
                        var targetInfo = new SKImageInfo(TargetWidth, TargetHeight, SKColorType.Rgba8888, SKAlphaType.Premul);
                        scaledBitmap = skBitmap.Resize(targetInfo, SKFilterQuality.High);
                        if (scaledBitmap == null || scaledBitmap.Width == 0 || scaledBitmap.Height == 0)
                        {
                            Log.Error("Failed to resize bitmap or invalid scaled bitmap dimensions");
                            return false;
                        }

                        // 创建图像
                        image = SKImage.FromBitmap(scaledBitmap);
                        if (image == null)
                        {
                            Log.Error("Failed to create SKImage from scaled bitmap");
                            return false;
                        }

                        // 编码图像 - 使用更保守的质量设置
                        data = image.Encode(SKEncodedImageFormat.Jpeg, 85); // 降低质量以减少内存使用
                        if (data == null || data.Size == 0)
                        {
                            Log.Error("Failed to encode image or empty encoded data");
                            return false;
                        }

                        // 保存文件
                        using (var fileStream = new FileStream(fileName, FileMode.Create, FileAccess.Write))
                        {
                            data.SaveTo(fileStream);
                            fileStream.Flush();
                        }

                        Log.Information("Successfully saved image: {FileName}, Size: {Size} bytes", fileName, data.Size);
                        return true;
                    }
                    finally
                    {
                        // 确保所有资源都被正确释放
                        data?.Dispose();
                        image?.Dispose();
                        scaledBitmap?.Dispose();
                        skBitmap?.Dispose();
                        memoryStream?.Dispose();
                        sourceBitmap?.Dispose();
                    }
            }
            catch (Exception ex)
            {
                Log.Error("图片保存失败:{@ex}", ex);
                return false;
            }
            finally
            {
                _imageSemaphore.Release();
            }
        }



        // 使用此方法绘制
        public static void SavePanelAsImage1(Control panel)
        {
            lock (lockobj)
            {
                try
                {
                   
                    // 获取控件的像素大小
                    var pixelSize = new PixelSize((int)panel.Bounds.Width, (int)panel.Bounds.Height);
                    var dpi = 96; // 设置 DPI

                    // 创建 Skia Bitmap
                    using (var skBitmap = new SKBitmap(pixelSize.Width, pixelSize.Height))
                    {
                        using (var skCanvas = new SKCanvas(skBitmap))
                        {
                            // 清除画布
                            //skCanvas.Clear();
                            skCanvas.Clear(SKColors.Transparent);
                            // 不知道为什么使用下面语句又会重影
                            //skCanvas.Clear(SKColors.White);

                            // 渲染控件到 Skia 画布
                            var renderTarget = new RenderTargetBitmap(pixelSize);
                            renderTarget.Render(panel);

                            using (var memoryStream = new MemoryStream())
                            {
                                renderTarget.Save(memoryStream);
                                memoryStream.Seek(0, SeekOrigin.Begin);

                                using (var skImage = SKImage.FromEncodedData(memoryStream))
                                {
                                    skCanvas.DrawImage(skImage, 0, 0);
                                }
                            }

                            // 保存为 JPEG
                            var fileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GjPic", Guid.NewGuid().ToString() + ".jpg");
                            using (var image = SKImage.FromBitmap(skBitmap))
                            using (var data = image.Encode(SKEncodedImageFormat.Jpeg, 96))
                            using (var stream = new FileStream(fileName, FileMode.Create, FileAccess.Write))
                            {
                                data.SaveTo(stream);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    // 处理异常
                }
            }
        }



        public static void SavePanelAsImage2(Panel panel, SKEncodedImageFormat format = SKEncodedImageFormat.Jpeg, int quality = 96)
        {
            // 步骤1：确保 Panel 完成布局
            //panel.Measure(Size.Infinity);
            //panel.Arrange(new Rect(panel.DesiredSize));
            #region 强制抗锯齿设置，否则字体显示模糊，主要是这个生效
            RenderOptions.SetBitmapInterpolationMode(panel, BitmapInterpolationMode.HighQuality);
            RenderOptions.SetEdgeMode(panel, EdgeMode.Aliased);
            #endregion
            panel.UseLayoutRounding = true;
            // 步骤2：渲染到 RenderTargetBitmap
            var renderBitmap = new RenderTargetBitmap(
                new PixelSize((int)panel.Bounds.Width, (int)panel.Bounds.Height),
                new Vector(quality, quality)
            );
            renderBitmap.Render(panel);
            
            //renderBitmap.Render(panel);

            // 转换核心代码
            using var memoryStream = new MemoryStream();
            renderBitmap.Save(memoryStream);
            memoryStream.Position = 0; // 重置流位置
            using var skImage = SKImage.FromEncodedData(memoryStream); // 自动识别格式
            var fileName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GjPic", Guid.NewGuid().ToString() + ".jpg");
            // 方式一：直接保存原始流数据（推荐，保留透明通道）
            //File.WriteAllBytes("output.png", memoryStream.ToArray());

            // 方式二：通过SkiaSharp编码保存（可自定义格式）
            using var data = skImage.Encode(SKEncodedImageFormat.Jpeg, quality); // 质量参数仅对JPEG有效
            using var fileStream = File.OpenWrite(fileName);
            data.SaveTo(fileStream);
        }
        public static byte[] SKBitmapToByteArray(SKBitmap bitmap, SKEncodedImageFormat format = SKEncodedImageFormat.Png, int quality = 100)
        {
            //using (var image = SKImage.FromBitmap(bitmap))
            using (var data = bitmap.Encode(format, quality))
            {
                return data.ToArray();
            }
        }

        /// <summary>
        /// 备用的简化图像保存方法
        /// </summary>
        public static async Task<bool> SavePanelAsImageSimpleAsync(Control panel, string fileName)
        {
            if (!MemorySafetyHelper.CheckNotNull(panel, "panel") ||
                !MemorySafetyHelper.CheckNotNull(fileName, "fileName"))
            {
                Log.Error("Invalid parameters: panel or fileName is null");
                return false;
            }

            try
            {
                // 确保目录存在
                var directory = Path.GetDirectoryName(fileName);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                // 简单的渲染和保存
                var bitmap = new RenderTargetBitmap(new PixelSize((int)panel.Bounds.Width, (int)panel.Bounds.Height));
                bitmap.Render(panel);

                using (var stream = new FileStream(fileName, FileMode.Create))
                {
                    bitmap.Save(stream, 90); // 使用较低的质量以减少问题
                }

                Log.Information("Simple image save succeeded: {FileName}", fileName);
                return true;
            }
            catch (Exception ex)
            {
                Log.Error("Simple image save failed: {Error}", ex.Message);
                return false;
            }
        }
    }
}

