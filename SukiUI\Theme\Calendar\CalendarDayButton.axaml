<ResourceDictionary xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="SukiCalendarDayButtonStyle" TargetType="CalendarDayButton">
        <Setter Property="ClickMode" Value="Release" />
        <Setter Property="MinWidth" Value="32" />
        <Setter Property="MinHeight" Value="32" />
        <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="IsVisible" Value="True" />
        <Setter Property="Padding" Value="2" />
        <Setter Property="ClipToBounds" Value="False" />
        <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
        <Setter Property="HorizontalAlignment" Value="Center" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Center" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="PART_RootBorder"
                        Padding="{TemplateBinding Padding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Panel Name="PART_RootPanel">
                        <Border Name="PART_EffectBorder"
                                Background="{DynamicResource SukiPrimaryColor0}"
                                CornerRadius="{TemplateBinding CornerRadius}">
                            <Border.Transitions>
                                <Transitions>
                                    <BrushTransition Property="Background" Duration="{DynamicResource ShortAnimationDuration}" />
                                    <BrushTransition Property="BorderBrush" Duration="{DynamicResource ShortAnimationDuration}" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>

                        <ContentPresenter Name="PART_ContentPresenter"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}" />
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ Border#PART_EffectBorder">
            <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor25}" />
        </Style>

        <Style Selector="^:selected /template/ Border#PART_EffectBorder">
            <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor}" />
            <Setter Property="TextElement.FontWeight" Value="Bold" />
            <Setter Property="TextElement.Foreground" Value="White" />
        </Style>

        <Style Selector="^:selected /template/ Border#PART_RootBorder">
            <Setter Property="TextElement.FontWeight" Value="Bold" />
        </Style>

        <Style Selector="^:today /template/ Border#PART_EffectBorder">
            <Setter Property="Background" Value="{DynamicResource SukiAccentColor50}" />
            <Setter Property="BorderThickness" Value="0" />
            <Setter Property="TextElement.FontWeight" Value="Bold" />
            <Setter Property="TextElement.Foreground" Value="White" />
        </Style>
        
        <Style Selector="^:today /template/ TextBlock">
            <Setter Property="TextElement.FontWeight" Value="Bold" />
            <Setter Property="TextElement.Foreground" Value="White" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="White" />
        </Style>
        <Style Selector="^:today /template/ ContentPresenter">
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="White" />
        </Style>
        
        <Style Selector="^:selected /template/ TextBlock">
            <Setter Property="TextElement.FontWeight" Value="Bold" />
            <Setter Property="TextElement.Foreground" Value="White" />
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="White" />
        </Style>
        <Style Selector="^:selected /template/ ContentPresenter">
            <Setter Property="FontWeight" Value="Bold" />
            <Setter Property="Foreground" Value="White" />
        </Style>
        
        
        <Style Selector="^:inactive /template/ Border#PART_RootBorder">
            <Setter Property="Opacity" Value="0.24" />
        </Style>

        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="0.32" />
        </Style>

        <Style Selector="^:blackout">
            <Setter Property="IsVisible" Value="False" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type CalendarDayButton}"
                  BasedOn="{StaticResource SukiCalendarDayButtonStyle}"
                  TargetType="CalendarDayButton" />
</ResourceDictionary>
