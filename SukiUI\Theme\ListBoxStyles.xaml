﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:theme="clr-namespace:SukiUI.Theme">
    <Design.PreviewWith>
        <Border Padding="20" Background="{DynamicResource SukiBackground}">
            <StackPanel Orientation="Horizontal">
                <ListBox Width="150">
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                </ListBox>
                <ListBox Width="150" Classes="WithoutCheck">
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                </ListBox>
                <ListBox Width="150" Classes="Stack">
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                    <ListBoxItem>Test</ListBoxItem>
                </ListBox>
            </StackPanel>
        </Border>
    </Design.PreviewWith>
    <Style Selector="ListBox">
        <Setter Property="TextBlock.Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseHighBrush}" />
        <Setter Property="BorderThickness" Value="{DynamicResource ListBoxBorderThemeThickness}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="FontSize" Value="{DynamicResource ControlContentThemeFontSize}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        ClipToBounds="{TemplateBinding ClipToBounds}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Border.Resources>
                        <theme:BiggestItemListBoxConverter x:Key="BiggestListItem" />
                    </Border.Resources>
                    <Panel>
                        <ListBoxItem Margin="0,0,32,0"
                                     Content="{TemplateBinding ItemsSource,
                                                               Converter={StaticResource BiggestListItem}}"
                                     Opacity="0" />
                        <ScrollViewer Name="PART_ScrollViewer"
                                      HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                      VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}">
                            <ItemsPresenter Name="PART_ItemsPresenter"
                                            Margin="{TemplateBinding Padding}"
                                            ItemsPanel="{TemplateBinding ItemsPanel}" />
                        </ScrollViewer>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>

    <Style Selector="ListBox.WithoutCheck ListBoxItem /template/ PathIcon#CheckSelected">
        <Setter Property="IsVisible" Value="False" />
    </Style>

    <Style Selector="ListBox.Stack">
        <Setter Property="TextBlock.Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseHighBrush}" />
        <Setter Property="BorderThickness" Value="{DynamicResource ListBoxBorderThemeThickness}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="FontSize" Value="{DynamicResource ControlContentThemeFontSize}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="border"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        ClipToBounds="{TemplateBinding ClipToBounds}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Border.Resources>
                        <theme:BiggestItemListBoxConverter x:Key="BiggestListItem" />
                    </Border.Resources>
                    <Panel>
                        <ListBoxItem Margin="0,0,32,0"
                                     Content="{TemplateBinding ItemsSource,
                                                               Converter={StaticResource BiggestListItem}}"
                                     Opacity="0" />
                        <ScrollViewer Name="PART_ScrollViewer"
                                      Classes="Stack"
                                      HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                      VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}">
                            <ItemsPresenter Name="PART_ItemsPresenter"
                                            Margin="{TemplateBinding Padding}"
                                            ItemsPanel="{TemplateBinding ItemsPanel}" />
                        </ScrollViewer>
                    </Panel>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>
</Styles>