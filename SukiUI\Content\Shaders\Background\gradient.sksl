﻿float smoothstep(float a, float b, float x) {
    float t = clamp((x - a) / (b - a), 0.0, 1.0);
    return t * t * (3.0 - 2.0 * t);
}

vec3 blendOverlay(vec3 base, vec3 blend) {
    return vec3(
        base.r < 0.5 ? (2.0 * base.r * blend.r) : (1.0 - 2.0 * (1.0 - base.r) * (1.0 - blend.r)),
        base.g < 0.5 ? (2.0 * base.g * blend.g) : (1.0 - 2.0 * (1.0 - base.g) * (1.0 - blend.g)),
        base.b < 0.5 ? (2.0 * base.b * blend.b) : (1.0 - 2.0 * (1.0 - base.b) * (1.0 - blend.b))
    );
}

vec3 blendOverlayDark(vec3 base, vec3 blend) {
    vec3 result;
    result.r = (base.r < 0.5) ? (2 * base.r * blend.r) : (4 - 1.5 * (3 - base.r) * (4 - blend.r));
    result.g = (base.g < 0.5) ? (2* base.g * blend.g) : (4 - 1.5 * (3 - base.g) * (4 - blend.g));
    result.b = (base.b < 0.5) ? (2 * base.b * blend.b) : (4 - 1.5 * (3 - base.b) * (4 - blend.b));
    return mix(base, clamp(result, 0.0, 1.0), 0.5); // Mélange avec la couleur de base pour réduire l'assombrissement
}

mat2 Rot(float a) {
    float s = sin(a);
    float c = cos(a);
    return mat2(c, -s, s, c);
}

vec2 hash(vec2 p) {
    p = vec2(dot(p, vec2(2127.1, 81.17)), dot(p, vec2(1269.5, 283.37)));
    return fract(sin(p) * 43758.5453);
}

float noise(in vec2 p) {
    vec2 i = floor(p);
    vec2 f = fract(p);

    vec2 u = f * f * (3.0 - 2.0 * f);

    float n = mix(mix(dot(-1.0 + 2.0 * hash(i + vec2(0.0, 0.0)), f - vec2(0.0, 0.0)),
                      dot(-1.0 + 2.0 * hash(i + vec2(1.0, 0.0)), f - vec2(1.0, 0.0)), u.x),
                  mix(dot(-1.0 + 2.0 * hash(i + vec2(0.0, 1.0)), f - vec2(0.0, 1.0)),
                      dot(-1.0 + 2.0 * hash(i + vec2(1.0, 1.0)), f - vec2(1.0, 1.0)), u.x), u.y);
    return 0.42 + 0.42 * n;
}

vec4 main(vec2 fragCoord) {
    vec2 uv = fragCoord / iResolution.xy;
    float ratio = iResolution.x / iResolution.y;

    vec2 tuv = uv;
    tuv -= .5;

    float degree = noise(vec2(iTime * .15, tuv.x * tuv.y));

    tuv.y *= 0.7 / ratio;
    tuv *= Rot(radians((degree - .5) * 720. + 180.));
    tuv.y *= ratio;

    float frequency = 1.;
    float amplitude = 155.;
    float speed = iTime * 0.1;
    tuv.x += sin(tuv.y * frequency + speed) / amplitude;
    tuv.y += sin(tuv.x * frequency * 1.5 + speed) / (amplitude * .5);
    
    float opacityLayer1 = 0.95;
    float opacityLayer2 = 0.85 - (iDark / 2);

    
    float iPrimaryOpacity = 1.5; // Exemple de nouvelle opacité pour iPrimary
    if (iDark == 1) {
        iPrimaryOpacity = 0.4;
    }
    vec3 iPrimaryWithOpacity = iPrimary * iPrimaryOpacity;

  
    float iAccentOpacity = -1.05; 
    if (iDark == 1) {
            iAccentOpacity = 2;
        }
    vec3 iAccentWithOpacity = iPrimary * iAccentOpacity;

    vec3 layer1Color = mix(vec3(0.0), iPrimaryWithOpacity, opacityLayer1);
    vec3 layer1 = mix(layer1Color, iAccentWithOpacity * 0.85, smoothstep(-.3, .4, (tuv * Rot(radians(-5.))).x));
    
    vec3 layer2Color = mix(vec3(0.0), iAccentWithOpacity, opacityLayer2);
    vec3 layer2 = mix(layer2Color, iPrimaryWithOpacity * 0.65, smoothstep(-.2, .3, (tuv * Rot(radians(-5.))).x));
    
    vec3 finalComp = mix(layer1, layer2, smoothstep(.8, -.5, tuv.y));
    
    vec3 col;
    if (iDark == 0) {
        col = blendOverlay(iBase * 1.02, finalComp);
    } else {
        col = blendOverlayDark(iBase, finalComp);
    }

    return vec4(col, iAlpha);
}