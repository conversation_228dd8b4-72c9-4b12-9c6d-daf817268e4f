<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.IconsView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:showMeTheXaml="clr-namespace:ShowMeTheXaml;assembly=ShowMeTheXaml.Avalonia"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:IconsViewModel"
             mc:Ignorable="d">
    <Grid RowDefinitions="Auto, *">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Icons">
                <StackPanel Classes="HeaderContent">
                    <TextBlock TextWrapping="Wrap">
                        SukiUI contains definitions for some basic icons that are used for a variety of controls. You may wish to use these icons in your app and controls to maintain consistency with SukiUI.
                    </TextBlock>
                    <TextBlock>
                        The icons can be used in the following way:
                    </TextBlock>
                    <StackPanel Orientation="Horizontal" Spacing="15">
                        <showMeTheXaml:XamlDisplay UniqueId="Icon1">
                            <PathIcon Data="{x:Static suki:Icons.Plus}" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="Icon2">
                            <PathIcon Classes="Primary" Data="{x:Static suki:Icons.Plus}" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="Icon3">
                            <PathIcon Classes="Accent" Data="{x:Static suki:Icons.Plus}" />
                        </showMeTheXaml:XamlDisplay>
                    </StackPanel>
                    <TextBlock>
                        Here is the full list of available icons, click on any of them to copy the XAML to use it to the clipboard:
                    </TextBlock>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <ItemsControl suki:ItemsControlExtensions.AnimatedScroll="True" ItemsSource="{Binding Icons}">
                <ItemsControl.ItemTemplate>
                    <DataTemplate x:DataType="controlsLibrary:IconItemViewModel">
                        <suki:GlassCard Margin="15,15,15,15"
                                        IsInteractive="True"
                                        Command="{Binding ClickCommand}">
                            <suki:GroupBox Header="{Binding Name}">
                                <PathIcon Width="25"
                                          Height="25"
                                          Margin="0,8,0,0"
                                          Data="{Binding Geometry}" />
                            </suki:GroupBox>
                        </suki:GlassCard>
                    </DataTemplate>
                </ItemsControl.ItemTemplate>
                <ItemsControl.ItemsPanel>
                    <ItemsPanelTemplate>
                        <WrapPanel Classes="PageContainer" />
                    </ItemsPanelTemplate>
                </ItemsControl.ItemsPanel>
            </ItemsControl>
        </ScrollViewer>
    </Grid>
</UserControl>