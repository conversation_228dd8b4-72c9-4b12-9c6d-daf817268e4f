
<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:icons="clr-namespace:SukiUI.Content"
                    xmlns:sys="using:System"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI">
    <Design.PreviewWith>
        <Border Height="500" Padding="20">
            <StackPanel VerticalAlignment="Center" Spacing="20">
                <TimePicker />

            </StackPanel>
        </Border>
    </Design.PreviewWith>

    <x:Double x:Key="TimePickerFlyoutPresenterItemHeight">40</x:Double>
    <x:Double x:Key="TimePickerSpacerThemeWidth">1</x:Double>
    <Thickness x:Key="TimePickerBorderThemeThickness">1</Thickness>
    <x:Double x:Key="TimePickerFlyoutPresenterHighlightHeight">40</x:Double>
    <x:Double x:Key="TimePickerFlyoutPresenterAcceptDismissHostGridHeight">41</x:Double>
    <x:Double x:Key="TimePickerThemeMinWidth">242</x:Double>
    <x:Double x:Key="TimePickerThemeMaxWidth">456</x:Double>
    <Thickness x:Key="TimePickerFlyoutPresenterItemPadding">0,3,0,6</Thickness>
    <Thickness x:Key="TimePickerHostPadding">0,3,0,6</Thickness>

    <ControlTheme x:Key="SimpleTimePickerFlyoutButton" TargetType="Button">
        <Setter Property="RenderTransform" Value="none" />

        <Setter Property="Template">
            <ControlTemplate>
                <ContentPresenter Name="PART_ContentPresenter"
                                  HorizontalContentAlignment="Stretch"
                                  VerticalContentAlignment="Stretch"
                                  Background="{TemplateBinding Background}"
                                  BorderBrush="{TemplateBinding BorderBrush}"
                                  BorderThickness="{TemplateBinding BorderThickness}"
                                  Content="{TemplateBinding Content}"
                                  CornerRadius="{TemplateBinding CornerRadius}"
                                  Foreground="{TemplateBinding Foreground}" />
            </ControlTemplate>
        </Setter>

        <Style Selector="^:pointerover /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="BorderBrush" Value="{DynamicResource ThemeControlHighBrush}" />
            <Setter Property="TextElement.Foreground" Value="{TemplateBinding Foreground}" />
        </Style>

        <Style Selector="^:pressed /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Background">
                <SolidColorBrush Opacity="0.6" Color="{DynamicResource ThemeControlMidHighColor}" />
            </Setter>
            <Setter Property="BorderBrush" Value="{DynamicResource ThemeControlLowBrush}" />
            <Setter Property="TextElement.Foreground" Value="{TemplateBinding Foreground}" />
        </Style>

        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Opacity" Value="{DynamicResource ThemeDisabledOpacity}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type TimePicker}" TargetType="TimePicker">
        <Setter Property="FontSize" Value="{DynamicResource FontSizeNormal}" />
        <Setter Property="BorderThickness" Value="0,0,0,0" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiMediumBorderBrush}" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="Background" Value="{DynamicResource SukiBackground}" />
        <Setter Property="Padding" Value="20,8" />
        <Setter Property="Height" Value="40" />
        <Setter Property="Template">
            <ControlTemplate>
                <DataValidationErrors>
                    <Grid Name="LayoutRoot">
                        <suki:GlassCard Height="{TemplateBinding Height}" CornerRadius="{DynamicResource SmallCornerRadius}" Classes="Discrete" >

                            <Button x:Name="PART_FlyoutButton"
                                    MinWidth="{DynamicResource TimePickerThemeMinWidth}"
                                    MaxWidth="{DynamicResource TimePickerThemeMaxWidth}"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Background="{TemplateBinding Background}"
                                    BorderBrush="{TemplateBinding BorderBrush}"
                                    BorderThickness="{TemplateBinding BorderThickness}"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    Foreground="{TemplateBinding Foreground}"
                                    IsEnabled="{TemplateBinding IsEnabled}"
                                    Theme="{StaticResource SimpleTimePickerFlyoutButton}">
                                <DockPanel Margin="{TemplateBinding Padding}">
                                    <PathIcon Width="16"
                                              Height="16"
                                              Margin="12,0,0,0"
                                              HorizontalAlignment="Right"
                                              Data="{x:Static icons:Icons.Calendar}"
                                              DockPanel.Dock="Right"
                                              Foreground="{DynamicResource SukiLowText}" />

                                    <Grid Name="PART_FlyoutButtonContentGrid">

                                        <Border x:Name="PART_FirstPickerHost"
                                                Grid.Column="0"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Stretch">
                                            <TextBlock x:Name="PART_HourTextBlock"
                                                       Text="{DynamicResource STRING_TIMEPICKER_HOUR_TEXT}"
                                                       Padding="{DynamicResource TimePickerHostPadding}"
                                                       HorizontalAlignment="Center"
                                                       FontFamily="{TemplateBinding FontFamily}"
                                                       FontSize="{TemplateBinding FontSize}"
                                                       FontWeight="{TemplateBinding FontWeight}" />
                                        </Border>

                                        <Rectangle Name="PART_FirstColumnDivider"
                                                   Grid.Column="1"
                                                   Width="{DynamicResource TimePickerSpacerThemeWidth}"
                                                   HorizontalAlignment="Center"
                                                   Fill="{DynamicResource SukiMediumBorderBrush}" />

                                        <Border x:Name="PART_SecondPickerHost"
                                                Grid.Column="2"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Stretch">
                                            <TextBlock x:Name="PART_MinuteTextBlock"
                                                       Text="{DynamicResource STRING_TIMEPICKER_MINUTE_TEXT}"
                                                       Padding="{DynamicResource TimePickerHostPadding}"
                                                       HorizontalAlignment="Center"
                                                       FontFamily="{TemplateBinding FontFamily}"
                                                       FontSize="{TemplateBinding FontSize}"
                                                       FontWeight="{TemplateBinding FontWeight}" />
                                        </Border>

                                        <Rectangle Name="PART_SecondColumnDivider"
                                                   Grid.Column="3"
                                                   Width="{DynamicResource TimePickerSpacerThemeWidth}"
                                                   HorizontalAlignment="Center"
                                                   Fill="{DynamicResource SukiMediumBorderBrush}" />

                                        <Border x:Name="PART_ThirdPickerHost"
                                                Grid.Column="4"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Stretch">
                                            <TextBlock x:Name="PART_SecondTextBlock"
                                                       Text="{DynamicResource STRING_TIMEPICKER_SECOND_TEXT}"
                                                       Padding="{DynamicResource TimePickerHostPadding}"
                                                       HorizontalAlignment="Center"
                                                       FontFamily="{TemplateBinding FontFamily}"
                                                       FontSize="{TemplateBinding FontSize}"
                                                       FontWeight="{TemplateBinding FontWeight}" />
                                        </Border>

                                        <Rectangle Name="PART_ThirdColumnDivider"
                                                   Fill="{DynamicResource SukiMediumBorderBrush}"
                                                   HorizontalAlignment="Center"
                                                   Width="{DynamicResource TimePickerSpacerThemeWidth}"
                                                   Grid.Column="5" />

                                        <Border x:Name="PART_FourthPickerHost"
                                                Grid.Column="6"
                                                HorizontalAlignment="Stretch"
                                                VerticalAlignment="Stretch">
                                            <TextBlock x:Name="PART_PeriodTextBlock"
                                                       Padding="{DynamicResource TimePickerHostPadding}"
                                                       HorizontalAlignment="Center"
                                                       FontFamily="{TemplateBinding FontFamily}"
                                                       FontSize="{TemplateBinding FontSize}"
                                                       FontWeight="{TemplateBinding FontWeight}" />
                                        </Border>
                                    </Grid>
                                </DockPanel>
                            </Button>
                        </suki:GlassCard>
                        <Popup Name="PART_Popup"
                               IsLightDismissEnabled="True"
                               Placement="Bottom"
                               PlacementTarget="{TemplateBinding}"
                               WindowManagerAddShadowHint="False">
                            <TimePickerPresenter Name="PART_PickerPresenter" />
                        </Popup>

                    </Grid>
                </DataValidationErrors>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:disabled /template/ Rectangle">
            <Setter Property="Opacity" Value="{DynamicResource ThemeDisabledOpacity}" />
        </Style>

        <Style Selector="^:hasnotime /template/ Button#PART_FlyoutButton TextBlock">
            <Setter Property="Foreground" Value="{DynamicResource ThemeForegroundLowBrush}" />
        </Style>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type TimePickerPresenter}" TargetType="TimePickerPresenter">
        <Setter Property="Width" Value="262" />
        <Setter Property="MinWidth" Value="262" />
        <Setter Property="MaxHeight" Value="398" />
        <Setter Property="FontWeight" Value="Normal" />
        <Setter Property="CornerRadius" Value="10" />
        <Setter Property="Background" Value="{DynamicResource SukiCardBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ThemeBorderMidBrush}" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="Background"
                        MaxHeight="398"
                        Margin="15"
                        Padding="{DynamicResource DateTimeFlyoutBorderPadding}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        BoxShadow="{DynamicResource SukiPopupShadow}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Border.Styles>
                        <Style Selector="RepeatButton">
                            <Setter Property="Background" Value="Transparent" />
                        </Style>
                        <Style Selector="RepeatButton:pointerover">
                            <Setter Property="Background" Value="Transparent" />
                        </Style>
                    </Border.Styles>
                    <Grid Name="ContentPanel" RowDefinitions="*,Auto">
                        <Grid Name="PART_PickerContainer">
                            <Grid.OpacityMask>
                                <LinearGradientBrush StartPoint="48%,0%" EndPoint="50%,100%">
                                    <GradientStop Offset="0" />
                                    <GradientStop Offset="0.5" Color="Black" />
                                    <GradientStop Offset="1" />
                                </LinearGradientBrush>
                            </Grid.OpacityMask>
                            <Grid.Styles>
                                <Style Selector="ListBoxItem:selected  TextBlock">
                                    <Setter Property="FontWeight" Value="Bold" />
                                </Style>
                                <Style Selector="DateTimePickerPanel &gt; ListBoxItem">
                                    <Setter Property="Background" Value="Transparent" />
                                    <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
                                    <Setter Property="Padding" Value="4,2" />
                                    <Setter Property="Template">
                                        <ControlTemplate>
                                            <Border Margin="0,0,10,0"
                                                    Padding="8,4"
                                                    CornerRadius="6">

                                                <ContentPresenter Name="PART_ContentPresenter"
                                                                  Margin="0,0,0,0"
                                                                  Padding="{TemplateBinding Padding}"
                                                                  VerticalAlignment="Center"
                                                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                                                  VerticalContentAlignment="Center"
                                                                  Background="Transparent"
                                                                  BorderBrush="Transparent"
                                                                  BorderThickness="{TemplateBinding BorderThickness}"
                                                                  Content="{TemplateBinding Content}"
                                                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                                                  CornerRadius="{TemplateBinding CornerRadius}" />

                                            </Border>
                                        </ControlTemplate>
                                    </Setter>
                                </Style>
                            </Grid.Styles>

                            <Panel Name="PART_HourHost" Grid.Column="0">
                                <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Hidden">
                                    <DateTimePickerPanel Name="PART_HourSelector"
                                                         ItemHeight="{DynamicResource TimePickerFlyoutPresenterItemHeight}"
                                                         PanelType="Hour"
                                                         ShouldLoop="True" />
                                </ScrollViewer>
                                <RepeatButton Name="PART_HourUpButton" Theme="{StaticResource SimpleDateTimePickerUpButton}" />
                                <RepeatButton Name="PART_HourDownButton" Theme="{StaticResource SimpleDateTimePickerDownButton}" />
                            </Panel>

                            <Panel Name="PART_MinuteHost" Grid.Column="2">
                                <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Hidden">
                                    <DateTimePickerPanel Name="PART_MinuteSelector"
                                                         ItemHeight="{DynamicResource TimePickerFlyoutPresenterItemHeight}"
                                                         PanelType="Minute"
                                                         ShouldLoop="True" />
                                </ScrollViewer>
                                <RepeatButton Name="PART_MinuteUpButton" Theme="{StaticResource SimpleDateTimePickerUpButton}" />
                                <RepeatButton Name="PART_MinuteDownButton" Theme="{StaticResource SimpleDateTimePickerDownButton}" />
                            </Panel>

                            <Panel Name="PART_SecondHost" Grid.Column="4">
                                <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Hidden">
                                    <DateTimePickerPanel Name="PART_SecondSelector"
                                                         ItemHeight="{DynamicResource TimePickerFlyoutPresenterItemHeight}"
                                                         PanelType="Second"
                                                         ShouldLoop="True" />
                                </ScrollViewer>
                                <RepeatButton Name="PART_SecondUpButton" Theme="{StaticResource SimpleDateTimePickerUpButton}" />
                                <RepeatButton Name="PART_SecondDownButton" Theme="{StaticResource SimpleDateTimePickerDownButton}" />
                            </Panel>

                            <Panel Name="PART_PeriodHost" Grid.Column="6">
                                <ScrollViewer HorizontalScrollBarVisibility="Disabled" VerticalScrollBarVisibility="Hidden">
                                    <DateTimePickerPanel Name="PART_PeriodSelector"
                                                         ItemHeight="{DynamicResource TimePickerFlyoutPresenterItemHeight}"
                                                         PanelType="TimePeriod"
                                                         ShouldLoop="False" />
                                </ScrollViewer>
                                <RepeatButton Name="PART_PeriodUpButton" Theme="{StaticResource SimpleDateTimePickerUpButton}" />
                                <RepeatButton Name="PART_PeriodDownButton" Theme="{StaticResource SimpleDateTimePickerDownButton}" />
                            </Panel>

                            <Border x:Name="HighlightRect"
                                    Grid.Column="0"
                                    Grid.ColumnSpan="7"
                                    Height="{DynamicResource TimePickerFlyoutPresenterHighlightHeight}"
                                    Margin="5,0"
                                    VerticalAlignment="Center"
                                    Background="{DynamicResource SukiPrimaryColor5}"
                                    CornerRadius="10"
                                    ZIndex="-1" />
                            <Rectangle Name="PART_FirstSpacer"
                                       Grid.Column="1"
                                       Width="{DynamicResource TimePickerSpacerThemeWidth}"
                                       HorizontalAlignment="Center">
                                <Rectangle.Fill>
                                    <LinearGradientBrush StartPoint="48%,0%" EndPoint="50%,100%">
                                        <GradientStop Offset="0" Color="Transparent" />
                                        <GradientStop Offset="0.5" Color="{DynamicResource SukiMediumBorderBrush}" />
                                        <GradientStop Offset="1" Color="Transparent" />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <Rectangle Name="PART_SecondSpacer"
                                       Grid.Column="3"
                                       Width="{DynamicResource TimePickerSpacerThemeWidth}"
                                       HorizontalAlignment="Center">
                                <Rectangle.Fill>
                                    <LinearGradientBrush StartPoint="48%,0%" EndPoint="50%,100%">
                                        <GradientStop Offset="0" Color="Transparent" />
                                        <GradientStop Offset="0.5" Color="{DynamicResource SukiMediumBorderBrush}" />
                                        <GradientStop Offset="1" Color="Transparent" />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <Rectangle Name="PART_ThirdSpacer"
                                       Grid.Column="4"
                                       Width="{DynamicResource TimePickerSpacerThemeWidth}"
                                       HorizontalAlignment="Center">
                                <Rectangle.Fill>
                                    <LinearGradientBrush StartPoint="48%,0%" EndPoint="50%,100%">
                                        <GradientStop Offset="0" Color="Transparent" />
                                        <GradientStop Offset="0.5" Color="{DynamicResource SukiMediumBorderBrush}" />
                                        <GradientStop Offset="1" Color="Transparent" />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                        </Grid>

                        <Grid Name="AcceptDismissGrid"
                              Grid.Row="1"
                              ColumnDefinitions="*,*">
                            <Rectangle Grid.ColumnSpan="2"
                                       Height="{DynamicResource TimePickerSpacerThemeWidth}"
                                       VerticalAlignment="Top"
                                       Fill="{DynamicResource ThemeControlMidHighBrush}"
                                       IsVisible="False" />
                            <Button Name="PART_AcceptButton"
                                    Grid.Column="1"
                                    Height="{DynamicResource TimePickerFlyoutPresenterAcceptDismissHostGridHeight}"
                                    Margin="0,0,0,2"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    Theme="{StaticResource SimpleDateTimePickerButton}">
                                <StackPanel Margin="20,0" Orientation="Horizontal">
                                    <PathIcon Width="12"
                                              Height="12"
                                              Data="{x:Static icons:Icons.Check}"
                                              Foreground="{DynamicResource SukiPrimaryColor}" />
                                    <TextBlock Margin="10,0,0,0"
                                               FontSize="15"
                                               FontWeight="{DynamicResource DefaultDemiBold}"
                                               Foreground="{DynamicResource SukiPrimaryColor}"
                                               Text="{DynamicResource STRING_TIMEPICKER_APPLY}" />
                                </StackPanel>
                            </Button>
                            <Button Name="PART_DismissButton"
                                    Grid.Column="0"
                                    Height="{DynamicResource TimePickerFlyoutPresenterAcceptDismissHostGridHeight}"
                                    HorizontalAlignment="Stretch"
                                    VerticalAlignment="Stretch"
                                    FontSize="16"
                                    Theme="{StaticResource SimpleDateTimePickerButton}">
                                <PathIcon Width="12"
                                          Height="12"
                                          Data="{x:Static icons:Icons.Cross}"
                                          Foreground="{DynamicResource SukiPrimaryColor}" />
                            </Button>
                        </Grid>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^ /template/ Panel">
            <Style Selector="^:pointerover RepeatButton">
                <Setter Property="IsVisible" Value="True" />
            </Style>
        </Style>
    </ControlTheme>
</ResourceDictionary>