﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI"
                    xmlns:cc="clr-namespace:AvaloniaEdit.CodeCompletion;assembly=AvaloniaEdit">
    <ControlTheme x:Key="{x:Type cc:CompletionListBox}"
                  BasedOn="{StaticResource {x:Type ListBox}}"
                  TargetType="cc:CompletionListBox">
        <Setter Property="Padding" Value="0" />
        <Setter Property="ItemContainerTheme">
            <ControlTheme BasedOn="{StaticResource {x:Type ListBoxItem}}" TargetType="ListBoxItem">
                <Setter Property="TextBlock.Foreground" Value="{DynamicResource SystemControlForegroundBaseHighBrush}" />
                <Setter Property="Background" Value="Red" />
                <Setter Property="BorderBrush" Value="{DynamicResource SystemControlForegroundBaseHighBrush}" />
                <Setter Property="BorderThickness" Value="{DynamicResource ListBoxBorderThemeThickness}" />
                <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
                <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
                <Setter Property="Padding" Value="4,1" />
                <Setter Property="FontSize" Value="12" />
                <Setter Property="FontWeight" Value="DemiBold" />
            </ControlTheme>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>

                <Border Name="border"
                        Background="{DynamicResource SukiCardBackground}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        ClipToBounds="{TemplateBinding ClipToBounds}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Border.Resources>
                        <suki:BiggestItemListBoxConverter x:Key="BiggestListitem" />
                    </Border.Resources>
                    <Grid>
                        <ListBoxItem Margin="0,0,32,0"
                                     Content="{TemplateBinding ItemsSource,
                                                               Converter={StaticResource BiggestListitem}}"
                                     Opacity="0" />
                        <ScrollViewer Name="PART_ScrollViewer"
                                      HorizontalScrollBarVisibility="{TemplateBinding (ScrollViewer.HorizontalScrollBarVisibility)}"
                                      VerticalScrollBarVisibility="{TemplateBinding (ScrollViewer.VerticalScrollBarVisibility)}">
                            <ItemsPresenter Name="PART_ItemsPresenter"
                                            Margin="{TemplateBinding Padding}"
                                            ItemsPanel="{TemplateBinding ItemsPanel}" />
                        </ScrollViewer>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>

    <ControlTheme x:Key="{x:Type cc:CompletionList}" TargetType="cc:CompletionList">
        <Setter Property="Template">
            <ControlTemplate>
                <Border Margin="10"
                        BoxShadow="{DynamicResource SukiPopupShadow}"
                        ClipToBounds="True"
                        CornerRadius="8">
                    <cc:CompletionListBox Name="PART_ListBox">
                        <cc:CompletionListBox.ItemTemplate>
                            <DataTemplate x:DataType="cc:ICompletionData">
                                <StackPanel Margin="0" Orientation="Horizontal">
                                    <Image Width="16"
                                           Height="16"
                                           Margin="0,0,2,0"
                                           Source="{Binding Image}" />
                                    <ContentPresenter Content="{Binding Content}" />
                                </StackPanel>
                            </DataTemplate>
                        </cc:CompletionListBox.ItemTemplate>
                    </cc:CompletionListBox>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
</ResourceDictionary>
