<ResourceDictionary xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="SukiPathIconStyle" TargetType="PathIcon">
        <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Height" Value="{DynamicResource IconElementThemeHeight}" />
        <Setter Property="Width" Value="{DynamicResource IconElementThemeWidth}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}">
                    <Viewbox Width="{TemplateBinding Width}" Height="{TemplateBinding Height}">
                        <Path Data="{TemplateBinding Data}"
                              Fill="{TemplateBinding Foreground}"
                              Stretch="Uniform">
                            <Path.Transitions>
                                <Transitions>
                                    <BrushTransition Property="Fill" Duration="{DynamicResource ShortAnimationDuration}" />
                                </Transitions>
                            </Path.Transitions>
                        </Path>
                    </Viewbox>
                </Border>
            </ControlTemplate>
        </Setter>
        <Style Selector="^.Primary">
            <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
        </Style>
        <Style Selector="^.Accent">
            <Setter Property="Foreground" Value="{DynamicResource SukiAccentColor}" />
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type PathIcon}"
                  BasedOn="{StaticResource SukiPathIconStyle}"
                  TargetType="PathIcon" />
</ResourceDictionary>
