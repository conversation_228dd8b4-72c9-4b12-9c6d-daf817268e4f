using System;
using SukiUI.Demo.Infrastructure;
using SukiUI.Demo.Features.CarControl;

namespace SukiUI.Demo
{
    /// <summary>
    /// 库文件检查测试类
    /// 用于验证第一步实现的库文件检查功能
    /// </summary>
    public static class TestLibraryCheck
    {
        /// <summary>
        /// 运行库文件检查测试
        /// </summary>
        public static void RunTest()
        {
            Console.WriteLine("=== 开始库文件检查测试 ===");
            Console.WriteLine();

            try
            {
                // 1. 基本检查
                Console.WriteLine("1. 基本库文件检查:");
                var basicCheck = NativeLibraryManager.CheckLibraryExists();
                Console.WriteLine($"   结果: {(basicCheck ? "通过" : "失败")}");
                Console.WriteLine();

                // 2. 详细检查
                Console.WriteLine("2. 详细库文件检查:");
                var detailedCheck = NativeLibraryManager.CheckAllLibraryFiles();
                Console.WriteLine($"   平台: {detailedCheck.Platform}");
                Console.WriteLine($"   总文件: {detailedCheck.TotalFiles}");
                Console.WriteLine($"   存在: {detailedCheck.ExistingFiles}");
                Console.WriteLine($"   缺失: {detailedCheck.MissingFiles}");
                Console.WriteLine($"   状态: {(detailedCheck.IsAllLibrariesPresent ? "完整" : "不完整")}");
                Console.WriteLine();

                // 3. 组件库检查
                Console.WriteLine("3. HCNetSDKCom组件库检查:");
                Console.WriteLine($"   存在: {(detailedCheck.ComponentLibraryExists ? "是" : "否")}");
                Console.WriteLine($"   路径: {detailedCheck.ComponentLibraryPath}");
                Console.WriteLine($"   文件数: {detailedCheck.ComponentLibraryCount}");
                Console.WriteLine();

                // 4. 缺失文件列表
                if (detailedCheck.MissingLibraries.Count > 0)
                {
                    Console.WriteLine("4. 缺失的库文件:");
                    foreach (var missing in detailedCheck.MissingLibraries)
                    {
                        Console.WriteLine($"   - {missing}");
                    }
                    Console.WriteLine();
                }

                // 5. 生成完整报告
                Console.WriteLine("5. 生成详细报告:");
                var report = detailedCheck.GenerateReport();
                Console.WriteLine(report);

                // 6. 诊断信息
                Console.WriteLine("6. 诊断信息:");
                var diagnostics = CrossPlatformLibraryTest.GetLibraryDiagnostics();
                Console.WriteLine(diagnostics);

            }
            catch (Exception ex)
            {
                Console.WriteLine($"测试过程中发生异常: {ex.Message}");
                Console.WriteLine($"堆栈跟踪: {ex.StackTrace}");
            }

            Console.WriteLine("=== 库文件检查测试结束 ===");
        }

        /// <summary>
        /// 运行简化测试（用于快速验证）
        /// </summary>
        public static void RunQuickTest()
        {
            Console.WriteLine("=== 快速库文件检查 ===");
            
            try
            {
                var result = NativeLibraryManager.CheckAllLibraryFiles();
                
                Console.WriteLine($"平台: {result.Platform}");
                Console.WriteLine($"状态: {(result.IsAllLibrariesPresent ? "✓ 完整" : "✗ 不完整")}");
                Console.WriteLine($"统计: {result.ExistingFiles}/{result.TotalFiles}");
                
                if (result.MissingFiles > 0)
                {
                    Console.WriteLine($"缺失: {string.Join(", ", result.MissingLibraries.Take(3))}");
                    if (result.MissingLibraries.Count > 3)
                    {
                        Console.WriteLine($"... 还有 {result.MissingLibraries.Count - 3} 个");
                    }
                }
                
                Console.WriteLine($"组件库: {(result.ComponentLibraryExists ? "✓" : "✗")}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"快速测试失败: {ex.Message}");
            }
            
            Console.WriteLine("=== 快速检查结束 ===");
        }
    }
}
