using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using Serilog;
using SukiUI.Demo.Bll;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// SDK资源管理器
    /// 负责统一管理SDK的初始化、连接、清理等资源
    /// </summary>
    public class SDKResourceManager : IDisposable
    {
        private static SDKResourceManager? _instance;
        private static readonly object _lock = new object();
        
        private readonly ConcurrentDictionary<string, DeviceConnection> _connections = new();
        private readonly List<IntPtr> _allocatedMemory = new();
        private readonly object _memoryLock = new object();
        
        private bool _isSDKInitialized = false;
        private bool _isDisposed = false;
        private CancellationTokenSource? _cancellationTokenSource;

        /// <summary>
        /// 获取SDK资源管理器单例
        /// </summary>
        public static SDKResourceManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lock)
                    {
                        if (_instance == null)
                        {
                            _instance = new SDKResourceManager();
                        }
                    }
                }
                return _instance;
            }
        }

        private SDKResourceManager()
        {
            _cancellationTokenSource = new CancellationTokenSource();
            Log.Information("SDKResourceManager: 资源管理器已创建");
        }

        /// <summary>
        /// 初始化SDK
        /// </summary>
        /// <returns>是否成功</returns>
        public bool InitializeSDK()
        {
            lock (_lock)
            {
                if (_isSDKInitialized)
                {
                    Log.Debug("SDKResourceManager: SDK已经初始化");
                    return true;
                }

                try
                {
                    Log.Information("SDKResourceManager: 开始初始化SDK");

                    // 确保NativeLibraryManager已初始化
                    if (!NativeLibraryManager.IsInitialized)
                    {
                        Log.Warning("SDKResourceManager: NativeLibraryManager未初始化，正在初始化...");
                        NativeLibraryManager.Initialize();
                    }

                    // Linux平台应用SDK配置
                    if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Linux))
                    {
                        bool configResult = NativeLibraryManager.ApplyLinuxSDKConfiguration();
                        if (!configResult)
                        {
                            Log.Error("SDKResourceManager: Linux SDK配置失败");
                            return false;
                        }
                    }

                    // 初始化SDK
                    _isSDKInitialized = CHCNetSDK.NET_DVR_Init();
                    
                    if (_isSDKInitialized)
                    {
                        // 设置连接超时
                        CHCNetSDK.NET_DVR_SetConnectTime(5000, 1);
                        
                        // 获取SDK版本
                        var version = CHCNetSDK.NET_DVR_GetSDKVersion();
                        Log.Information($"SDKResourceManager: SDK初始化成功，版本: {version}");
                        
                        return true;
                    }
                    else
                    {
                        var errorCode = CHCNetSDK.NET_DVR_GetLastError();
                        Log.Error($"SDKResourceManager: SDK初始化失败，错误代码: {errorCode}");
                        return false;
                    }
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "SDKResourceManager: SDK初始化异常");
                    return false;
                }
            }
        }

        /// <summary>
        /// 添加设备连接
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        /// <param name="userId">用户ID</param>
        /// <param name="deviceInfo">设备信息</param>
        public void AddConnection(string connectionId, int userId, string deviceInfo)
        {
            if (_isDisposed)
            {
                Log.Warning("SDKResourceManager: 资源管理器已释放，无法添加连接");
                return;
            }

            var connection = new DeviceConnection
            {
                ConnectionId = connectionId,
                UserId = userId,
                DeviceInfo = deviceInfo,
                ConnectedTime = DateTime.Now,
                IsActive = true
            };

            _connections.TryAdd(connectionId, connection);
            Log.Information($"SDKResourceManager: 添加设备连接 - {connectionId}, UserId: {userId}");
        }

        /// <summary>
        /// 移除设备连接
        /// </summary>
        /// <param name="connectionId">连接ID</param>
        public void RemoveConnection(string connectionId)
        {
            if (_connections.TryRemove(connectionId, out var connection))
            {
                try
                {
                    // 登出设备
                    if (connection.UserId > 0)
                    {
                        bool logoutResult = CHCNetSDK.NET_DVR_Logout_V30(connection.UserId);
                        Log.Information($"SDKResourceManager: 设备登出 - {connectionId}, 结果: {logoutResult}");
                    }
                    
                    connection.IsActive = false;
                    connection.DisconnectedTime = DateTime.Now;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, $"SDKResourceManager: 移除连接异常 - {connectionId}");
                }
            }
        }

        /// <summary>
        /// 分配内存并跟踪
        /// </summary>
        /// <param name="size">内存大小</param>
        /// <returns>内存指针</returns>
        public IntPtr AllocateMemory(int size)
        {
            if (_isDisposed)
            {
                throw new ObjectDisposedException(nameof(SDKResourceManager));
            }

            var ptr = System.Runtime.InteropServices.Marshal.AllocHGlobal(size);
            
            lock (_memoryLock)
            {
                _allocatedMemory.Add(ptr);
            }
            
            Log.Debug($"SDKResourceManager: 分配内存 {size} 字节，指针: {ptr}");
            return ptr;
        }

        /// <summary>
        /// 释放内存
        /// </summary>
        /// <param name="ptr">内存指针</param>
        public void FreeMemory(IntPtr ptr)
        {
            if (ptr == IntPtr.Zero) return;

            lock (_memoryLock)
            {
                if (_allocatedMemory.Remove(ptr))
                {
                    System.Runtime.InteropServices.Marshal.FreeHGlobal(ptr);
                    Log.Debug($"SDKResourceManager: 释放内存，指针: {ptr}");
                }
            }
        }

        /// <summary>
        /// 获取连接统计信息
        /// </summary>
        /// <returns>统计信息</returns>
        public ConnectionStatistics GetConnectionStatistics()
        {
            var stats = new ConnectionStatistics
            {
                TotalConnections = _connections.Count,
                ActiveConnections = _connections.Values.Count(c => c.IsActive),
                AllocatedMemoryBlocks = _allocatedMemory.Count,
                IsSDKInitialized = _isSDKInitialized
            };

            return stats;
        }

        /// <summary>
        /// 优雅关闭所有资源
        /// </summary>
        public void Shutdown()
        {
            if (_isDisposed) return;

            Log.Information("SDKResourceManager: 开始优雅关闭");

            try
            {
                // 1. 停止取消令牌
                _cancellationTokenSource?.Cancel();

                // 2. 登出所有设备连接
                LogoutAllDevices();

                // 3. 清理SDK资源
                CleanupSDK();

                // 4. 释放所有分配的内存
                FreeAllMemory();

                Log.Information("SDKResourceManager: 优雅关闭完成");
            }
            catch (Exception ex)
            {
                Log.Error(ex, "SDKResourceManager: 优雅关闭过程中发生异常");
            }
        }

        /// <summary>
        /// 登出所有设备
        /// </summary>
        private void LogoutAllDevices()
        {
            Log.Information($"SDKResourceManager: 开始登出 {_connections.Count} 个设备连接");

            var logoutTasks = new List<Task>();

            foreach (var connection in _connections.Values.ToList())
            {
                if (connection.IsActive && connection.UserId > 0)
                {
                    var task = Task.Run(() =>
                    {
                        try
                        {
                            bool result = CHCNetSDK.NET_DVR_Logout_V30(connection.UserId);
                            Log.Information($"SDKResourceManager: 设备登出 - {connection.ConnectionId}, 结果: {result}");
                            connection.IsActive = false;
                            connection.DisconnectedTime = DateTime.Now;
                        }
                        catch (Exception ex)
                        {
                            Log.Error(ex, $"SDKResourceManager: 设备登出异常 - {connection.ConnectionId}");
                        }
                    });
                    
                    logoutTasks.Add(task);
                }
            }

            // 等待所有登出操作完成，最多等待10秒
            try
            {
                Task.WaitAll(logoutTasks.ToArray(), TimeSpan.FromSeconds(10));
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "SDKResourceManager: 等待设备登出超时");
            }

            _connections.Clear();
            Log.Information("SDKResourceManager: 所有设备连接已清理");
        }

        /// <summary>
        /// 清理SDK资源
        /// </summary>
        private void CleanupSDK()
        {
            if (_isSDKInitialized)
            {
                try
                {
                    bool result = CHCNetSDK.NET_DVR_Cleanup();
                    Log.Information($"SDKResourceManager: SDK清理结果: {result}");
                    _isSDKInitialized = false;
                }
                catch (Exception ex)
                {
                    Log.Error(ex, "SDKResourceManager: SDK清理异常");
                }
            }
        }

        /// <summary>
        /// 释放所有分配的内存
        /// </summary>
        private void FreeAllMemory()
        {
            lock (_memoryLock)
            {
                Log.Information($"SDKResourceManager: 开始释放 {_allocatedMemory.Count} 个内存块");

                foreach (var ptr in _allocatedMemory.ToList())
                {
                    try
                    {
                        System.Runtime.InteropServices.Marshal.FreeHGlobal(ptr);
                    }
                    catch (Exception ex)
                    {
                        Log.Warning(ex, $"SDKResourceManager: 释放内存失败，指针: {ptr}");
                    }
                }

                _allocatedMemory.Clear();
                Log.Information("SDKResourceManager: 所有内存块已释放");
            }
        }

        /// <summary>
        /// 释放资源
        /// </summary>
        public void Dispose()
        {
            if (!_isDisposed)
            {
                Shutdown();
                _cancellationTokenSource?.Dispose();
                _isDisposed = true;
                Log.Information("SDKResourceManager: 资源管理器已释放");
            }
        }
    }

    /// <summary>
    /// 设备连接信息
    /// </summary>
    public class DeviceConnection
    {
        public string ConnectionId { get; set; } = "";
        public int UserId { get; set; } = -1;
        public string DeviceInfo { get; set; } = "";
        public DateTime ConnectedTime { get; set; } = DateTime.Now;
        public DateTime? DisconnectedTime { get; set; }
        public bool IsActive { get; set; } = false;
    }

    /// <summary>
    /// 连接统计信息
    /// </summary>
    public class ConnectionStatistics
    {
        public int TotalConnections { get; set; } = 0;
        public int ActiveConnections { get; set; } = 0;
        public int AllocatedMemoryBlocks { get; set; } = 0;
        public bool IsSDKInitialized { get; set; } = false;
    }
}
