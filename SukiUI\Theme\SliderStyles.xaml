﻿<Styles xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:suki="https://github.com/kikipoulet/SukiUI"
        xmlns:system="clr-namespace:System;assembly=netstandard">
    <Design.PreviewWith>
        <Border Padding="20" Background="White">
            <Slider Width="100" Value="50" />
        </Border>
    </Design.PreviewWith>

    <Style Selector="RepeatButton.Decrease">
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Border Height="12"
                            Margin="0,0,-12,0"
                            Background="Red"
                            CornerRadius="{DynamicResource SmallCornerRadius}" />
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style Selector="Border.ThumbB">
        <Setter Property="BoxShadow" Value="0 0 4 0 #999999" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiPrimaryColor}" />
        <Setter Property="Transitions">
            <Transitions>
                <BoxShadowsTransition Property="BoxShadow" Duration="0:0:0.3" />

            </Transitions>
        </Setter>

    </Style>

    <Style Selector="Border.ThumbBorder">
        <Setter Property="Transitions">
            <Transitions>
                <ThicknessTransition Property="BorderThickness" Duration="0:0:0.1" />
            </Transitions>
        </Setter>
    </Style>

    <Style Selector="Border.ThumbBorder:pointerover">
        <Setter Property="BoxShadow" Value="0 0 7 0 #999999" />
        <Setter Property="BorderThickness" Value="1.5" />

    </Style>



    <Style Selector="Slider:horizontal">
        <Setter Property="MinWidth" Value="40" />
        <Setter Property="MinHeight" Value="20" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid Name="grid">
                    <Grid.Resources>
                       
                    </Grid.Resources>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto" />
                        <RowDefinition Height="Auto" MinHeight="20" />
                        <RowDefinition Height="Auto" />
                    </Grid.RowDefinitions>
                    <suki:GlassCard Name="TrackBackground"
                            Grid.Row="1" Classes="Control"
                            Height="22"
                            Margin="8,0"
                            VerticalAlignment="Center"
                            BorderThickness="0"
                            CornerRadius="20" />

                    <Track Name="PART_Track"
                           Grid.Row="1"
                           IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                           Orientation="Horizontal">
                        <Track.DecreaseButton>
                            <RepeatButton Name="PART_DecreaseButton"
                                          Height="22"
                                          Margin="8,0,-30,0"
                                          HorizontalAlignment="Stretch"
                                          Background="{DynamicResource SukiPrimaryColor}"
                                          BorderThickness="0"
                                          CornerRadius="20" />
                        </Track.DecreaseButton>
                        <Track.IncreaseButton>
                            <RepeatButton Name="PART_IncreaseButton" Classes="repeattrack" />
                        </Track.IncreaseButton>
                        <Thumb Name="thumb" Margin="3,0,0,0">
                            <Thumb.Template>
                                <ControlTemplate>
                                    <Grid Width="32" Height="32">
                                        <Border Width="16"
                                                Height="16"
                                                Margin="0"
                                                Background="White"
                                                BorderThickness="0"
                                                Classes="ThumbBorder ThumbB"
                                                CornerRadius="{DynamicResource MediumCornerRadius}" />
                                    </Grid>
                                </ControlTemplate>
                            </Thumb.Template>
                        </Thumb>
                    </Track>


                </Grid>
            </ControlTemplate>
        </Setter>
    </Style>
    <Style Selector="Slider:vertical">
        <Setter Property="MinWidth" Value="20" />
        <Setter Property="MinHeight" Value="40" />
        <Setter Property="Template">
            <ControlTemplate>
                   <Grid Name="grid">
             
                    <suki:GlassCard Name="TrackBackground"
                           Classes="Control"
                            Width="22"
                            Margin="8,8"
                            HorizontalAlignment="Center"
                            BorderThickness="0"
                            CornerRadius="20" />

                    <Track Name="PART_Track"
                        
                           IsDirectionReversed="{TemplateBinding IsDirectionReversed}"
                           Orientation="Vertical">
                        <Track.DecreaseButton>
                            <RepeatButton Name="PART_DecreaseButton"
                                          Width="22"
                                          Margin="0,-30,0,7"
                                          HorizontalAlignment="Stretch"
                                          Background="{DynamicResource SukiPrimaryColor}"
                                          BorderThickness="0"
                                          CornerRadius="20" />
                        </Track.DecreaseButton>
                        <Track.IncreaseButton>
                            <RepeatButton Name="PART_IncreaseButton" Classes="repeattrack" />
                        </Track.IncreaseButton>
                        <Thumb Name="thumb" Margin="0,0,0,2">
                            <Thumb.Template>
                                <ControlTemplate>
                                    <Grid Width="32" Height="32">
                                        <Border Width="16"
                                                Height="16"
                                                Margin="0"
                                                Background="White"
                                                BorderThickness="0"
                                                Classes="ThumbBorder ThumbB"
                                                CornerRadius="{DynamicResource MediumCornerRadius}" />
                                    </Grid>
                                </ControlTemplate>
                            </Thumb.Template>
                        </Thumb>
                    </Track>


                </Grid>
            </ControlTemplate>
        </Setter>
    </Style>
    <Style Selector="Slider /template/ Track#PART_Track">
        <Setter Property="Minimum" Value="{TemplateBinding Minimum}" />
        <Setter Property="Maximum" Value="{TemplateBinding Maximum}" />
        <Setter Property="Value" Value="{TemplateBinding Value, Mode=TwoWay}" />
    </Style>
 
    <Style Selector="Slider /template/ RepeatButton.repeattrack">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Foreground" Value="{DynamicResource ThemeBorderLowBrush}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="{TemplateBinding Background}" />
            </ControlTemplate>
        </Setter>
    </Style>
    <Style Selector="Slider /template/ TickBar">
        <Setter Property="Ticks" Value="{TemplateBinding Ticks}" />
    </Style>
    <Style Selector="Slider:disabled /template/ Grid#grid">
        <Setter Property="Opacity" Value="{DynamicResource ThemeDisabledOpacity}" />
    </Style>
</Styles>