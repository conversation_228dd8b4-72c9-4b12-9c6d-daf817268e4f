﻿using NetCoreServer;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net.Sockets;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Diagnostics;

namespace SukiUI.Demo.Bll
{
    public class SiHeYiUdpServer : UdpServer
    {
        public Action<string> DataReceived;
        public SiHeYiUdpServer(IPAddress address, int port) : base(address, port) { }

        protected override void OnStarted()
        {
            // Start receive datagrams
            ReceiveAsync();
        }

        protected override void OnReceived(EndPoint endpoint, byte[] buffer, long offset, long size)
        {
            //Console.WriteLine("Incoming: " + Encoding.UTF8.GetString(buffer, (int)offset, (int)size));
            var message = Encoding.UTF8.GetString(buffer, (int)offset, (int)size);
            DataReceived?.Invoke(message);
            //继续接收数据包
            ReceiveAsync();
            // Echo the message back to the sender
            //也可以通过回复数据包，来继续接收数据
            //SendAsync(endpoint, buffer, 0, size);
        }

        protected override void OnSent(EndPoint endpoint, long sent)
        {
            // Continue receive datagrams
            ReceiveAsync();
        }

        protected override void OnError(SocketError error)
        {
            Console.WriteLine($"Echo UDP server caught an error with code {error}");
        }
    }
}
