﻿
using Mapsui.Layers;
using Mapsui;
using Mapsui.Extensions;
using SkiaSharp;
using NetTopologySuite.Shape.Random;

using Mapsui.Rendering.Skia;

using Mapsui.Rendering.Skia.SkiaStyles;

using Mapsui.Styles;
using Mapsui.Tiling;
using Mapsui.UI;



using Mapsui.Rendering;
using Mapsui.UI.Avalonia;
namespace SukiUI.Demo.Bll
{    
    public class ImageRectStyle : IStyle
    {
        public double MinVisible { get; set; } = 0;
        public double MaxVisible { get; set; } = double.MaxValue;
        public bool Enabled { get; set; } = true;
        public float Opacity { get; set; } = 0.7f;
        public string ImagePath { get; set; } = string.Empty; // 图片路径
        public float Rotation { get; set; } = 0;
        public IList<MPoint> TargetGeoPoints { get; set; }=  new List<MPoint>();
    }

    public class SkiaCustomStyleRenderer : ISkiaStyleRenderer
    {
        public static Random Random = new(1);

        public bool Draw(SKCanvas canvas, Viewport viewport, ILayer layer, IFeature feature, IStyle style, IRenderCache renderCache, long iteration)
        {
            if (style is not ImageRectStyle imageRectStyle) return false;

            //if (!feature.Extent.HasValue) return false; // 需要 feature 带有 Envelope

            var envelope = feature.Extent;

            //viewport.WorldToScreen(envelope);
            // 地理坐标转屏幕坐标
            var min = viewport.WorldToScreen(envelope.MinX, envelope.MinY);
            var max = viewport.WorldToScreen(envelope.MaxX, envelope.MaxY);

            var left = Math.Min(min.X, max.X);
            var top = Math.Min(min.Y, max.Y);
            var right = Math.Max(min.X, max.X);
            var bottom = Math.Max(min.Y, max.Y);

            var destRect = new SKRect((float)left, (float)top, (float)right, (float)bottom);

            // 加载图片
            using var bitmap = SKBitmap.Decode(imageRectStyle.ImagePath);
            if (bitmap == null) return false;

            using var paint = new SKPaint { Color = new SKColor(255, 255, 255, (byte)(255 * layer.Opacity * imageRectStyle.Opacity)) };
            // 计算中心点
            var centerX = (float)((left + right) / 2);
            var centerY = (float)((top + bottom) / 2);
            // 保存画布状态
            canvas.Save();

            // 1. 平移到中心
            canvas.Translate(centerX, centerY);
            // 2. 旋转
            if (imageRectStyle.Rotation != 0)
                canvas.RotateDegrees(imageRectStyle.Rotation);
            // 3. 平移回去
            canvas.Translate(-centerX, -centerY);

            // 4. 绘制图片（此时图片会以中心为轴旋转）
            canvas.DrawBitmap(bitmap, destRect, paint);

            // 恢复画布状态
            canvas.Restore();

            // 拉伸绘制
            //canvas.DrawBitmap(bitmap, destRect, paint);

            return true;
        }

    }

    public class CustomStyleSample 
    {
        //public string Name => "Custom Style";
        //public string Category => "Styles";

        //private const string _mapInfoLayerName = "Custom Style Layer";

        //public void Setup(IMapControl mapControl)
        //{
        //    mapControl.Map = CreateMap();
        //}

        public static void CreateMap(MapControl mapControl)
        {
           
            mapControl.Renderer.StyleRenderers.Add(typeof(ImageRectStyle), new SkiaCustomStyleRenderer());
            //var map = new Map();

            //map.Layers.Add(OpenStreetMap.CreateTileLayer());
            //map.Layers.Add(CreateStylesLayer(map.Extent));

            //map.Widgets.Add(new MapInfoWidget(map, l => l.Name == _mapInfoLayerName));

            //return map;
        }

        private static ILayer CreateStylesLayer(List<IFeature> feature,string layerName)
        {
            return new MemoryLayer
            {
                Name = layerName,
                Features = feature,
                Style = new ImageRectStyle(),
            };
        }

        //private static IEnumerable<IFeature> CreateDiverseFeatures(IEnumerable<MPoint> randomPoints)
        //{
        //    var features = new List<IFeature>();
        //    var style = new CustomStyle();
        //    var counter = 1;
        //    foreach (var point in randomPoints)
        //    {
        //        var feature = new PointFeature(point);
        //        feature["Label"] = $"I'm no. {counter++} and, autsch, you hit me!";
        //        feature.Styles.Add(style); // Here the custom style is set!
        //        feature.Styles.Add(SmalleDot());
        //        features.Add(feature);
        //    }
        //    return features;
        //}

        private static IStyle SmalleDot()
        {
            return new SymbolStyle { SymbolScale = 0.2, Fill = new Brush(new Color(40, 40, 40)) };
        }
    }
}
