﻿<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.InfoBarView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:InfoBarViewModel"
             mc:Ignorable="d">
    <Grid RowDefinitions="Auto, *">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="InfoBar">
                <StackPanel Classes="HeaderContent" Spacing="15">
                    <TextBlock>
                        InfoBar is a control that displays a message and can be used to show specific severity message to the user.
                    </TextBlock>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Margin="0,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="Long Message:" />
                        <TextBox Name="MessageTextBox"
                                 Width="500"
                                 Margin="15,0,0,0"
                                 Text="Hello world !" />
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Margin="0,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="IsOpen Status" />
                        <Button Margin="15,0,0,0"
                                Command="{Binding RefreshIsOpenStatusCommand}"
                                Content="Refresh" />
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Margin="0,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="IsClosable" />
                        <ToggleSwitch Margin="15,0,0,0" IsChecked="{Binding IsClosable}" />
                    </StackPanel>
                    
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Margin="0,0,0,0"
                                   VerticalAlignment="Center"
                                   FontWeight="DemiBold"
                                   Text="IsOpaque" />
                        <ToggleSwitch Margin="15,0,0,0" IsChecked="{Binding IsOpaque}" />
                    </StackPanel>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard>
                    <StackPanel Spacing="7">
                        <suki:InfoBar Title="Info" IsOpaque="{Binding IsOpaque}"
                                      Margin="10"
                                      IsClosable="{Binding IsClosable}"
                                      IsOpen="{Binding IsOpen, Mode=TwoWay}"
                                      Message="{Binding #MessageTextBox.Text}" />
                        <suki:InfoBar Title="Warning"  IsOpaque="{Binding IsOpaque}"
                                      Margin="10"
                                      IsClosable="{Binding IsClosable}"
                                      IsOpen="{Binding IsOpen, Mode=TwoWay}"
                                      Message="{Binding #MessageTextBox.Text}"
                                      Severity="Warning" />
                        <suki:InfoBar Title="Error"  IsOpaque="{Binding IsOpaque}"
                                      Margin="10"
                                      IsClosable="{Binding IsClosable}"
                                      IsOpen="{Binding IsOpen, Mode=TwoWay}"
                                      Message="{Binding #MessageTextBox.Text}"
                                      Severity="Error" />
                        <suki:InfoBar Title="Success"
                                      Margin="10"  IsOpaque="{Binding IsOpaque}"
                                      IsClosable="{Binding IsClosable}"
                                      IsOpen="{Binding IsOpen, Mode=TwoWay}"
                                      Message="{Binding #MessageTextBox.Text}"
                                      Severity="Success" />
                    </StackPanel>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
