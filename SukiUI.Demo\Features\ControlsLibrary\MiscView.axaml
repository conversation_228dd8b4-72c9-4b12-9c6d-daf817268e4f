<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.MiscView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:showMeTheXaml="clr-namespace:ShowMeTheXaml;assembly=ShowMeTheXaml.Avalonia"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:MiscViewModel"
             mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Miscellaneous">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        An assortment of controls that don't otherwise fit neatly into another category.
                    </TextBlock>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard MaxWidth="300">
                    <suki:BusyArea BusyText="Busy..." IsBusy="{Binding IsBusy}">
                        <suki:GroupBox Header="Busy Area">
                            <StackPanel Spacing="10">
                                <TextBlock Classes="h3" TextWrapping="Wrap">Click this button to set the card area busy for 3 seconds.</TextBlock>
                                <Button HorizontalAlignment="Center"
                                        Command="{Binding ToggleBusyCommand}"
                                        Content="Click Me." />
                            </StackPanel>
                        </suki:GroupBox>
                    </suki:BusyArea>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Date Picker">
                        <StackPanel Spacing="15">
                            <DatePicker SelectedDate="{Binding SelectedDateTimeOffset}" />
                            <CalendarDatePicker SelectedDate="{Binding SelectedDateTimeOffset}" />
                        </StackPanel>

                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Calendar">
                        <Calendar SelectedDate="{Binding SelectedDateTime}" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Time Picker">
                        <TimePicker />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Numeric Up/Down">
                        <StackPanel Spacing="10">
                            <showMeTheXaml:XamlDisplay UniqueId="Numeric1">
                                <NumericUpDown Value="10" />
                            </showMeTheXaml:XamlDisplay>
                            <showMeTheXaml:XamlDisplay UniqueId="Numeric2">
                                <NumericUpDown suki:NumericUpDownExtensions.Unit="inch" Value="10" />
                            </showMeTheXaml:XamlDisplay>
                            <showMeTheXaml:XamlDisplay UniqueId="Numeric3">
                                <NumericUpDown suki:NumericUpDownExtensions.Unit="inch"
                                               ShowButtonSpinner="False"
                                               Value="10" />
                            </showMeTheXaml:XamlDisplay>

                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="DropDownButton">
                        <DropDownButton VerticalAlignment="Top" Content="Click To Open">
                            <DropDownButton.Flyout>
                                <Flyout>
                                    <Border Padding="10"
                                            Classes="Card"
                                            CornerRadius="8">
                                        <TextBlock Foreground="{DynamicResource SukiText}">
                                            Dropdown button content.
                                        </TextBlock>
                                    </Border>
                                </Flyout>
                            </DropDownButton.Flyout>
                        </DropDownButton>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard>
                    <suki:GroupBox Header="File Picker">
                        <!-- <StackPanel Spacing="8"> -->
                        <!--  <Button Margin="5"  -->
                        <!--  HorizontalAlignment="Center"  -->
                        <!--  VerticalAlignment="Center"  -->
                        <!--  Classes="Primary"  -->
                        <!--  Command="{Binding OpenFileCommand}"  -->
                        <!--  Content="Open File" />  -->
                        <!--  <Button Margin="5"  -->
                        <!--  HorizontalAlignment="Center"  -->
                        <!--  VerticalAlignment="Center"  -->
                        <!--  Classes="Primary"  -->
                        <!--  Command="{Binding SaveFileCommand}"  -->
                        <!--  Content="Save File" />  -->
                        <!--  <Button Margin="5"  -->
                        <!--  HorizontalAlignment="Center"  -->
                        <!--  VerticalAlignment="Center"  -->
                        <!--  Classes="Primary"  -->
                        <!--  Command="{Binding OpenFolderCommand}"  -->
                        <!--  Content="Open Folder" />  -->
                        <!-- </StackPanel> -->
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</UserControl>