﻿<Styles xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:suki="https://github.com/kikipoulet/SukiUI">
    <Design.PreviewWith>
        <Border Padding="20" Background="White">
            <StackPanel Spacing="20">
                <CheckBox>Unchecked</CheckBox>
                <CheckBox IsChecked="True">Checked</CheckBox>
                <CheckBox IsChecked="{x:Null}" IsThreeState="True">Indeterminate</CheckBox>
            </StackPanel>
        </Border>
    </Design.PreviewWith>

    <Style Selector="CheckBox AccessText">
        <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
    </Style>

    <Style Selector="CheckBox">
        <Setter Property="Foreground" Value="{DynamicResource ThemeForegroundBrush}" />
        <Setter Property="Background" Value="{DynamicResource SukiCardBackground}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ThemeBorderMidBrush}" />
        <Setter Property="BorderThickness" Value="0.5" />
        <Setter Property="Padding" Value="10,1,0,0" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid ColumnDefinitions="Auto,*">
                    <suki:GlassCard IsInteractive="True" Classes="Control" Name="border"
                            Width="20"
                            Height="20"
                            VerticalAlignment="Center"  
                        Classes.PrimaryOpaque="{TemplateBinding IsChecked}"
                       
                            CornerRadius="6">
                        
                        <Panel>
                            <Path Name="checkMark"
                                  Width="9"
                                  Height="9" Opacity="1"
                                  Margin="1,1,0,0"
                                  HorizontalAlignment="Center"
                                  VerticalAlignment="Center"
								  FlowDirection="LeftToRight"
                                  Data="M 1145.607177734375,430 C1145.607177734375,430 1141.449951171875,435.0772705078125 1141.449951171875,435.0772705078125 1141.449951171875,435.0772705078125 1139.232177734375,433.0999755859375 1139.232177734375,433.0999755859375 1139.232177734375,433.0999755859375 1138,434.5538330078125 1138,434.5538330078125 1138,434.5538330078125 1141.482177734375,438 1141.482177734375,438 1141.482177734375,438 1141.96875,437.9375 1141.96875,437.9375 1141.96875,437.9375 1147,431.34619140625 1147,431.34619140625 1147,431.34619140625 1145.607177734375,430 1145.607177734375,430 z"
                                  Fill="White"
                                  Stretch="Uniform" >
                                <Path.Transitions>
                                    <Transitions>
                                    <DoubleTransition Property="Opacity" Duration="0:0:0.2"></DoubleTransition>
                                        </Transitions>
                                </Path.Transitions>
                            </Path>
                            <Border Name="IndetBorder" Opacity="0" VerticalAlignment="Center" HorizontalAlignment="Center" Background="White" Height="3" Width="8" CornerRadius="2">
                                <Border.Transitions>
                                    <Transitions>
                                    <DoubleTransition Property="Opacity" Duration="0:0:0.2"></DoubleTransition>
                                    </Transitions>
                                </Border.Transitions>
                            </Border>
                        </Panel>
                    </suki:GlassCard>
                    <ContentPresenter Name="PART_ContentPresenter"
                                      Grid.Column="1"
                                      Margin="{TemplateBinding Padding}"
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      FontSize="{TemplateBinding FontSize}"
                                      IsVisible="{TemplateBinding Content,
                                                                  Converter={x:Static ObjectConverters.IsNotNull}}"
                                      RecognizesAccessKey="True" />
                </Grid>
            </ControlTemplate>
        </Setter>
    </Style>
    <Style Selector="CheckBox TextBlock">
        <Setter Property="Foreground" Value="{DynamicResource SukiText}" />
    </Style>

    <Style Selector="CheckBox /template/ Path#checkMark">
        <Setter Property="Opacity" Value="0"></Setter>
    </Style>
    <Style Selector="CheckBox /template/ Rectangle#indeterminateMark">
        <Setter Property="Opacity" Value="0"></Setter>
    </Style>
    <Style Selector="CheckBox:checked /template/ Path#checkMark">
        <Setter Property="Opacity" Value="1"></Setter>
    </Style>
    <Style Selector="CheckBox:indeterminate /template/ Rectangle#indeterminateMark">
        <Setter Property="Opacity" Value="0"></Setter>
    </Style>
    
    <Style Selector="CheckBox:indeterminate /template/ Border#IndetBorder">
        <Setter Property="Opacity" Value="1"></Setter>
    </Style>
 
    <Style Selector="CheckBox:disabled /template/ suki|GlassCard#border">
        <Setter Property="Opacity" Value="0.7"></Setter>
    </Style>
</Styles>
