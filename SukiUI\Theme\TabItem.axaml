<ResourceDictionary xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Border Width="400" Height="400">
            <Panel>
                <TabControl Margin="0,0,0,200" TabStripPlacement="Bottom">
                    <TabItem Header="Arch">
                        <Border Height="100" Background="AntiqueWhite">
                            <TextBlock FontSize="20"
                                       Foreground="Black"
                                       Text="Content" />
                        </Border>
                    </TabItem>
                    <TabItem Header="Leaf">
                        <Border Height="100" Background="Green" />
                    </TabItem>
                    <TabItem Header="Disabled" IsEnabled="False" />
                </TabControl>
            </Panel>
        </Border>
    </Design.PreviewWith>
    <ControlTheme x:Key="SukiTabItemStyle" TargetType="TabItem">
        <Setter Property="FontSize" Value="50" />
        <Setter Property="FontWeight" Value="Bold" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Margin" Value="0" />
        <Setter Property="MinHeight" Value="{DynamicResource TabItemMinHeight}" />
        <Setter Property="VerticalContentAlignment" Value="Bottom" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="PART_LayoutRoot"
                        Margin="8,0"
                        Padding="{TemplateBinding Padding}"
                        Background="Transparent"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <StackPanel>
                        <ContentPresenter Name="PART_HeaderPresenter"
                                          Padding="0,5"
                                          HorizontalAlignment="Center"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          HorizontalContentAlignment="Center"
                                          Content="{TemplateBinding Header}"
                                          ContentTemplate="{TemplateBinding HeaderTemplate}"
                                          TextBlock.FontFamily="{TemplateBinding FontFamily}"
                                          TextBlock.FontSize="{TemplateBinding FontSize}"
                                          TextBlock.FontWeight="{TemplateBinding FontWeight}">
                            <ContentPresenter.Transitions>
                                <Transitions>
                                    <BrushTransition Property="Foreground" Duration="0:0:0.1" />
                                </Transitions>
                            </ContentPresenter.Transitions>
                        </ContentPresenter>
                        <Border Name="PART_Underline"
                                Height="3"
                                Margin="10,0,10,0"
                                Background="{DynamicResource SukiPrimaryColor}"
                                CornerRadius="55"
                                Opacity="0">
                            <Border.Transitions>
                                <Transitions>
                                    <DoubleTransition Property="Opacity" Duration="0:0:0.3" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>
                    </StackPanel>
                </Border>
            </ControlTemplate>
        </Setter>

        <Style Selector="^:selected /template/ Border#PART_Underline">
            <Setter Property="Opacity" Value="1" />
        </Style>

        <Style Selector="^ /template/ Border#PART_LayoutRoot">
            <Setter Property="Background" Value="{Binding $parent[TabItem].Background}" />
        </Style>

        <Style Selector="^:pointerover">
            <Setter Property="Background" Value="Transparent" />
            <Style Selector="^ /template/ ContentPresenter#PART_HeaderPresenter">
                <Setter Property="TextBlock.Foreground" Value="{DynamicResource SukiPrimaryColor75}" />
            </Style>
        </Style>

        <Style Selector="^:selected">
            <Setter Property="Background" Value="Transparent" />
            <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
            <Style Selector="^ /template/ ContentPresenter#PART_HeaderPresenter">
                <Setter Property="TextBlock.Foreground" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>
            <Style Selector="^ /template/ Border#PART_SelectedPipe">
                <Setter Property="Opacity" Value="1" />
            </Style>
        </Style>

        <Style Selector="^[IsEnabled=False]">
            <Setter Property="Foreground" Value="{DynamicResource SukiDisabledText}" />
        </Style>

        <Style Selector="^[TabStripPlacement=Bottom]">
            <Style Selector="^ /template/ ContentPresenter#PART_HeaderPresenter">
                <Setter Property="DockPanel.Dock" Value="Bottom" />
                <Setter Property="Margin" Value="0,0,0,0" />
            </Style>
            <Style Selector="^ Border#PART_Underline">
                <Setter Property="Margin" Value="10,0,10,0" />
            </Style>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type TabItem}"
                  BasedOn="{StaticResource SukiTabItemStyle}"
                  TargetType="TabItem" />
</ResourceDictionary>