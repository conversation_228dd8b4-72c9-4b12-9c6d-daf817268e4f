<suki:SukiWindow x:Class="SukiUI.Demo.SukiUIDemoView"
                 xmlns="https://github.com/avaloniaui"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:avalonia="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
                 xmlns:converters="clr-namespace:SukiUI.Demo.Converters"
                 xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                 xmlns:demo="clr-namespace:SukiUI.Demo"
                 xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                 xmlns:suki="https://github.com/kikipoulet/SukiUI"
                 Title="加滋杰 - 四合一"
                 d:DesignHeight="600"
                 d:DesignWidth="800"
                 x:DataType="demo:SukiUIDemoViewModel"
                 BackgroundAnimationEnabled="{Binding AnimationsEnabled}"
                 BackgroundShaderFile="{Binding CustomShaderFile}"
                 BackgroundStyle="{Binding BackgroundStyle}"
                 BackgroundTransitionsEnabled="{Binding TransitionsEnabled}"
                 CanMinimize="{Binding !WindowLocked}"
                 CanMove="{Binding !WindowLocked}"
                 CanResize="{Binding !WindowLocked}"
                 Icon="Assets/samllicon.png"
                 IsMenuVisible="True"
                 IsTitleBarVisible="{Binding TitleBarVisible, Mode=TwoWay}"
                 ShowBottomBorder="{Binding ShowBottomBar}"
                 ShowTitlebarBackground="{Binding ShowTitleBar}"
                 WindowState="Maximized"
                 mc:Ignorable="d">
    <suki:SukiWindow.TitleBarContextMenu>
        <ContextMenu>
            <MenuItem Header="Custom TitleBar Context Menu Item" />
        </ContextMenu>
    </suki:SukiWindow.TitleBarContextMenu>
    <suki:SukiWindow.Hosts>
        <suki:SukiToastHost Manager="{Binding ToastManager}" />
        <suki:SukiDialogHost Manager="{Binding DialogManager}" />
    </suki:SukiWindow.Hosts>
    <suki:SukiWindow.LogoContent>
        <avalonia:MaterialIcon Width="20"
                               Height="20"
                               VerticalAlignment="Center"
                               Foreground="{DynamicResource SukiPrimaryColor}"
                               Kind="Car">
            <avalonia:MaterialIcon.Transitions>
                <Transitions>
                    <BrushTransition Property="Foreground" Duration="{DynamicResource ShortAnimationDuration}" />
                </Transitions>
            </avalonia:MaterialIcon.Transitions>
        </avalonia:MaterialIcon>
    </suki:SukiWindow.LogoContent>
    <!--<suki:SukiWindow.RightWindowTitleBarControls>
        <Button Classes="Basic Rounded WindowControlsButton">
            <avalonia:MaterialIcon Foreground="{DynamicResource SukiText}" Kind="Cog" />
            <Button.Flyout>
                <Flyout>
                    <Panel>

                        <Border Margin="20"
                                BoxShadow="{DynamicResource SukiPopupShadow}"
                                CornerRadius="20" />

                        <Border MinWidth="350"
                                Margin="20"
                                Background="{DynamicResource SukiCardBackground}"
                                BorderBrush="{DynamicResource SukiLightBorderBrush}"
                                BorderThickness="1"
                                ClipToBounds="True"
                                CornerRadius="15">
                            <Border Padding="15" Background="{DynamicResource PopupGradientBrush}">

                                <StackPanel Margin="0,0,0,0" Spacing="20">

                                    <Grid ColumnDefinitions="*,20,*">

                                        <suki:GlassCard Padding="15" CornerRadius="15">
                                            <DockPanel>
                                                <Border Width="35"
                                                        Height="35"
                                                        Padding="0"
                                                        Background="{DynamicResource SukiPrimaryColor}"
                                                        CornerRadius="100">
                                                    <avalonia:MaterialIcon Width="20"
                                                                           Height="20"
                                                                           HorizontalAlignment="Center"
                                                                           VerticalAlignment="Center"
                                                                           Foreground="White"
                                                                           Kind="Wifi" />
                                                </Border>

                                                <TextBlock HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           FontSize="15"
                                                           FontWeight="DemiBold"
                                                           Text="Wifi" />
                                            </DockPanel>
                                        </suki:GlassCard>
                                        <suki:GlassCard Grid.Column="2"
                                                        Padding="15"
                                                        CornerRadius="15">
                                            <DockPanel>
                                                <suki:GlassCard Width="35"
                                                                Height="35"
                                                                Padding="0"
                                                                CornerRadius="100">
                                                    <avalonia:MaterialIcon Width="20"
                                                                           Height="20"
                                                                           HorizontalAlignment="Center"
                                                                           VerticalAlignment="Center"
                                                                           Foreground="{DynamicResource SukiText}"
                                                                           Kind="Bluetooth" />
                                                </suki:GlassCard>

                                                <TextBlock HorizontalAlignment="Center"
                                                           VerticalAlignment="Center"
                                                           FontSize="14"
                                                           FontWeight="DemiBold"
                                                           Text="Bluetooth" />
                                            </DockPanel>
                                        </suki:GlassCard>
                                    </Grid>

                                    <suki:GlassCard Padding="15" CornerRadius="15">

                                        <DockPanel>
                                            <TextBlock Margin="0,0,0,10"
                                                       VerticalAlignment="Center"
                                                       DockPanel.Dock="Top"
                                                       FontSize="15"
                                                       FontWeight="DemiBold"
                                                       Text="Volume" />
                                            <avalonia:MaterialIcon DockPanel.Dock="Left"
                                                                   Foreground="{DynamicResource SukiLowText}"
                                                                   Kind="VolumeLow" />
                                            <avalonia:MaterialIcon DockPanel.Dock="Right"
                                                                   Foreground="{DynamicResource SukiLowText}"
                                                                   Kind="VolumeHigh" />
                                            <Slider MinWidth="100" Value="50" />
                                        </DockPanel>

                                    </suki:GlassCard>

                                    <suki:GlassCard Padding="15" CornerRadius="15">
                                        <DockPanel>
                                            <ToggleSwitch VerticalAlignment="Center"
                                                          DockPanel.Dock="Right"
                                                          IsChecked="True" />
                                            <TextBlock Margin="0,8,0,0"
                                                       VerticalAlignment="Center"
                                                       DockPanel.Dock="Bottom"
                                                       Foreground="{DynamicResource SukiLowText}"
                                                       Text="Disable notifications." />
                                            <TextBlock VerticalAlignment="Center"
                                                       FontSize="15"
                                                       FontWeight="DemiBold"
                                                       Text="Mute" />

                                        </DockPanel>
                                    </suki:GlassCard>
                                </StackPanel>

                            </Border>
                        </Border>
                    </Panel>
                </Flyout>
            </Button.Flyout>
        </Button>
    </suki:SukiWindow.RightWindowTitleBarControls>-->

    <!--<suki:SukiWindow.MenuItems>
        <MenuItem Header="设置">
            <MenuItem Command="{Binding ToggleBaseThemeCommand}" Header="{Binding BaseTheme}">
                <MenuItem.Icon>
                    <avalonia:MaterialIcon Kind="Lightbulb" />
                </MenuItem.Icon>
            </MenuItem>
            <MenuItem Command="{Binding ToggleWindowLockCommand}"
                      Header="Window Lock"
                      ToolTip.Tip="Toggles minimizing and resizing.">
                <MenuItem.Icon>
                    <avalonia:MaterialIcon Kind="{Binding WindowLocked, Converter={x:Static converters:BoolToIconConverters.WindowLock}}" />
                </MenuItem.Icon>
            </MenuItem>
            <MenuItem Command="{Binding ToggleTitleBarCommand}"
                      Header="Title Bar"
                      ToolTip.Tip="Toggles the title bar.">
                <MenuItem.Icon>
                    <avalonia:MaterialIcon Kind="{Binding TitleBarVisible, Converter={x:Static converters:BoolToIconConverters.Visibility}}" />
                </MenuItem.Icon>
            </MenuItem>
            <MenuItem Command="{Binding ToggleTitleBackgroundCommand}" Header="Change TitleBar Background Visibility">
                <MenuItem.Icon>
                    <avalonia:MaterialIcon Kind="Visibility" />
                </MenuItem.Icon>
            </MenuItem>
    -->
    <!--<MenuItem Command="{Binding ToggleRightToLeftCommand}"
                      Header="Right To Left"
                      ToolTip.Tip="Toggles the right to left.">
                <MenuItem.Icon>
                    <avalonia:MaterialIcon Kind="SwapHorizontal" />
                </MenuItem.Icon>
            </MenuItem>-->
    <!--
            <MenuItem Header="Fullscreen"
                      PointerPressed="MakeFullScreenPressed"
                      ToolTip.Tip="Makes the app fullscreen." />
        </MenuItem>
        <MenuItem Header="样式">
            <MenuItem Click="ThemeMenuItem_OnClick"
                      Header="切换为..."
                      ItemsSource="{Binding Themes}">
                <MenuItem.DataTemplates>
                    <DataTemplate DataType="{x:Type suki:SukiColorTheme}">
                        <TextBlock Foreground="{Binding PrimaryBrush}" Text="{Binding DisplayName}" />
                    </DataTemplate>
                </MenuItem.DataTemplates>
            </MenuItem>
    -->
    <!--<MenuItem Header="-" />
            <MenuItem Command="{Binding CreateCustomThemeCommand}" Header="Create Custom" />
            <MenuItem Header="-" />
            <MenuItem Command="{Binding ShadCnModeCommand}" Header="Shadcn Mode" />-->
    <!--
        </MenuItem>
        <MenuItem Header="背景">
            <MenuItem Click="BackgroundMenuItem_OnClick"
                      Header="切换为..."
                      ItemsSource="{Binding BackgroundStyles}" />
            <MenuItem Header="-" />
            <MenuItem Command="{Binding ToggleAnimationsCommand}" Header="动画">
                <MenuItem.Icon>
                    <avalonia:MaterialIcon Kind="{Binding AnimationsEnabled, Converter={x:Static converters:BoolToIconConverters.Animation}}" />
                </MenuItem.Icon>
            </MenuItem>
            <MenuItem Command="{Binding ToggleTransitionsCommand}" Header="过渡">
                <MenuItem.Icon>
                    <avalonia:MaterialIcon Kind="{Binding TransitionsEnabled, Converter={x:Static converters:BoolToIconConverters.Animation}}" />
                </MenuItem.Icon>
            </MenuItem>
        </MenuItem>
    </suki:SukiWindow.MenuItems>-->
    <suki:SukiSideMenu IsSearchEnabled="True"
                       ItemsSource="{Binding DemoPages}"
                       SelectedItem="{Binding ActivePage}">
        <suki:SukiSideMenu.Styles>
            <Style Selector="Image.AppIcon">
                <Setter Property="Transitions">
                    <Transitions>
                        <DoubleTransition Property="Opacity" Duration="0.1" />
                    </Transitions>
                </Setter>
                <Style Selector="^:pointerover">
                    <Setter Property="Opacity" Value="0.5" />
                </Style>
            </Style>
        </suki:SukiSideMenu.Styles>
        <suki:SukiSideMenu.ItemTemplate>
            <DataTemplate>
                <suki:SukiSideMenuItem Classes="Compact" Header="{Binding DisplayName}">
                    <suki:SukiSideMenuItem.Icon>
                        <avalonia:MaterialIcon Kind="{Binding Icon}" />
                    </suki:SukiSideMenuItem.Icon>
                </suki:SukiSideMenuItem>
            </DataTemplate>
        </suki:SukiSideMenu.ItemTemplate>
        <suki:SukiSideMenu.HeaderContent>
            <Image Width="160"
                   Height="40"
                   Margin="30,10,30,30"
                   Classes="AppIcon"
                   IsVisible="{Binding $parent[suki:SukiWindow].((demo:SukiUIDemoViewModel)DataContext).TitleBarVisible}"
                   PointerPressed="InputElement_OnPointerPressed"
                   Source="Assets/OIG.N5o-removebg-preview.png"
                   ToolTip.Tip="显示/隐藏标题栏" />
        </suki:SukiSideMenu.HeaderContent>
        <!--<suki:SukiSideMenu.FooterContent>
            <StackPanel HorizontalAlignment="Center" Orientation="Horizontal">
                <StackPanel.Styles>
                    <Style Selector="Button">
                        <Setter Property="Command" Value="{Binding OpenUrlCommand}" />
                    </Style>
                    <Style Selector="avalonia|MaterialIcon">
                        <Setter Property="Width" Value="25" />
                        <Setter Property="Height" Value="25" />
                    </Style>
                </StackPanel.Styles>
                <Button Classes="Basic"
                        CommandParameter="https://github.com/kikipoulet/SukiUI"
                        ToolTip.Tip="Open On GitHub.">
                    <avalonia:MaterialIcon Kind="Github" />
                </Button>
                <Button Classes="Basic"
                        CommandParameter="https://www.nuget.org/packages/SukiUI"
                        ToolTip.Tip="View On NuGet.">
                    <avalonia:MaterialIcon Kind="Package" />
                </Button>
                <Button Classes="Basic"
                        CommandParameter="https://github.com/kikipoulet/CherylUI"
                        ToolTip.Tip="CheryUI - For Mobile">
                    <avalonia:MaterialIcon Kind="Cat" />
                </Button>
            </StackPanel>
        </suki:SukiSideMenu.FooterContent>-->
    </suki:SukiSideMenu>
</suki:SukiWindow>