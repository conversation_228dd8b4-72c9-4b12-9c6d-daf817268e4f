using System;
using System.Diagnostics;
using System.IO;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading;

namespace CrashDetectionTest.Infrastructure
{
    /// <summary>
    /// 崩溃日志记录器 - 负责记录应用程序崩溃和异常信息
    /// </summary>
    public static class CrashLogger
    {
        private static readonly object _lockObject = new object();
        private static string? _logDirectory;
        private static bool _isInitialized = false;

        /// <summary>
        /// 初始化崩溃日志记录器
        /// </summary>
        public static void Initialize()
        {
            if (_isInitialized) return;

            try
            {
                _logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "crash_logs");
                Directory.CreateDirectory(_logDirectory);

                // 清理旧日志文件（保留7天）
                CleanupOldLogs();

                // 记录应用程序启动
                LogApplicationStart();

                _isInitialized = true;
                Console.WriteLine("✓ 崩溃日志记录器初始化成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 崩溃日志记录器初始化失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录异常信息
        /// </summary>
        public static void LogException(Exception exception, string context = "")
        {
            if (!_isInitialized) Initialize();

            try
            {
                var logEntry = CreateExceptionLogEntry(exception, context);
                WriteToLog(logEntry);
                Console.WriteLine($"✓ 异常已记录: {exception.GetType().Name}");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 记录异常失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 记录进程退出信息
        /// </summary>
        public static void LogProcessExit(int exitCode)
        {
            if (!_isInitialized) return;

            try
            {
                var logEntry = CreateProcessExitLogEntry(exitCode);
                WriteToLog(logEntry);
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 记录进程退出失败: {ex.Message}");
            }
        }

        private static void LogApplicationStart()
        {
            var logEntry = CreateApplicationStartLogEntry();
            WriteToLog(logEntry);
        }

        private static string CreateApplicationStartLogEntry()
        {
            var sb = new StringBuilder();
            var process = Process.GetCurrentProcess();

            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] APPLICATION_START");
            sb.AppendLine("=== 系统信息 ===");
            sb.AppendLine($"OS: {RuntimeInformation.OSDescription}");
            sb.AppendLine($"Architecture: {RuntimeInformation.OSArchitecture}");
            sb.AppendLine($"Framework: {RuntimeInformation.FrameworkDescription}");
            sb.AppendLine($"Process ID: {process.Id}");
            sb.AppendLine($"Process Name: {process.ProcessName}");
            sb.AppendLine($"Working Set: {process.WorkingSet64 / 1024 / 1024} MB");
            sb.AppendLine($"Private Memory: {process.PrivateMemorySize64 / 1024 / 1024} MB");
            sb.AppendLine($"Virtual Memory: {process.VirtualMemorySize64 / 1024 / 1024} MB");
            sb.AppendLine($"Thread Count: {process.Threads.Count}");
            sb.AppendLine($"Start Time: {process.StartTime:yyyy-MM-dd HH:mm:ss}");
            sb.AppendLine();

            return sb.ToString();
        }

        private static string CreateExceptionLogEntry(Exception exception, string context)
        {
            var sb = new StringBuilder();
            var process = Process.GetCurrentProcess();

            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] EXCEPTION");
            sb.AppendLine($"Context: {context}");
            sb.AppendLine($"Exception Type: {exception.GetType().FullName}");
            sb.AppendLine($"Message: {exception.Message}");
            sb.AppendLine($"Thread ID: {Thread.CurrentThread.ManagedThreadId}");
            sb.AppendLine($"Working Set: {process.WorkingSet64 / 1024 / 1024} MB");
            sb.AppendLine($"Private Memory: {process.PrivateMemorySize64 / 1024 / 1024} MB");
            sb.AppendLine("Stack Trace:");
            sb.AppendLine(exception.StackTrace ?? "No stack trace available");

            if (exception.InnerException != null)
            {
                sb.AppendLine("Inner Exception:");
                sb.AppendLine($"Type: {exception.InnerException.GetType().FullName}");
                sb.AppendLine($"Message: {exception.InnerException.Message}");
                sb.AppendLine($"Stack Trace: {exception.InnerException.StackTrace}");
            }

            sb.AppendLine();
            return sb.ToString();
        }

        private static string CreateProcessExitLogEntry(int exitCode)
        {
            var sb = new StringBuilder();
            var process = Process.GetCurrentProcess();

            sb.AppendLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}] PROCESS_EXIT");
            sb.AppendLine($"Exit Code: {exitCode}");
            sb.AppendLine($"Process ID: {process.Id}");
            sb.AppendLine($"Total Runtime: {DateTime.Now - process.StartTime}");
            sb.AppendLine($"Peak Working Set: {process.PeakWorkingSet64 / 1024 / 1024} MB");
            sb.AppendLine($"Peak Virtual Memory: {process.PeakVirtualMemorySize64 / 1024 / 1024} MB");
            sb.AppendLine();

            return sb.ToString();
        }

        private static void WriteToLog(string logEntry)
        {
            if (_logDirectory == null) return;

            lock (_lockObject)
            {
                var logFileName = $"crash_{DateTime.Now:yyyyMMdd}.log";
                var logFilePath = Path.Combine(_logDirectory, logFileName);

                File.AppendAllText(logFilePath, logEntry, Encoding.UTF8);
            }
        }

        private static void CleanupOldLogs()
        {
            if (_logDirectory == null || !Directory.Exists(_logDirectory)) return;

            try
            {
                var cutoffDate = DateTime.Now.AddDays(-7);
                var logFiles = Directory.GetFiles(_logDirectory, "crash_*.log");

                foreach (var logFile in logFiles)
                {
                    var fileInfo = new FileInfo(logFile);
                    if (fileInfo.CreationTime < cutoffDate)
                    {
                        File.Delete(logFile);
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 清理旧日志失败: {ex.Message}");
            }
        }

        /// <summary>
        /// 获取最新的日志文件路径
        /// </summary>
        public static string? GetLatestLogFile()
        {
            if (_logDirectory == null || !Directory.Exists(_logDirectory)) return null;

            var logFiles = Directory.GetFiles(_logDirectory, "crash_*.log");
            if (logFiles.Length == 0) return null;

            return logFiles.OrderByDescending(f => File.GetLastWriteTime(f)).First();
        }

        /// <summary>
        /// 读取最新日志的最后几行
        /// </summary>
        public static string[] GetRecentLogLines(int lineCount = 20)
        {
            var latestLogFile = GetLatestLogFile();
            if (latestLogFile == null || !File.Exists(latestLogFile))
                return Array.Empty<string>();

            try
            {
                var lines = File.ReadAllLines(latestLogFile);
                return lines.TakeLast(lineCount).ToArray();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 读取日志失败: {ex.Message}");
                return Array.Empty<string>();
            }
        }
    }
}
