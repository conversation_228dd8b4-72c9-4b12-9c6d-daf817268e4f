﻿
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace SukiUI.Demo.Bll
{
    public static class FsqlToBDM
    {

        /// <summary>
        /// 将json字符串转换为BaseDataModel,total代表总条数，Data代表数据
        /// </summary>
        /// <param name="jsonstr"></param>
        /// <returns></returns>
        //public static List<BaseDataModel> ConvertToBaseDataModel(string jsonstr)
        //{
        //    //var data = JsonConvert.DeserializeObject(XyList);
        //    //JObject data = JObject.Parse(XyList);
        //    //var total = data["Total"];
        //    //var temp = data["Data"];
        //    // 将 JSON 数组转换为 List<Dictionary<string, object>>  
        //    List<BaseDataModel> models = new List<BaseDataModel>();
        //    if (string.IsNullOrEmpty(jsonstr))
        //    {
        //        return models;
        //    }
        //    var temp = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonstr);
        //    if (temp.TryGetValue("Data", out object value))
        //    {
        //        var list = JsonConvert.DeserializeObject<List<Dictionary<string, object>>>(value.ToString());

        //        foreach (var item in list)
        //        {
        //            //Console.WriteLine("Item:");
        //            // 输出每个字典的键值对  
        //            BaseDataModel model = new BaseDataModel();
        //            foreach (var kvp in item)
        //            {
        //                model.SetColumnValue(kvp.Key.ToLower(), kvp.Value); // 设置到 BaseDataModel
        //                //Console.WriteLine($"  Key: {kvp.Key}, Value: {kvp.Value}");
        //            }
        //            models.Add(model);
        //        }

        //    }



        //    return models;
        //}
        /// <summary>
        /// 将json字符串转换为BaseDataModel,total代表总条数，Data代表数据。
        /// 接口返回为动态数据
        /// </summary>
        /// <param name="jsonstr"></param>
        /// <returns></returns>
        public static List<BaseDataModel> ConvertToBaseDataModel(string jsonstr)
        {

            List<BaseDataModel> models = new List<BaseDataModel>();
            if (string.IsNullOrEmpty(jsonstr))
            {
                return models;
            }

            var temp = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonstr);
            if (temp.TryGetValue("Tag", out object tag))
            {
                if (tag.ToString() != "1")
                {
                    return models;
                }
            }
            if (temp.TryGetValue("Data", out object value))
            {
                var list = JsonConvert.DeserializeObject<List<JObject>>(value.ToString());

                foreach (var item in list)
                {
                    //Console.WriteLine("Item:");
                    // 输出每个字典的键值对  
                    BaseDataModel model = new BaseDataModel();
                    // 遍历 JObject  
                    foreach (var property in item.Properties())
                    {
                        model.SetColumnValue(property.Name.ToLower(), property.Value); // 设置到 BaseDataModel
                    }

                    models.Add(model);
                }

            }
            return models;
        }
        public static string ConvertToString(string jsonstr)
        {

            var models = string.Empty;
            if (string.IsNullOrEmpty(jsonstr))
            {
                return models;
            }

            var temp = JsonConvert.DeserializeObject<Dictionary<string, object>>(jsonstr);
            if (temp.TryGetValue("Tag", out object tag))
            {
                if (tag.ToString() != "1")
                {
                    return models;
                }
                if (temp.TryGetValue("Data", out object value))
                { return models = value.ToString(); }
            }

            return models;
        }

    }
}
