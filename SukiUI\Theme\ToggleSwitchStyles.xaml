﻿<Styles xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:suki="https://github.com/kikipoulet/SukiUI"
        xmlns:system="clr-namespace:System;assembly=netstandard">
    <Styles.Resources>
        <Thickness x:Key="ToggleSwitchTopHeaderMargin">0,0,0,6</Thickness>
        <GridLength x:Key="ToggleSwitchPreContentMargin">6</GridLength>
        <GridLength x:Key="ToggleSwitchPostContentMargin">6</GridLength>
        <x:Double x:Key="ToggleSwitchThemeMinWidth">0</x:Double>
    </Styles.Resources>
    <Design.PreviewWith>
        <StackPanel Width="250"
                    Margin="20"
                    Background="White"
                    Spacing="24">
            <ToggleSwitch />

        </StackPanel>
    </Design.PreviewWith>

    <Style Selector="ToggleSwitch">
        <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
        <Setter Property="RenderTransform">
            <ScaleTransform ScaleX="1.1" ScaleY="1.1" />
        </Setter>
        <Setter Property="OnContent" Value=""></Setter>
        <Setter Property="OffContent" Value=""></Setter>
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontSize" Value="{DynamicResource ControlContentThemeFontSize}" />
        <Setter Property="Template">
            <ControlTemplate>
                <DockPanel Name="DP">
                    <DockPanel.Resources>
                        <ResourceDictionary>
                            <ResourceDictionary.ThemeDictionaries>
                                <ResourceDictionary x:Key="Dark">
                                    <system:Double x:Key="ControlGlassOpacity">0.4</system:Double>
                                </ResourceDictionary>
                                </ResourceDictionary.ThemeDictionaries>
                        </ResourceDictionary>
                        
                    </DockPanel.Resources>
                    <ContentPresenter x:Name="PART_OffContentPresenter"
                                      DockPanel.Dock="Left" Margin="0,0,7,0"
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                      Content="{TemplateBinding OffContent}"
                                      ContentTemplate="{TemplateBinding OffContentTemplate}" />

                    <ContentPresenter x:Name="PART_OnContentPresenter"
                                      DockPanel.Dock="Right"
                                      HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                      VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                      Content="{TemplateBinding OnContent}"
                                      ContentTemplate="{TemplateBinding OnContentTemplate}" />
                
                <Grid Background="Transparent" RowDefinitions="Auto,*">
<Grid.Resources>
    
 
</Grid.Resources>
                    <ContentPresenter x:Name="PART_ContentPresenter"
                                      Grid.Row="0"
                                      VerticalAlignment="Top"
                                      Content="{TemplateBinding Content}"
                                      ContentTemplate="{TemplateBinding ContentTemplate}"
                                      RecognizesAccessKey="True" />

                    <Grid Grid.Row="1"
                          MinWidth="{DynamicResource ToggleSwitchThemeMinWidth}"
                          HorizontalAlignment="Left"
                          VerticalAlignment="Top">

                        <Grid.RowDefinitions>
                            <RowDefinition Height="{DynamicResource ToggleSwitchPreContentMargin}" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="{DynamicResource ToggleSwitchPostContentMargin}" />
                        </Grid.RowDefinitions>

                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="12" MaxWidth="12" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <Grid x:Name="SwitchAreaGrid"
                              Grid.RowSpan="3"
                              Grid.ColumnSpan="3"
                              Margin="0,5"
                              TemplatedControl.IsTemplateFocusTarget="True" />
                        

                        <Border 
                                Grid.Row="1"
                                Width="40"
                                Height="22"
                                Background="{DynamicResource SukiPrimaryColor0}"
                                BorderThickness="0"
                                CornerRadius="{DynamicResource MediumCornerRadius}">
                            <Border.Transitions>
                                <Transitions>
                                    <DoubleTransition Property="Opacity" Duration="0:0:0.15" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>
                        
                        
                        <suki:GlassCard 
                            Grid.Row="1"
                            Width="40" Classes="Control"
                            Height="22" IsAnimated="False"
                        
                            BorderThickness="0"
                            CornerRadius="{DynamicResource MediumCornerRadius}">
                            
                        </suki:GlassCard>

                        <Border x:Name="SwitchBackground"
                                Grid.Row="1"
                                Width="40" ClipToBounds="True"
                                Height="22"
                                Background="Transparent"
                                BorderThickness="0"
                                CornerRadius="{DynamicResource MediumCornerRadius}">
                            <Panel HorizontalAlignment="Right" Name="PanelSelected" Width="160" Margin="40,0,-160,0" >
                             <Panel.Background>
                                 <LinearGradientBrush StartPoint="0%,50%" EndPoint="100%,50%">
                                     <GradientStop Color="{DynamicResource SukiPrimaryColor0}" Offset="0.3"></GradientStop>
                                     <GradientStop Color="{DynamicResource SukiPrimaryColor}" Offset="1"></GradientStop>
                                 </LinearGradientBrush>
                             </Panel.Background>
                                <Panel.Transitions>
                                    <Transitions>
                                        <ThicknessTransition Delay="0:0:0" Property="Margin" Duration="0:0:0.7">
                                            <ThicknessTransition.Easing>
                                                    <SineEaseOut />
                                            </ThicknessTransition.Easing>
                                        </ThicknessTransition>
                                     <DoubleTransition Property="Opacity" Duration="0:0:0.5"></DoubleTransition>
                                    </Transitions>
                                </Panel.Transitions>
                            </Panel>
                            <Border.Transitions>
                                <Transitions>
                                    <DoubleTransition Property="Opacity" Duration="0:0:0.15" />
                                    <BrushTransition Property="Background" Duration="0:0:0.15" />
                                </Transitions>
                            </Border.Transitions>
                        </Border>

                        <Canvas x:Name="PART_SwitchKnob"
                                Grid.Row="1"
                                Width="22"
                                Height="22"
                                HorizontalAlignment="Left">





                            <Border x:Name="SwitchKnob"
                                    Canvas.Left="2"
                                    Canvas.Top="2"
                                    Width="18"
                                    Height="18"
                                    Margin="0,0,0,0"
                                    
                                    BoxShadow="{DynamicResource SukiSwitchShadow}"
                                    CornerRadius="{DynamicResource MediumCornerRadius}">
                                <Border />
                                <Border.Transitions>
                                    <Transitions >
                                        
                                        <DoubleTransition Property="Width" Duration="0:0:0.15">
                                            <DoubleTransition.Easing>
                                                <QuadraticEaseOut />
                                            </DoubleTransition.Easing>
                                        </DoubleTransition>
                                        <DoubleTransition Property="Canvas.Left" Duration="0:0:0.45">
                                            <DoubleTransition.Easing>
                                                <QuarticEaseOut />
                                            </DoubleTransition.Easing>
                                        </DoubleTransition>
                                    </Transitions>
                                </Border.Transitions>
                            </Border>

                            <Grid x:Name="PART_MovingKnobs"></Grid> <!-- dont remove this -->
                        </Canvas>
                    </Grid>
                </Grid>
                </DockPanel>
            </ControlTemplate>
        </Setter>
    </Style>

   
    <Style Selector="ToggleSwitch /template/ Border#SwitchKnob">

        <Setter Property="Background" Value="#fcfcfc" />
    </Style>
    <Style Selector="ToggleSwitch:unchecked /template/ Border#SwitchKnob">
        <Setter Property="Width" Value="18" />
        <Setter Property="Canvas.Left" Value="2" />

    </Style>

    <Style Selector="ToggleSwitch:checked /template/ Border#SwitchKnob">

        <Setter Property="BoxShadow" Value="0 0 0 0 White" />
        <Setter Property="Width" Value="18" />
        <Setter Property="Canvas.Left" Value="20" />
        

    </Style>

    

 


   <!-- <Style Selector="ToggleSwitch:checked /template/ Border#SwitchBackground">
        <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor}" />
    </Style> -->

   <Style Selector="ToggleSwitch:checked /template/ Panel#PanelSelected">
       <Setter Property="Margin" Value="40,0,0,0" />
   </Style>
    
</Styles>