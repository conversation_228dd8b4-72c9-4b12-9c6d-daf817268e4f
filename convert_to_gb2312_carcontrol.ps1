# Convert CarControlView.axaml.cs file to GB2312 encoding
# To fix Chinese character encoding issues

param(
    [string]$FilePath = "SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs"
)

Write-Host "Starting conversion to GB2312 encoding..." -ForegroundColor Green
Write-Host "Target file: $FilePath" -ForegroundColor Cyan

# Check if file exists
if (-not (Test-Path $FilePath)) {
    Write-Host "Error: File not found $FilePath" -ForegroundColor Red
    exit 1
}

# 备份原文件
$backupPath = "$FilePath.backup.$(Get-Date -Format 'yyyyMMdd_HHmmss')"
Write-Host "创建备份文件: $backupPath" -ForegroundColor Yellow
Copy-Item $FilePath $backupPath

try {
    # 注册编码提供程序
    [System.Text.Encoding]::RegisterProvider([System.Text.CodePagesEncodingProvider]::Instance)
    
    # 尝试以UTF-8读取文件
    Write-Host "读取文件内容..." -ForegroundColor Cyan
    $content = Get-Content $FilePath -Raw -Encoding UTF8
    
    # 验证内容中是否包含中文字符
    if ($content -match "[\u4e00-\u9fff]") {
        Write-Host "✓ 检测到中文字符，继续转换..." -ForegroundColor Green
    } else {
        Write-Host "⚠ 未检测到中文字符" -ForegroundColor Yellow
    }
    
    # 获取GB2312编码器
    $gb2312 = [System.Text.Encoding]::GetEncoding("GB2312")
    
    # 将内容转换为GB2312编码并保存
    Write-Host "转换为GB2312编码..." -ForegroundColor Cyan
    $bytes = $gb2312.GetBytes($content)
    [System.IO.File]::WriteAllBytes($FilePath, $bytes)
    
    Write-Host "✓ 成功转换为GB2312编码" -ForegroundColor Green
    
    # 验证转换
    Write-Host "验证转换结果..." -ForegroundColor Cyan
    $verifyContent = $gb2312.GetString([System.IO.File]::ReadAllBytes($FilePath))
    if ($verifyContent.Length -gt 0 -and $verifyContent -match "[\u4e00-\u9fff]") {
        Write-Host "✓ 验证成功：文件已保存为GB2312编码，中文字符正常" -ForegroundColor Green
        Write-Host "文件大小: $($bytes.Length) 字节" -ForegroundColor Cyan
    } else {
        Write-Host "⚠ 验证警告：可能存在编码问题" -ForegroundColor Yellow
    }
    
    Write-Host "`n转换完成！" -ForegroundColor Green
    Write-Host "原文件已备份为: $backupPath" -ForegroundColor Yellow
    Write-Host "如果出现问题，可以使用备份文件恢复" -ForegroundColor Yellow
    
} catch {
    Write-Host "转换失败: $($_.Exception.Message)" -ForegroundColor Red
    Write-Host "正在恢复备份文件..." -ForegroundColor Yellow
    
    try {
        Copy-Item $backupPath $FilePath -Force
        Write-Host "✓ 已恢复原文件" -ForegroundColor Green
    } catch {
        Write-Host "恢复失败: $($_.Exception.Message)" -ForegroundColor Red
    }
    
    Write-Host "`n建议手动处理：" -ForegroundColor Cyan
    Write-Host "1. 在Visual Studio中打开文件" -ForegroundColor White
    Write-Host "2. 文件 -> 高级保存选项" -ForegroundColor White
    Write-Host "3. 选择 '简体中文(GB2312) - 代码页 936'" -ForegroundColor White
    Write-Host "4. 保存文件" -ForegroundColor White
    
    exit 1
}

Write-Host "`n使用说明：" -ForegroundColor Cyan
Write-Host "- 文件已转换为GB2312编码" -ForegroundColor White
Write-Host "- 在支持GB2312的编辑器中打开应该能正确显示中文" -ForegroundColor White
Write-Host "- 如果在某些编辑器中仍显示乱码，请确认编辑器的编码设置" -ForegroundColor White
