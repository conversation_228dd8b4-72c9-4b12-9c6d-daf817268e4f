# RadioButton

A control used for collecting the user's choice.

## Base

![{3562E3E6-C5A7-4D5C-BB1A-BF95347A6AA3}](https://github.com/user-attachments/assets/3157a435-10dd-4d84-ac91-e83739023ee9)

```xml
<RadioButton Content="Option One"
             GroupName="A"
             IsChecked="True" />
```

## Chips

![{6BB65B0C-C0E8-4F6E-ADC0-EB29FFDD93DA}](https://github.com/user-attachments/assets/7d573d74-65b0-4379-a4b7-2830bfd381cf)

```xml
<RadioButton Classes="Chips" Content="Option One" IsChecked="True" />
```

## GigaChips

![{466C440E-25EE-4CF1-985F-EC8105043D22}](https://github.com/user-attachments/assets/aea0e668-c02c-432c-9f3f-e8621e726444)

```xml
<RadioButton Classes="GigaChips" Content="Option One" IsChecked="True" />
```

## See Also

[Demo: SukiUI.Demo/Features/ControlsLibrary/TogglesView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/TogglesView.axaml)
