﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SukiUI.Demo.Bll
{
    public class SiHeYiListParam
    {
        /// <summary>
        /// 搜索条件开始时间
        /// </summary>
        //public DateTime? StartTime { get; set; }

        ///// <summary>
        ///// 搜索条件结束时间
        ///// </summary>
        //public DateTime? EndTime { get; set; }
        //接口代码
        public string JKID { get; set; }
        public string DATA { get; set; }
        ////身份证号码
        //public string SFZMHM { get; set; }
        ////车型
        //public string CX { get; set; }
        ////考试项目
        //public string KSXMS { get; set; }
        ////扣分项目
        //public string KFXMS { get; set; }
        ////约考日期
        //public DateTime YKRQ { get; set; }
        ////驾校代码
        //public string JXDM { get; set; }
        ////考试员
        //public string KSY { get; set; }
        ////接口了ID
        //public string SUBID { get; set; }
        public string ExtendedParam1 { get; set; }
        public string ExtendedParam3 { get; set; }
        public string ExtendedParam2 { get; set; }
    }
}
