# Expander

`Expander` 是一个可以折叠/展开的内容容器，支持四种方向（上下左右）。

## 展示

<img src="/controls/data/expander.gif" />

## 示例

```xml
<Expander ExpandDirection="Down" Header="Down Expander">
    <TextBlock>Some Down Content</TextBlock>
</Expander>

<Expander ExpandDirection="Up" Header="Up Expander">
    <TextBlock>Some Up Content</TextBlock>
</Expander>

<Expander ExpandDirection="Right" Header="Right Expander">
    <TextBlock>Some Right Content</TextBlock>
</Expander>

<Expander ExpandDirection="Left" Header="Left Expander">
    <TextBlock>Some Left Content</TextBlock>
</Expander>
```

## 参阅

[Demo: SukiUI.Demo/Features/ControlsLibrary/ExpanderView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/ExpanderView.axaml)