﻿using Avalonia;
using ShowMeTheXaml;
using Serilog;
using System;
using System.IO;
using Avalonia.Dialogs;
using Avalonia.Media;

namespace SukiUI.Demo;

internal static class Program
{
    // Initialization code. Don't use any Avalonia, third-party APIs or any
    // SynchronizationContext-reliant code before AppMain is called: things aren't initialized
    // yet and stuff might break.
    [STAThread]
    public static void Main(string[] args)
    {
        try
        {
            // 检查是否是测试模式
            if (args.Length > 0 && args[0] == "--test-crash")
            {
                Console.WriteLine("启动崩溃检测测试模式...");

                // 初始化Serilog用于测试
                var testLogPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs", "test.txt");
                Log.Logger = new LoggerConfiguration()
                    .MinimumLevel.Information()
                    .WriteTo.Console()
                    .WriteTo.File(testLogPath, rollingInterval: RollingInterval.Day)
                    .CreateLogger();

                TestCrashDetection.RunTests();
                return;
            }

            // 确保日志目录存在
            var logsDir = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "logs");
            if (!Directory.Exists(logsDir))
            {
                Directory.CreateDirectory(logsDir);
                Console.WriteLine($"创建日志目录: {logsDir}");
            }

            // Serilog 配置
            var logPath = Path.Combine(logsDir, "Siheyilog.txt");
            Log.Logger = new LoggerConfiguration()
                .MinimumLevel.Information()
                .WriteTo.Console()
                .WriteTo.File(logPath, rollingInterval: RollingInterval.Day)
                .CreateLogger();
            Log.Information("日志配置完成，日志文件路径: {LogPath}", logPath);

            // 检查是否是日志测试模式
            if (args.Length > 0 && args[0] == "--test-logs")
            {
                Console.WriteLine("启动日志目录测试模式...");
                TestLogDirectory.TestAndShowLogDirectories();
                TestLogDirectory.CreateTestLogFiles();
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
                return;
            }
            BuildAvaloniaApp()
           .StartWithClassicDesktopLifetime(args);


        }
        catch (Exception ex)
        {
            Log.Fatal(ex, "应用程序启动失败");
            //throw;
        }
        finally
        {
            //Log.CloseAndFlush();
        }
        //BuildAvaloniaApp()

        //.StartWithClassicDesktopLifetime(args);
    }
    // Avalonia configuration, don't remove; also used by visual designer.
    public static AppBuilder BuildAvaloniaApp()
    {
       

        var app = AppBuilder.Configure<App>()
            .UsePlatformDetect()
            .WithInterFont()
            .LogToTrace()
            .UseXamlDisplay();

        if (OperatingSystem.IsWindows() || OperatingSystem.IsMacOS() || OperatingSystem.IsLinux())
        {
            app.UseManagedSystemDialogs();
        }

        return app;
    }
}