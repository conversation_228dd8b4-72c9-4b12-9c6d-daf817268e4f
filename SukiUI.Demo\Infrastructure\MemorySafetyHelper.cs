using System;
using System.Runtime.InteropServices;
using Serilog;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// 内存安全辅助工具
    /// </summary>
    public static class MemorySafetyHelper
    {
        /// <summary>
        /// 检查指针是否有效
        /// </summary>
        public static bool IsValidPointer(IntPtr ptr)
        {
            return ptr != IntPtr.Zero;
        }

        /// <summary>
        /// 安全地执行可能导致段错误的操作
        /// </summary>
        public static T SafeExecute<T>(Func<T> operation, T defaultValue = default(T), string operationName = "Unknown")
        {
            try
            {
                return operation();
            }
            catch (AccessViolationException ex)
            {
                Log.Error("Access violation in {OperationName}: {@Exception}", operationName, ex);
                CrashLogger.LogApplicationException(ex, $"Access violation in {operationName}");
                return defaultValue;
            }
            catch (SEHException ex)
            {
                Log.Error("SEH exception in {OperationName}: {@Exception}", operationName, ex);
                CrashLogger.LogApplicationException(ex, $"SEH exception in {operationName}");
                return defaultValue;
            }
            catch (Exception ex)
            {
                Log.Error("Unexpected exception in {OperationName}: {@Exception}", operationName, ex);
                CrashLogger.LogApplicationException(ex, $"Unexpected exception in {operationName}");
                return defaultValue;
            }
        }

        /// <summary>
        /// 安全地执行可能导致段错误的操作（无返回值）
        /// </summary>
        public static bool SafeExecute(Action operation, string operationName = "Unknown")
        {
            try
            {
                operation();
                return true;
            }
            catch (AccessViolationException ex)
            {
                Log.Error("Access violation in {OperationName}: {@Exception}", operationName, ex);
                CrashLogger.LogApplicationException(ex, $"Access violation in {operationName}");
                return false;
            }
            catch (SEHException ex)
            {
                Log.Error("SEH exception in {OperationName}: {@Exception}", operationName, ex);
                CrashLogger.LogApplicationException(ex, $"SEH exception in {operationName}");
                return false;
            }
            catch (Exception ex)
            {
                Log.Error("Unexpected exception in {OperationName}: {@Exception}", operationName, ex);
                CrashLogger.LogApplicationException(ex, $"Unexpected exception in {operationName}");
                return false;
            }
        }

        /// <summary>
        /// 检查对象是否为null并记录警告
        /// </summary>
        public static bool CheckNotNull<T>(T obj, string objectName) where T : class
        {
            if (obj == null)
            {
                var message = $"Null reference detected: {objectName}";
                Log.Warning(message);
                CrashLogger.LogTestEvent("NullReference", message);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 安全地释放资源
        /// </summary>
        public static void SafeDispose(IDisposable resource, string resourceName = "Unknown")
        {
            if (resource == null) return;

            try
            {
                resource.Dispose();
            }
            catch (Exception ex)
            {
                Log.Warning("Error disposing {ResourceName}: {@Exception}", resourceName, ex);
                CrashLogger.LogApplicationException(ex, $"Error disposing {resourceName}");
            }
        }

        /// <summary>
        /// 检查数组边界
        /// </summary>
        public static bool CheckArrayBounds<T>(T[] array, int index, string arrayName = "Array")
        {
            if (array == null)
            {
                Log.Warning("Array is null: {ArrayName}", arrayName);
                return false;
            }

            if (index < 0 || index >= array.Length)
            {
                Log.Warning("Array index out of bounds: {ArrayName}[{Index}], Length: {Length}", 
                    arrayName, index, array.Length);
                return false;
            }

            return true;
        }
    }
}
