﻿<Styles xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <Design.PreviewWith>
        <Border Width="400" Height="400">
            <Panel>
                <TabControl Margin="0,0,0,200">
                    <TabItem Header="Arch">
                        <Border Height="100" Background="AntiqueWhite">
                            <TextBlock FontSize="20"
                                       Foreground="Black"
                                       Text="Content" />
                        </Border>
                    </TabItem>
                    <TabItem Header="Leaf">
                        <Border Height="100" Background="Green" />
                    </TabItem>
                    <TabItem Header="Disabled" IsEnabled="False" />
                </TabControl>
            </Panel>
        </Border>
    </Design.PreviewWith>

    <Styles.Resources>
        <Thickness x:Key="TabControlTopPlacementItemMargin">0 0 0 2</Thickness>
    </Styles.Resources>

    <Style Selector="TabControl">
        <Setter Property="Margin" Value="0" />
        <Setter Property="Padding" Value="{DynamicResource TabItemMargin}" />
        <Setter Property="Background" Value="{DynamicResource TabControlBackground}" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border HorizontalAlignment="{TemplateBinding HorizontalAlignment}"
                        VerticalAlignment="{TemplateBinding VerticalAlignment}"
                        Background="{TemplateBinding Background}"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="{TemplateBinding BorderThickness}"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <DockPanel>
                        <LayoutTransformControl Name="PART_LayoutTransform" DockPanel.Dock="{TemplateBinding TabStripPlacement}">
                            <ItemsPresenter Name="PART_ItemsPresenter" ItemsPanel="{TemplateBinding ItemsPanel}" />
                        </LayoutTransformControl>
                        <ContentPresenter Name="PART_SelectedContentHost"
                                          Margin="{TemplateBinding Padding}"
                                          HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                          Content="{TemplateBinding SelectedContent}"
                                          ContentTemplate="{TemplateBinding SelectedContentTemplate}" />
                    </DockPanel>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>

    <Style Selector="TabControl[TabStripPlacement=Left] /template/ LayoutTransformControl#PART_LayoutTransform">
        <Setter Property="LayoutTransform" Value="rotate(-90deg)" />
        <Style Selector="^ ItemsPresenter#PART_ItemsPresenter &gt; WrapPanel">
            <Setter Property="Orientation" Value="Horizontal" />
        </Style>
    </Style>
    <Style Selector="TabControl[TabStripPlacement=Right] /template/ LayoutTransformControl#PART_LayoutTransform">
        <Setter Property="LayoutTransform" Value="rotate(90deg)" />
        <Style Selector="^ ItemsPresenter#PART_ItemsPresenter &gt; WrapPanel">
            <Setter Property="Orientation" Value="Horizontal" />
        </Style>
    </Style>
    <Style Selector="TabControl[TabStripPlacement=Top] /template/ ItemsPresenter#PART_ItemsPresenter">
        <Setter Property="Margin" Value="{DynamicResource TabControlTopPlacementItemMargin}" />
    </Style>
</Styles>