using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using System.Threading.Tasks;
using Material.Icons;
using SukiUI.Toasts;
using Avalonia.Controls;
using Microsoft.Extensions.DependencyInjection;
using SukiUI.Demo.Common;

namespace SukiUI.Demo.Features.Login;

public partial class LoginViewModel : DemoPageBase
{
    public Action LoginSuccessful;
    public Action LoginCancelled;
    private readonly ISukiToastManager _toastManager;
    private readonly SukiViews _serviceProvider;
    private readonly Window _loginWindow;

    [ObservableProperty] private string _username = "";
    [ObservableProperty] private string _password = "";
    [ObservableProperty] private bool _rememberMe;
    [ObservableProperty] private string _errorMessage = "";
    [ObservableProperty] private bool _hasError;

    public LoginViewModel()
        : base("登录", MaterialIconKind.Login)
    {
        //_toastManager = toastManager;
        //_serviceProvider = views;
        //_loginWindow = loginWindow;
    }

    [RelayCommand]
    private async Task Login()
    {
        // 清除之前的错误
        HasError = false;
        ErrorMessage = "";

        // 验证输入
        if (string.IsNullOrWhiteSpace(Username))
        {
            ErrorMessage = "请输入用户名";
            HasError = true;
            return;
        }

        if (string.IsNullOrWhiteSpace(Password))
        {
            ErrorMessage = "请输入密码";
            HasError = true;
            return;
        }

        // TODO: 实现实际的登录逻辑
        await Task.Delay(1000); // 模拟网络请求

        // 这里添加您的登录验证逻辑
        if (Username == "admin" && Password == "admin")
        {
            HasError = false;
            //_toastManager.CreateSimpleInfoToast()
            //    .WithTitle("登录成功")
            //    .WithContent($"欢迎回来, {Username}!")
            //    .Queue();

            // 创建并显示主窗口
            //var views = views.GetRequiredService<SukiViews>();
            //var mainWindow = _serviceProvider.CreateView<SukiUIDemoViewModel>(_serviceProvider) as Window;
            //mainWindow?.Show();
            LoginSuccessful?.Invoke();
            // 关闭登录窗口
            //_loginWindow.Close();
        }
        else
        {
            ErrorMessage = "用户名或密码错误";
            HasError = true;
        }
    }

    [RelayCommand]
    private void Cancel()
    {
        LoginCancelled?.Invoke();
    }
}