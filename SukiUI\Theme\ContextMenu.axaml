<ResourceDictionary xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="SukiContextMenuStyle" TargetType="ContextMenu">
        <Setter Property="Background" Value="{DynamicResource SukiCardBackground}" />
        <Setter Property="CornerRadius" Value="6" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiMenuBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Template">
            <ControlTemplate>
                <!--  Tiny margin helps prevent mouse-over immediately  -->
                <LayoutTransformControl Name="PART_LayoutTransform"
                                        Margin="1,0,0,0"
                                        RenderTransformOrigin="0%, 0%">
                    <Panel>
                        <Border Margin="16"
                                BoxShadow="{DynamicResource SukiPopupShadow}"
                                CornerRadius="{TemplateBinding CornerRadius}" />
                        <Border Margin="15"
                                Background="{TemplateBinding Background}"
                                BorderBrush="{TemplateBinding BorderBrush}"
                                BorderThickness="{TemplateBinding BorderThickness}"
                                ClipToBounds="True"
                                CornerRadius="{TemplateBinding CornerRadius}">
                            <Panel Background="{DynamicResource PopupGradientBrush}">
                            <ItemsPresenter Name="PART_ItemsPresenter"
                                            HorizontalAlignment="Left"
                                            VerticalAlignment="Center"
                                            ItemsPanel="{TemplateBinding ItemsPanel}"
                                            KeyboardNavigation.TabNavigation="Continue" />
                            </Panel>
                        </Border>
                    </Panel>
                </LayoutTransformControl>
            </ControlTemplate>
        </Setter>
        <Style Selector="^[IsOpen=True] /template/ LayoutTransformControl#PART_LayoutTransform">
            <Style.Animations>
                <Animation Easing="{StaticResource MenuEasing}"
                           FillMode="Forward"
                           Duration="{StaticResource ShortAnimationDuration}">
                    <KeyFrame Cue="0%">
                        <Setter Property="ScaleTransform.ScaleY" Value="0.92" />
                        <Setter Property="ScaleTransform.ScaleX" Value="0.92" />
                        <Setter Property="Opacity" Value="0" />
                    </KeyFrame>
                    <KeyFrame Cue="100%">
                        <Setter Property="ScaleTransform.ScaleY" Value="1" />
                        <Setter Property="ScaleTransform.ScaleX" Value="1" />
                        <Setter Property="Opacity" Value="1" />
                    </KeyFrame>
                </Animation>
            </Style.Animations>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type ContextMenu}"
                  BasedOn="{StaticResource SukiContextMenuStyle}"
                  TargetType="ContextMenu" />
</ResourceDictionary>
