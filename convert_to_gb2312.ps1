# PowerShell脚本：将文件转换为GB2312编码
# 适用于整个项目都使用GB2312编码的情况

param(
    [string]$FilePath = "SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs"
)

Write-Host "开始将文件转换为GB2312编码..." -ForegroundColor Green

# 检查文件是否存在
if (-not (Test-Path $FilePath)) {
    Write-Host "错误：文件不存在 $FilePath" -ForegroundColor Red
    exit 1
}

# 备份原文件
$backupPath = $FilePath + ".backup"
Copy-Item $FilePath $backupPath
Write-Host "已备份原文件到: $backupPath" -ForegroundColor Yellow

try {
    # 注册编码提供程序
    [System.Text.Encoding]::RegisterProvider([System.Text.CodePagesEncodingProvider]::Instance)
    
    # 尝试以UTF-8读取文件
    Write-Host "尝试读取文件..." -ForegroundColor Cyan
    $content = Get-Content $FilePath -Raw -Encoding UTF8
    
    # 获取GB2312编码器
    $gb2312 = [System.Text.Encoding]::GetEncoding("GB2312")
    
    # 将内容转换为GB2312编码并保存
    $bytes = $gb2312.GetBytes($content)
    [System.IO.File]::WriteAllBytes($FilePath, $bytes)
    
    Write-Host "✓ 成功转换为GB2312编码" -ForegroundColor Green
    
    # 验证转换
    $verifyContent = $gb2312.GetString([System.IO.File]::ReadAllBytes($FilePath))
    if ($verifyContent.Length -gt 0) {
        Write-Host "✓ 验证成功：文件已保存为GB2312编码" -ForegroundColor Green
    }
    
} catch {
    Write-Host "转换失败: $($_.Exception.Message)" -ForegroundColor Red
    
    # 恢复备份文件
    if (Test-Path $backupPath) {
        Copy-Item $backupPath $FilePath
        Write-Host "已恢复原文件" -ForegroundColor Yellow
    }
}

Write-Host "操作完成" -ForegroundColor Green
