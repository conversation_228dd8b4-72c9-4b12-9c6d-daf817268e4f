﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:content="clr-namespace:SukiUI.Content"
        xmlns:converters="clr-namespace:SukiUI.Converters"
        xmlns:theme="clr-namespace:SukiUI.Theme">
    <Design.PreviewWith>
        <StackPanel Width="200">
            <Border Padding="20">
                <ScrollViewer Width="200"
                              Height="100"
                              >
                    <StackPanel>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                        <ListBoxItem>isse</ListBoxItem>
                    </StackPanel>
                </ScrollViewer>
            </Border>

            <ScrollViewer HorizontalScrollBarVisibility="Visible" VerticalScrollBarVisibility="Auto">
                <StackPanel Width="500" Orientation="Horizontal">
                    <Panel Width="50"
                           Height="50"
                           Margin="5"
                           Background="Gray" />
                    <Panel Width="50"
                           Height="50"
                           Margin="5"
                           Background="Gray" />
                    <Panel Width="50"
                           Height="50"
                           Margin="5"
                           Background="Gray" />
                    <Panel Width="50"
                           Height="50"
                           Margin="5"
                           Background="Gray" />
                    <Panel Width="50"
                           Height="50"
                           Margin="5"
                           Background="Gray" />
                </StackPanel>
            </ScrollViewer>
        </StackPanel>
    </Design.PreviewWith>

    <Style Selector="ScrollBar:vertical">
<Setter Property="AllowAutoHide" Value="False"></Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="Transparent" UseLayoutRounding="False">
                    <Grid RowDefinitions="Auto,*,Auto">

                        <Track Grid.Row="1"
                               Grid.Column="1"
                               IsDirectionReversed="True"
                               Maximum="{TemplateBinding Maximum}"
                               Minimum="{TemplateBinding Minimum}"
                               Orientation="{TemplateBinding Orientation}"
                               ViewportSize="{TemplateBinding ViewportSize}"
                               Value="{TemplateBinding Value,
                                                       Mode=TwoWay}">
                            <Track.DecreaseButton>
                                <RepeatButton Name="PART_PageUpButton"
                                              Background="Transparent"
                                              Classes="repeattrack"
                                              Focusable="False" />
                            </Track.DecreaseButton>
                            <Track.IncreaseButton>
                                <RepeatButton Name="PART_PageDownButton"
                                              Background="Transparent"
                                              Classes="repeattrack"
                                              Focusable="False" />
                            </Track.IncreaseButton>
                            <Thumb Name="thumb">
                               
                                <Thumb.Template>
                                    <ControlTemplate>
                                        <Panel>
                                           
                                            <Border  Name="ThumbBarVertical" Margin="0,3" Width="3"
                                                     Background="{DynamicResource SukiMediumBorderBrush}"
                                                     CornerRadius="{DynamicResource MediumCornerRadius}" >
                                       
                                                <Border.Transitions>
                                                    <Transitions>
                                                        <BrushTransition Property="Background" Duration="0:0:0.15"></BrushTransition>
                                                        <DoubleTransition Property="Width" Duration="0:0:0.1"></DoubleTransition>
                                                    </Transitions>
                                                </Border.Transitions>
                                            </Border>
                                        </Panel>
                                    </ControlTemplate>
                                </Thumb.Template>
                            </Thumb>
                        </Track>

                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>
    
   

    <Style Selector="ScrollBar.Stack:vertical">
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="Width" Value="NaN" />
        <Setter Property="Template">
            <ControlTemplate>
                <Grid HorizontalAlignment="Stretch" RowDefinitions="Auto,*,Auto">
                    <RepeatButton Name="PART_PageUpButton"
                                  Height="60"
                                  HorizontalAlignment="Stretch"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  Focusable="False">
                        <RepeatButton.IsVisible>
                            <MultiBinding Converter="{x:Static converters:SideMenuScrollerToVisibilityBool.Up}">
                                <Binding Path="Value" RelativeSource="{RelativeSource TemplatedParent}" />
                                <Binding Path="Minimum" RelativeSource="{RelativeSource TemplatedParent}" />
                            </MultiBinding>
                        </RepeatButton.IsVisible>
                        <PathIcon Width="10"
                                  Height="25"
                                  Data="{x:Static content:Icons.ChevronUp}"
                                  Foreground="{DynamicResource SukiText}">
                            <PathIcon.RenderTransform>
                                <ScaleTransform ScaleX="1" />
                            </PathIcon.RenderTransform>
                        </PathIcon>
                    </RepeatButton>
                    <RepeatButton Name="PART_PageDownButton"
                                  Grid.Row="2"
                                  Height="60"
                                  HorizontalAlignment="Stretch"
                                  Background="Transparent"
                                  BorderThickness="0"
                                  Focusable="False">
                        <RepeatButton.IsVisible>
                            <MultiBinding Converter="{x:Static converters:SideMenuScrollerToVisibilityBool.Down}">
                                <Binding Path="Value" RelativeSource="{RelativeSource TemplatedParent}" />
                                <Binding Path="Maximum" RelativeSource="{RelativeSource TemplatedParent}" />
                            </MultiBinding>
                        </RepeatButton.IsVisible>
                        <PathIcon Width="10"
                                  Height="25"
                                  Data="{x:Static content:Icons.ChevronDown}"
                                  Foreground="{DynamicResource SukiText}">
                            <PathIcon.RenderTransform>
                                <ScaleTransform ScaleX="1" />
                            </PathIcon.RenderTransform>
                        </PathIcon>
                    </RepeatButton>
                </Grid>
            </ControlTemplate>
        </Setter>
    </Style>

    <Style Selector="ScrollBar:horizontal">
        <Setter Property="AllowAutoHide" Value="False"></Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <Border Background="Transparent" UseLayoutRounding="False">
                    <Grid >

                        <Track 
                               IsDirectionReversed="False"
                               Maximum="{TemplateBinding Maximum}"
                               Minimum="{TemplateBinding Minimum}"
                               Orientation="{TemplateBinding Orientation}"
                               ViewportSize="{TemplateBinding ViewportSize}"
                               Value="{TemplateBinding Value,
                                                       Mode=TwoWay}">
                            <Track.DecreaseButton>
                                <RepeatButton Name="PART_PageUpButton"
                                              Background="Transparent"
                                              Classes="repeattrack"
                                              Focusable="False" />
                            </Track.DecreaseButton>
                            <Track.IncreaseButton>
                                <RepeatButton Name="PART_PageDownButton"
                                              Background="Transparent"
                                              Classes="repeattrack"
                                              Focusable="False" />
                            </Track.IncreaseButton>
                            <Thumb Name="thumb">
                                <Thumb.Template>
                                    <ControlTemplate>
                                        <Panel>
                                            <Border Height="3" Name="ThumbBarHorizontal"
                                                    Margin="3,0"
                                                    Background="{DynamicResource SukiMediumBorderBrush}"
                                                    CornerRadius="10" >
                                                    <Border.Transitions>
                                                    <Transitions>
                                                        <BrushTransition Property="Background" Duration="0:0:0.15"></BrushTransition>
                                                        <DoubleTransition Property="Height" Duration="0:0:0.1"></DoubleTransition>
                                                    </Transitions>
                                                </Border.Transitions>
                                            </Border>
                                        </Panel>
                                    </ControlTemplate>
                                </Thumb.Template>
                            </Thumb>
                        </Track>
                    </Grid>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>
    
    
    <Style Selector="ScrollViewer:pointerover /template/ ScrollBar">
        <Setter Property="AllowAutoHide" Value="True"></Setter>
    </Style>
    
    <Style Selector="ScrollBar[AllowAutoHide=True] Border#ThumbBarVertical">
        <Setter Property="Background" Value="{DynamicResource SukiControlBorderBrush}"></Setter>
        <Setter Property="Width" Value="8"></Setter>
    </Style>

    
    <Style Selector="ScrollBar[AllowAutoHide=True] Border#ThumbBarHorizontal">
        <Setter Property="Background" Value="{DynamicResource SukiControlBorderBrush}"></Setter>
        <Setter Property="Height" Value="8"></Setter>
    </Style>
   
</Styles>
