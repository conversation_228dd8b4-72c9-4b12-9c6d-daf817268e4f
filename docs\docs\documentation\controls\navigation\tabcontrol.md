# Tab Control

`TabControl` provides the user with a collection of tabs that can be used to display several contents.

::: tip
`TabStripPlacement` property is available
:::

## Show

<img src="/controls/layout/tabcontrol.webp"/>

## Example

```xml
<TabControl>
    <TabItem Header="Tab 1">
        <!-- Content -->
    </TabItem>
    <TabItem Header="Tab 2">
        <!-- Content -->
    </TabItem>
</TabControl>
```

## See Also

[Demo: SukiUI.Demo/Features/ControlsLibrary/TabControl/TabControlView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/TabControl/TabControlView.axaml)