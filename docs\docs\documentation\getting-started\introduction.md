# Introduction

**SukiUI** makes your Avalonia applications more modern. The library offers a large number of animated controls and theme switches.

<div style="display: flex;justify-content: space-around;">
<img src="https://img.shields.io/github/stars/kikipoulet/SukiUI?style=for-the-badge"/>
<img src="https://img.shields.io/github/forks/kikipoulet/SukiUI?style=for-the-badge"/>
<img src="https://img.shields.io/github/commit-activity/m/kikipoulet/SukiUI
?style=for-the-badge"/>
<a href="https://www.nuget.org/packages/SukiUI"><img src="https://img.shields.io/nuget/vpre/SukiUI?style=for-the-badge" alt="Nuget Pre"/></a> 
</div>

![overview](https://github.com/user-attachments/assets/00622266-dbb8-4c05-8d1f-782483f4ca14)


## Gallery

Go to Microsoft Store to get the Gallary app

<a href="https://apps.microsoft.com/detail/9NM01BJ6JTTF?hl=en-us&gl=US">
   <img src="https://get.microsoft.com/images/en-us%20dark.svg" width="200" alt="Download SukiUI Controls Gallery" />
</a>
