﻿<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:dmc="using:Dock.Model.Controls"
                    xmlns:suki="https://github.com/kikipoulet/SukiUI"
                    x:CompileBindings="True">
  <Design.PreviewWith>
    <Border Padding="20">
      <StackPanel Spacing="20">
        <ToolTabStripItem>Leaf</ToolTabStripItem>
        <ToolTabStripItem IsSelected="True">Arch</ToolTabStripItem>
        <ToolTabStripItem Background="Yellow">Background</ToolTabStripItem>
      </StackPanel>
    </Border>
  </Design.PreviewWith>

  <x:Double x:Key="TabStripItemMinHeight">48</x:Double>
  <x:Double x:Key="TabStripItemPipeThickness">2</x:Double>

  <ContextMenu x:Key="ToolTabStripItemContextMenu" x:DataType="dmc:IToolDock" x:CompileBindings="True">
    <MenuItem Header="_Float"
              Command="{Binding Owner.Factory.FloatDockable}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanFloat}"/>
    <MenuItem Header="_Dock"
              Command="{Binding Owner.Factory.PinDockable}"
              CommandParameter="{Binding}"
              IsEnabled="{Binding OriginalOwner, Converter={x:Static ObjectConverters.IsNotNull}, FallbackValue=False}"
              IsVisible="{Binding CanPin, FallbackValue=False}"/>
    <MenuItem Header="_Auto Hide"
              Command="{Binding Owner.Factory.PinDockable}"
              CommandParameter="{Binding }"
              IsEnabled="{Binding OriginalOwner, Converter={x:Static ObjectConverters.IsNull}, FallbackValue=False}">
      <MenuItem.IsVisible>
        <MultiBinding Converter="{x:Static BoolConverters.And}">
          <Binding Path="CanPin" FallbackValue="{x:False}" />
          <Binding Path="$parent[HostWindow]" Converter="{x:Static ObjectConverters.IsNull}" />
        </MultiBinding>
      </MenuItem.IsVisible>
    </MenuItem>
    <MenuItem Header="_Close"
              Command="{Binding Owner.Factory.CloseDockable}"
              CommandParameter="{Binding}"
              IsVisible="{Binding CanClose}"/>
  </ContextMenu>
  
  <ControlTheme x:Key="{x:Type ToolTabStripItem}" TargetType="ToolTabStripItem">

    <Setter Property="(TextElement.FontSize)" Value="{DynamicResource DockFontSizeNormal}" />
    <Setter Property="FontWeight" Value="Normal" />
    <Setter Property="MinHeight" Value="0" />
    <Setter Property="VerticalContentAlignment" Value="Center" />
    <Setter Property="Background" Value="Transparent" />
    <Setter Property="Foreground" Value="{DynamicResource DockThemeForegroundBrush}" />
    <Setter Property="BorderBrush" Value="{DynamicResource DockThemeBorderLowBrush}" />
    <Setter Property="BorderThickness" Value="0 0 0 0" />
    <Setter Property="Margin" Value="0" />
    <Setter Property="Padding" Value="4 1 4 0" />

    <Setter Property="Template">
      <ControlTemplate>
        <Panel Margin="0,5" >
          <Border Name="SelectedBorder" IsVisible="False"
                  Margin="6,0"
                  Padding="0"
                 
                  CornerRadius="8">
            <suki:GlassCard Classes="Control" CornerRadius="8" >
             
            </suki:GlassCard>
          </Border>
        <Border Margin="12,4" Background="{TemplateBinding Background}"
                TextElement.FontFamily="{TemplateBinding FontFamily}"
                TextElement.FontSize="{TemplateBinding FontSize}"
                TextElement.FontWeight="{TemplateBinding FontWeight}"
                BorderBrush="{TemplateBinding BorderBrush}"
                BorderThickness="0"
                Padding="{TemplateBinding Padding}"
                ContextMenu="{DynamicResource ToolTabStripItemContextMenu}"
                x:DataType="dmc:IToolDock" x:CompileBindings="True">
            <DockableControl TrackingMode="Tab">
              <StackPanel x:Name="DragTool"
                          Background="Transparent"
                          Orientation="Horizontal"
                          Spacing="2"
                          DockProperties.IsDragArea="True"
                          DockProperties.IsDropArea="True">
                <Panel Margin="2">
                  <ContentPresenter FontWeight="DemiBold"  ContentTemplate="{Binding $parent[ToolControl].HeaderTemplate}"
                                    Content="{Binding}" />
                </Panel>
              </StackPanel>
            </DockableControl>
          </Border>
         </Panel>
      </ControlTemplate>
    </Setter>
    

    <Style Selector="^:selected /template/ Border#SelectedBorder">
      <Setter Property="IsVisible" Value="True"></Setter>
      
    </Style>


  
    
  </ControlTheme>

</ResourceDictionary>
