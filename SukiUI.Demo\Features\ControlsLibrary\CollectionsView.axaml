<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.CollectionsView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:objectModel="clr-namespace:System.Collections.ObjectModel;assembly=System.ObjectModel"
             xmlns:showMeTheXaml="clr-namespace:ShowMeTheXaml;assembly=ShowMeTheXaml.Avalonia"
             xmlns:system="clr-namespace:System;assembly=System.Runtime"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:CollectionsViewModel"
             mc:Ignorable="d">
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Collections">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        All basic collection controls have been styled and animated.
                    </TextBlock>
                    <TextBlock>
                        The DataGrid aligns it's content to the left by default, but you can apply CenterAlign and LeftAlign classes to the CellStyleClasses property of a column definition.
                    </TextBlock>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard>
                    <suki:GroupBox Header="ListBox">
                        <showMeTheXaml:XamlDisplay UniqueId="ListBox">
                            <ListBox MaxHeight="200"
                                     ItemsSource="{Binding SimpleContent}"
                                     SelectedItem="{Binding SelectedSimpleContent}" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="ComboBox">
                        <showMeTheXaml:XamlDisplay UniqueId="ComboBox">
                            <ComboBox VerticalAlignment="Top" ItemsSource="{Binding SimpleContent}" SelectedItem="{Binding SelectedSimpleContent}" />
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>

                <suki:GlassCard>
                    <suki:GroupBox Header="AutoCompleteBox">
                        <showMeTheXaml:XamlDisplay UniqueId="AutoCompleteBox">
                            <AutoCompleteBox VerticalAlignment="Top">
                                <AutoCompleteBox.ItemsSource>
                                    <objectModel:ObservableCollection x:TypeArguments="system:String">
                                        <system:String>USA 1</system:String>
                                        <system:String>USA 2</system:String>
                                        <system:String>USA 3</system:String>
                                        <system:String>France</system:String>
                                        <system:String>England</system:String>
                                        <system:String>Germany</system:String>
                                        <system:String>Belgium</system:String>
                                        <system:String>China</system:String>
                                    </objectModel:ObservableCollection>
                                </AutoCompleteBox.ItemsSource>
                            </AutoCompleteBox>
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="DataGrid">
                        <StackPanel>
                            <showMeTheXaml:XamlDisplay UniqueId="DataGrid">
                                <DataGrid  MinWidth="350"
                                          MaxHeight="200"
                                          CanUserResizeColumns="{Binding IsDataGridColumnsResizable}"
                                          ItemsSource="{Binding DataGridContent}">
                                    <DataGrid.Styles>
                                        <Style Selector="DataGridRowGroupHeader">
                                            <Setter Property="IsItemCountVisible" Value="True" />
                                        </Style>
                                    </DataGrid.Styles>
                                    <DataGrid.Columns>
                                        <DataGridTextColumn Binding="{Binding StringColumn, DataType=controlsLibrary:DataGridContentViewModel}" Header="String" />
                                        <DataGridTextColumn Binding="{Binding IntColumn, DataType=controlsLibrary:DataGridContentViewModel}" Header="Int" />
                                        <DataGridCheckBoxColumn Binding="{Binding BoolColumn, DataType=controlsLibrary:DataGridContentViewModel}" Header="Bool" />
                                        <DataGridTextColumn Binding="{Binding Group, DataType=controlsLibrary:DataGridContentViewModel}" Header="Group" />
                                    </DataGrid.Columns>
                                </DataGrid>
                            </showMeTheXaml:XamlDisplay>
                            <DockPanel Margin="20,20,0,0">
                                <ToggleSwitch DockPanel.Dock="Left" IsChecked="{Binding IsDataGridColumnsResizable}" />
                                <TextBlock Margin="5,0"
                                           VerticalAlignment="Center"
                                           Text="Resizable Columns" />
                            </DockPanel>
                        </StackPanel>
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Tree View">
                        <showMeTheXaml:XamlDisplay UniqueId="TreeView">
                            <TreeView MaxHeight="200" ItemsSource="{Binding TreeViewContent}">
                                <TreeView.ItemTemplate>
                                    <TreeDataTemplate ItemsSource="{Binding SubNodes}">
                                        <TextBlock Text="{Binding Value}" />
                                    </TreeDataTemplate>
                                </TreeView.ItemTemplate>
                            </TreeView>
                        </showMeTheXaml:XamlDisplay>
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</UserControl>
