using System;
using System.Threading;
using System.Threading.Tasks;
using CrashDetectionTest.Infrastructure;
using CrashDetectionTest.TestScenarios;

namespace CrashDetectionTest
{
    /// <summary>
    /// 崩溃检测系统测试程序
    /// </summary>
    class Program
    {
        private static ProcessMonitor? _processMonitor;

        static async Task Main(string[] args)
        {
            Console.WriteLine("=== 崩溃检测系统测试程序 ===");
            Console.WriteLine("版本: 1.0.0");
            Console.WriteLine("作者: AI Assistant");
            Console.WriteLine();

            try
            {
                // 初始化崩溃检测系统
                await InitializeCrashDetectionSystem();

                // 设置全局异常处理器
                SetupGlobalExceptionHandlers();

                // 显示主菜单
                await ShowMainMenu();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"❌ 程序启动失败: {ex.Message}");
                Console.WriteLine("按任意键退出...");
                Console.ReadKey();
            }
            finally
            {
                // 清理资源
                Cleanup();
            }
        }

        private static Task InitializeCrashDetectionSystem()
        {
            Console.WriteLine("🚀 正在初始化崩溃检测系统...");
            
            // 初始化崩溃日志记录器
            CrashLogger.Initialize();
            
            // 启动进程监控器（每5秒监控一次）
            _processMonitor = new ProcessMonitor(TimeSpan.FromSeconds(5));
            
            Console.WriteLine("✓ 崩溃检测系统初始化完成");
            Console.WriteLine();

            return Task.CompletedTask;
        }

        private static void SetupGlobalExceptionHandlers()
        {
            // 设置未处理异常处理器
            AppDomain.CurrentDomain.UnhandledException += OnUnhandledException;
            
            // 设置任务异常处理器
            TaskScheduler.UnobservedTaskException += OnUnobservedTaskException;
            
            // 设置进程退出处理器
            AppDomain.CurrentDomain.ProcessExit += OnProcessExit;
            
            Console.WriteLine("✓ 全局异常处理器设置完成");
            Console.WriteLine();
        }

        private static async Task ShowMainMenu()
        {
            while (true)
            {
                Console.WriteLine("=== 测试菜单 ===");
                Console.WriteLine("1. 基础异常测试");
                Console.WriteLine("2. 高级异常测试");
                Console.WriteLine("3. 资源监控测试");
                Console.WriteLine("4. 查看崩溃日志");
                Console.WriteLine("5. 查看进程状态");
                Console.WriteLine("6. 未处理异常测试 (⚠️ 将导致程序崩溃)");
                Console.WriteLine("0. 退出程序");
                Console.WriteLine();
                Console.Write("请选择测试类型 (0-6): ");

                var input = Console.ReadLine();
                Console.WriteLine();

                switch (input)
                {
                    case "1":
                        await RunBasicExceptionTests();
                        break;
                    case "2":
                        await RunAdvancedExceptionTests();
                        break;
                    case "3":
                        await RunResourceMonitoringTests();
                        break;
                    case "4":
                        ViewCrashLogs();
                        break;
                    case "5":
                        ViewProcessStatus();
                        break;
                    case "6":
                        CrashTestHelper.TestUnhandledException();
                        break;
                    case "0":
                        Console.WriteLine("正在退出程序...");
                        return;
                    default:
                        Console.WriteLine("❌ 无效选择，请重试");
                        break;
                }

                Console.WriteLine();
                Console.WriteLine("按任意键继续...");
                Console.ReadKey();
                Console.Clear();
            }
        }

        private static async Task RunBasicExceptionTests()
        {
            Console.WriteLine("🧪 开始基础异常测试...");
            Console.WriteLine();

            CrashTestHelper.TestNullReferenceException();
            await Task.Delay(1000);

            CrashTestHelper.TestDivideByZeroException();
            await Task.Delay(1000);

            CrashTestHelper.TestIndexOutOfRangeException();
            await Task.Delay(1000);

            Console.WriteLine();
            Console.WriteLine("✅ 基础异常测试完成");
        }

        private static async Task RunAdvancedExceptionTests()
        {
            Console.WriteLine("🧪 开始高级异常测试...");
            Console.WriteLine();

            CrashTestHelper.TestOutOfMemoryException();
            await Task.Delay(1000);

            CrashTestHelper.TestStackOverflowException();
            await Task.Delay(1000);

            await CrashTestHelper.TestMultiThreadedException();

            Console.WriteLine();
            Console.WriteLine("✅ 高级异常测试完成");
        }

        private static async Task RunResourceMonitoringTests()
        {
            Console.WriteLine("🧪 开始资源监控测试...");
            Console.WriteLine();

            Console.WriteLine("选择测试类型:");
            Console.WriteLine("1. 高内存使用测试");
            Console.WriteLine("2. 高线程数测试");
            Console.Write("请选择 (1-2): ");

            var choice = Console.ReadLine();
            Console.WriteLine();

            switch (choice)
            {
                case "1":
                    CrashTestHelper.TestHighMemoryUsage();
                    break;
                case "2":
                    await CrashTestHelper.TestHighThreadCount();
                    break;
                default:
                    Console.WriteLine("❌ 无效选择");
                    return;
            }

            Console.WriteLine();
            Console.WriteLine("✅ 资源监控测试完成");
        }

        private static void ViewCrashLogs()
        {
            Console.WriteLine("📋 查看崩溃日志...");
            Console.WriteLine();

            var recentLines = CrashLogger.GetRecentLogLines(30);
            if (recentLines.Length == 0)
            {
                Console.WriteLine("❌ 没有找到崩溃日志");
                return;
            }

            Console.WriteLine($"最近 {recentLines.Length} 行日志内容:");
            Console.WriteLine(new string('-', 80));
            foreach (var line in recentLines)
            {
                Console.WriteLine(line);
            }
            Console.WriteLine(new string('-', 80));
        }

        private static void ViewProcessStatus()
        {
            Console.WriteLine("📊 查看进程状态...");
            Console.WriteLine();

            if (_processMonitor == null)
            {
                Console.WriteLine("❌ 进程监控器未初始化");
                return;
            }

            var snapshot = _processMonitor.GetSnapshot();
            Console.WriteLine("当前进程状态:");
            Console.WriteLine($"进程ID: {snapshot.ProcessId}");
            Console.WriteLine($"进程名: {snapshot.ProcessName}");
            Console.WriteLine($"工作集: {snapshot.WorkingSet / 1024 / 1024} MB");
            Console.WriteLine($"私有内存: {snapshot.PrivateMemory / 1024 / 1024} MB");
            Console.WriteLine($"虚拟内存: {snapshot.VirtualMemory / 1024 / 1024} MB");
            Console.WriteLine($"线程数: {snapshot.ThreadCount}");
            Console.WriteLine($"CPU时间: {snapshot.TotalProcessorTime.TotalSeconds:F2} 秒");
            Console.WriteLine($"启动时间: {snapshot.StartTime:yyyy-MM-dd HH:mm:ss}");
            Console.WriteLine($"运行时间: {DateTime.Now - snapshot.StartTime}");
        }

        private static void OnUnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            Console.WriteLine();
            Console.WriteLine("🚨 检测到未处理异常!");

            if (e.ExceptionObject is Exception ex)
            {
                CrashLogger.LogException(ex, "全局未处理异常");
                Console.WriteLine($"异常类型: {ex.GetType().Name}");
                Console.WriteLine($"异常消息: {ex.Message}");
                Console.WriteLine("✓ 异常已记录到崩溃日志");
            }

            Console.WriteLine($"程序即将终止: {e.IsTerminating}");
            Console.WriteLine();

            if (e.IsTerminating)
            {
                Console.WriteLine("程序即将崩溃，正在保存最终状态...");
                Thread.Sleep(1000);
            }
        }

        private static void OnUnobservedTaskException(object? sender, UnobservedTaskExceptionEventArgs e)
        {
            Console.WriteLine();
            Console.WriteLine("🚨 检测到未观察的任务异常!");

            if (e.Exception != null)
            {
                CrashLogger.LogException(e.Exception, "未观察的任务异常");
                Console.WriteLine($"异常类型: {e.Exception.GetType().Name}");
                Console.WriteLine($"异常消息: {e.Exception.Message}");
                Console.WriteLine("✓ 异常已记录到崩溃日志");
            }

            // 标记异常为已观察，防止程序崩溃
            e.SetObserved();
            Console.WriteLine();
        }

        private static void OnProcessExit(object? sender, EventArgs e)
        {
            Console.WriteLine();
            Console.WriteLine("📝 程序正在退出，记录退出信息...");
            CrashLogger.LogProcessExit(Environment.ExitCode);
        }

        private static void Cleanup()
        {
            try
            {
                _processMonitor?.Dispose();
                Console.WriteLine("✓ 资源清理完成");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"⚠️ 资源清理异常: {ex.Message}");
            }
        }
    }
}
