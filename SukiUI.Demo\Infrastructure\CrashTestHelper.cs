using System;
using System.Threading;
using System.Threading.Tasks;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// 崩溃测试助手 - 用于测试各种崩溃场景的检测能力
    /// </summary>
    public static class CrashTestHelper
    {
        /// <summary>
        /// 测试未处理异常
        /// </summary>
        public static void TestUnhandledException()
        {
            CrashLogger.LogApplicationException(null, "Testing unhandled exception scenario");
            
            Task.Run(() =>
            {
                Thread.Sleep(1000);
                throw new InvalidOperationException("Test unhandled exception from background thread");
            });
        }

        /// <summary>
        /// 测试主线程异常
        /// </summary>
        public static void TestMainThreadException()
        {
            CrashLogger.LogApplicationException(null, "Testing main thread exception scenario");
            throw new ArgumentException("Test exception from main thread");
        }

        /// <summary>
        /// 测试内存不足
        /// </summary>
        public static void TestOutOfMemory()
        {
            CrashLogger.LogApplicationException(null, "Testing out of memory scenario");
            
            Task.Run(() =>
            {
                try
                {
                    var list = new System.Collections.Generic.List<byte[]>();
                    while (true)
                    {
                        // 每次分配100MB
                        list.Add(new byte[100 * 1024 * 1024]);
                        Thread.Sleep(100);
                    }
                }
                catch (OutOfMemoryException ex)
                {
                    CrashLogger.LogUnhandledException(ex, "OutOfMemory Test");
                    throw;
                }
            });
        }

        /// <summary>
        /// 测试栈溢出
        /// </summary>
        public static void TestStackOverflow()
        {
            CrashLogger.LogApplicationException(null, "Testing stack overflow scenario");
            
            Task.Run(() =>
            {
                try
                {
                    RecursiveMethod(0);
                }
                catch (StackOverflowException ex)
                {
                    CrashLogger.LogUnhandledException(ex, "StackOverflow Test");
                    throw;
                }
            });
        }

        private static void RecursiveMethod(int depth)
        {
            if (depth % 1000 == 0)
            {
                Console.WriteLine($"Recursion depth: {depth}");
            }
            RecursiveMethod(depth + 1);
        }

        /// <summary>
        /// 测试死锁
        /// </summary>
        public static void TestDeadlock()
        {
            CrashLogger.LogApplicationException(null, "Testing deadlock scenario");
            
            var lock1 = new object();
            var lock2 = new object();

            Task.Run(() =>
            {
                lock (lock1)
                {
                    Thread.Sleep(100);
                    lock (lock2)
                    {
                        Console.WriteLine("Thread 1 acquired both locks");
                    }
                }
            });

            Task.Run(() =>
            {
                lock (lock2)
                {
                    Thread.Sleep(100);
                    lock (lock1)
                    {
                        Console.WriteLine("Thread 2 acquired both locks");
                    }
                }
            });
        }

        /// <summary>
        /// 测试正常退出
        /// </summary>
        public static void TestNormalExit()
        {
            CrashLogger.LogApplicationException(null, "Testing normal exit scenario");
            ProcessMonitor.LogCurrentSnapshot("Normal Exit Test");
            SignalHandler.LogManualExit("Normal exit test");
            
            // 模拟正常退出
            Environment.Exit(0);
        }

        /// <summary>
        /// 测试强制终止
        /// </summary>
        public static void TestForceKill()
        {
            CrashLogger.LogApplicationException(null, "Testing force kill scenario");
            ProcessMonitor.LogCurrentSnapshot("Force Kill Test");
            
            // 模拟强制终止
            Environment.Exit(1);
        }

        /// <summary>
        /// 测试信号处理（仅Linux）
        /// </summary>
        public static void TestSignalHandling()
        {
            if (System.Runtime.InteropServices.RuntimeInformation.IsOSPlatform(System.Runtime.InteropServices.OSPlatform.Linux))
            {
                CrashLogger.LogApplicationException(null, "Testing signal handling scenario");
                
                // 发送SIGTERM信号给自己
                var process = System.Diagnostics.Process.GetCurrentProcess();
                try
                {
                    process.Kill(); // 这会发送SIGTERM
                }
                catch (Exception ex)
                {
                    CrashLogger.LogApplicationException(ex, "Failed to send signal to self");
                }
            }
            else
            {
                CrashLogger.LogApplicationException(null, "Signal testing is only available on Linux");
            }
        }

        /// <summary>
        /// 测试所有崩溃场景
        /// </summary>
        public static void RunAllTests()
        {
            CrashLogger.LogApplicationException(null, "Starting comprehensive crash test suite");
            
            Console.WriteLine("选择要测试的崩溃场景:");
            Console.WriteLine("1. 未处理异常");
            Console.WriteLine("2. 主线程异常");
            Console.WriteLine("3. 内存不足");
            Console.WriteLine("4. 栈溢出");
            Console.WriteLine("5. 死锁");
            Console.WriteLine("6. 正常退出");
            Console.WriteLine("7. 强制终止");
            Console.WriteLine("8. 信号处理 (Linux only)");
            Console.WriteLine("0. 退出");
            
            while (true)
            {
                Console.Write("请输入选择 (0-8): ");
                var input = Console.ReadLine();
                
                switch (input)
                {
                    case "1":
                        TestUnhandledException();
                        break;
                    case "2":
                        TestMainThreadException();
                        break;
                    case "3":
                        TestOutOfMemory();
                        break;
                    case "4":
                        TestStackOverflow();
                        break;
                    case "5":
                        TestDeadlock();
                        break;
                    case "6":
                        TestNormalExit();
                        break;
                    case "7":
                        TestForceKill();
                        break;
                    case "8":
                        TestSignalHandling();
                        break;
                    case "0":
                        return;
                    default:
                        Console.WriteLine("无效选择，请重试");
                        break;
                }
                
                Console.WriteLine("测试已启动，请等待结果...");
                Thread.Sleep(2000);
            }
        }

        /// <summary>
        /// 记录测试开始
        /// </summary>
        public static void LogTestStart(string testName)
        {
            var message = $"=== CRASH TEST START: {testName} ===";
            CrashLogger.LogApplicationException(null, message);
            Console.WriteLine(message);
        }

        /// <summary>
        /// 记录测试结束
        /// </summary>
        public static void LogTestEnd(string testName)
        {
            var message = $"=== CRASH TEST END: {testName} ===";
            CrashLogger.LogApplicationException(null, message);
            Console.WriteLine(message);
        }
    }
}
