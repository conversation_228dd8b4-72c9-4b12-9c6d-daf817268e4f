<ResourceDictionary xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="SukiFlyoutPresenterStyle" TargetType="FlyoutPresenter">
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="Background" Value="{DynamicResource ThemeBackgroundBrush}" />
        <Setter Property="BorderBrush" Value="{DynamicResource ThemeBorderMidBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="ScrollViewer.VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="TextElement.Foreground" Value="{DynamicResource SukiText}"/>
        <Setter Property="Cursor" Value="Arrow"/>
   <!--     <Setter Property="FlowDirection" Value="{TemplateBinding FlowDirection}"/> -->
        <Setter Property="Template">
            <ControlTemplate>
                <Border Name="LayoutRoot" 
                        Background="Transparent"
                        BorderBrush="{TemplateBinding BorderBrush}"
                        BorderThickness="0"
                        CornerRadius="{TemplateBinding CornerRadius}">
                    <Border.Transitions>
                        <Transitions>
                            <DoubleTransition Property="Opacity" Duration="0:0:0.3" />
                        </Transitions>
                    </Border.Transitions>
                    <ScrollViewer HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}" VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                        <ContentPresenter Margin="{TemplateBinding Padding}"
                                          HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                          VerticalAlignment="{TemplateBinding VerticalContentAlignment}"
                                          HorizontalContentAlignment="Stretch"
                                          VerticalContentAlignment="Stretch"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}" />
                    </ScrollViewer>
                </Border>
            </ControlTemplate>
        </Setter>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type FlyoutPresenter}"
                  BasedOn="{StaticResource SukiFlyoutPresenterStyle}"
                  TargetType="FlyoutPresenter" />
</ResourceDictionary>
