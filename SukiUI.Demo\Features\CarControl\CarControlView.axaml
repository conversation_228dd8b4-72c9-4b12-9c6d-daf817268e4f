<UserControl x:Class="SukiUI.Demo.Features.CarControl.CarControlView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:Maps="clr-namespace:Mapsui.UI.Avalonia;assembly=Mapsui.UI.Avalonia"
             xmlns:carControl="clr-namespace:SukiUI.Demo.Features.CarControl"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             d:DesignHeight="480"
             d:DesignWidth="800"
             x:DataType="carControl:CarControlViewModel"
             mc:Ignorable="d">


    <Grid HorizontalAlignment="Stretch"
          VerticalAlignment="Stretch"
          ColumnDefinitions="*,*">
        <!--  最外层分左右两列  -->
        <!--  左侧区域  -->
        <Panel Name="Panelxy" Grid.Column="0">
            <DockPanel Name="Panelxy1" Margin="5">
                <!--  左侧顶部（分左右）  -->
                <Grid ColumnDefinitions="auto,*" DockPanel.Dock="Top">
                    <StackPanel Grid.Column="0"
                                Orientation="Horizontal"
                                Spacing="5">
                        <Image Width="70"
                               Height="100"
                               Source="{Binding Gawzp}"
                               Stretch="Fill" />
                        <Image Width="70"
                               Height="100"
                               Source="{Binding Mjzp}"
                               Stretch="Fill" />
                    </StackPanel>
                    <StackPanel Grid.Column="1" Margin="5">
                        <!--<TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="姓&#160;&#160;&#160;&#160;&#160;&#160;名：" />-->
                        <TextBlock xml:space="preserve"
                                   Foreground="{DynamicResource SukiPrimaryColor}"
                                   Text="{Binding Xm, StringFormat='姓       名：{0}'}" />
                        <TextBlock xml:space="preserve"
                                   Foreground="{DynamicResource SukiPrimaryColor}"
                                   Text="{Binding Sfzmhm, StringFormat='身份证号：{0}'}" />
                        <TextBlock xml:space="preserve"
                                   Foreground="{DynamicResource SukiPrimaryColor}"
                                   Text="{Binding Kc, StringFormat='考       场：{0}'}" />
                        <TextBlock xml:space="preserve"
                                   Foreground="{DynamicResource SukiPrimaryColor}"
                                   Text="{Binding Ksy, StringFormat='考  试  员：{0}'}" />
                        <TextBlock xml:space="preserve"
                                   Foreground="{DynamicResource SukiPrimaryColor}"
                                   Text="准考证号：" />
                        <!--<Button Command="{Binding StopCommand}" Content="消息测试" />-->

                    </StackPanel>
                </Grid>

                <!--  左侧中间  -->

                <StackPanel Margin="0,0,10,0"
                            DockPanel.Dock="Top"
                            Orientation="Horizontal"
                            Spacing="10">
                    <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding Kscs, StringFormat='考试次数:{0}'}" />
                    <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding Kscj, StringFormat='考试成绩:{0}'}" />
                    <TextBlock Foreground="{DynamicResource SukiPrimaryColor}"
                               IsVisible="{Binding Iskslx}"
                               Text="{Binding Kslx, StringFormat='路线:{0}'}" />

                </StackPanel>
                <StackPanel DockPanel.Dock="Top"
                            IsVisible="{Binding Iskssj}"
                            Orientation="Horizontal"
                            Spacing="5">
                    <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding Kskssj, StringFormat='开始时间:{0}'}" />

                    <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding Kssc, StringFormat='考试时长:{0}'}" />

                    <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding Xmsc, StringFormat='项目时长:{0}'}" />

                </StackPanel>



                <!--  左侧底部  -->
                <!--<WrapPanel x:Name="PanelKsxms"
                           Margin="0"
                           DockPanel.Dock="Bottom" />-->
                <!--  考试扣分项 中间部分  ，dockPanel 默认最后一个元素的Dock为fill  -->
                <StackPanel>

                    <Grid>
                        <!--<ScrollViewer>-->
                        <DataGrid x:Name="kfGrid"
                                  Margin="0"
                                  VerticalAlignment="Top"
                                  FontSize="8"
                                  Foreground="{DynamicResource SukiPrimaryColor}"
                                  IsReadOnly="True"
                                  ItemsSource="{Binding Kfxxs}">
                            <DataGrid.Columns>
                                <DataGridTextColumn Binding="{Binding Id}" Header="代码" />
                                <DataGridTextColumn Binding="{Binding Jvalue}" Header="分值" />
                                <DataGridTextColumn Binding="{Binding Jname}" Header="扣分描述" />
                            </DataGrid.Columns>
                        </DataGrid>
                        <!--</ScrollViewer>-->
                    </Grid>
                </StackPanel>
            </DockPanel>
        </Panel>

        <!--  右侧区域  -->
        <Panel Name="PanelCar"
               Grid.Column="1"
               Height="360">
            <DockPanel Name="PanelCar1" Margin="0">

                <!--  右侧底部 考试信号  -->

                <WrapPanel x:Name="PanelKsxhs"
                           Margin="0"
                           DockPanel.Dock="Bottom">

                    <ItemsControl ItemsSource="{Binding Ksxhs}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Horizontal" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>

                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Button Width="{Binding Width}"
                                        Margin="3,2"
                                        Padding="0"
                                        Background="{Binding BackgroundColor}"
                                        Content="{Binding Name}"
                                        Foreground="{Binding ForegroundColor}" />

                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </WrapPanel>



                <!--  右侧顶部（分左右），考试项目  -->
                <WrapPanel Name="PanelExamItems" DockPanel.Dock="Right">
                    <ItemsControl ItemsSource="{Binding ExamItems}">
                        <ItemsControl.ItemsPanel>
                            <ItemsPanelTemplate>
                                <WrapPanel Orientation="Vertical" />
                            </ItemsPanelTemplate>
                        </ItemsControl.ItemsPanel>

                        <ItemsControl.ItemTemplate>
                            <DataTemplate>
                                <Button Height="{Binding Height}"
                                        Margin="2,2"
                                        Padding="0"
                                        Background="{Binding BackgroundColor}"
                                        Content="{Binding Name}"
                                        Foreground="{Binding ForegroundColor}" />
                            </DataTemplate>
                        </ItemsControl.ItemTemplate>
                    </ItemsControl>
                </WrapPanel>


                <Grid>
                    <!--  背景  -->
                    <Border CornerRadius="8">
                        <Maps:MapControl x:Name="mapControl" />

                    </Border>
                    <!--  左上  -->
                    <StackPanel Grid.Row="0"
                                Grid.Column="0"
                                Margin="5"
                                HorizontalAlignment="Left"
                                VerticalAlignment="Top">
                        <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding CarPlateAndNo}" />
                        <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding CarType, StringFormat='车型:{0}'}" />
                        <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding Chesu, StringFormat='车速:{0}'}" />
                        <TextBlock Foreground="{DynamicResource SukiPrimaryColor}" Text="{Binding Zhuansu, StringFormat='转速:{0}'}" />
                        <TextBlock x:Name="Dw"
                                   Foreground="{DynamicResource SukiPrimaryColor}"
                                   Text="{Binding Dangwei, StringFormat='档位:{0}'}" />
                    </StackPanel>



                </Grid>

            </DockPanel>
        </Panel>
    </Grid>

</UserControl>
