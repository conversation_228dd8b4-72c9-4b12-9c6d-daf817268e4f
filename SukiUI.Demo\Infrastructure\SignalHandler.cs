using System;
using System.Runtime.InteropServices;
using System.Threading;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// Linux信号处理器 - 捕获系统级别的程序终止信号
    /// </summary>
    public static class SignalHandler
    {
        private static bool _isInitialized = false;
        private static readonly object _lockObject = new object();

        // Linux信号常量
        private const int SIGTERM = 15;  // 终止信号
        private const int SIGINT = 2;    // 中断信号 (Ctrl+C)
        private const int SIGQUIT = 3;   // 退出信号
        private const int SIGKILL = 9;   // 强制终止信号 (无法捕获)
        private const int SIGSEGV = 11;  // 段错误
        private const int SIGABRT = 6;   // 异常终止
        private const int SIGFPE = 8;    // 浮点异常
        private const int SIGILL = 4;    // 非法指令

        // P/Invoke声明
        [DllImport("libc", SetLastError = true)]
        private static extern IntPtr signal(int signum, SignalHandlerDelegate handler);

        [DllImport("libc")]
        private static extern int getpid();

        // 信号处理委托
        private delegate void SignalHandlerDelegate(int signal);

        /// <summary>
        /// 初始化信号处理器
        /// </summary>
        public static void Initialize()
        {
            lock (_lockObject)
            {
                if (_isInitialized) return;

                try
                {
                    // 只在Linux上初始化信号处理
                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                    {
                        // 注册信号处理器
                        RegisterSignalHandler(SIGTERM, "SIGTERM (Termination)");
                        RegisterSignalHandler(SIGINT, "SIGINT (Interrupt/Ctrl+C)");
                        RegisterSignalHandler(SIGQUIT, "SIGQUIT (Quit)");
                        RegisterSignalHandler(SIGSEGV, "SIGSEGV (Segmentation Fault)");
                        RegisterSignalHandler(SIGABRT, "SIGABRT (Abort)");
                        RegisterSignalHandler(SIGFPE, "SIGFPE (Floating Point Exception)");
                        RegisterSignalHandler(SIGILL, "SIGILL (Illegal Instruction)");

                        _isInitialized = true;
                        CrashLogger.LogApplicationException(null, $"Signal handlers initialized for PID {getpid()}");
                    }
                }
                catch (Exception ex)
                {
                    CrashLogger.LogApplicationException(ex, "Failed to initialize signal handlers");
                }
            }
        }

        /// <summary>
        /// 注册单个信号处理器
        /// </summary>
        private static void RegisterSignalHandler(int signalNumber, string signalName)
        {
            try
            {
                var result = signal(signalNumber, OnSignalReceived);
                if (result == new IntPtr(-1))
                {
                    CrashLogger.LogApplicationException(null, $"Failed to register handler for {signalName}");
                }
            }
            catch (Exception ex)
            {
                CrashLogger.LogApplicationException(ex, $"Exception while registering {signalName} handler");
            }
        }

        /// <summary>
        /// 信号处理回调
        /// </summary>
        private static void OnSignalReceived(int signalNumber)
        {
            try
            {
                string signalName = GetSignalName(signalNumber);
                string message = $"Received signal {signalNumber} ({signalName})";

                // 记录信号信息 - 使用更安全的日志方法
                CrashLogger.LogTestEvent("LinuxSignal", $"Process terminated by signal: {message}");

                // 记录当前线程和进程信息
                var contextInfo = GetSignalContext();
                CrashLogger.LogTestEvent("SignalContext", contextInfo);

                // 对于致命信号，执行清理并退出
                if (IsFatalSignal(signalNumber))
                {
                    CrashLogger.LogProcessExit($"Fatal signal {signalNumber} ({signalName}) received");

                    // 对于SIGSEGV，记录更多调试信息
                    if (signalNumber == SIGSEGV)
                    {
                        try
                        {
                            var stackTrace = Environment.StackTrace;
                            CrashLogger.LogTestEvent("SIGSEGV_StackTrace", stackTrace);

                            // 记录当前线程信息
                            var threadInfo = $"Thread ID: {Thread.CurrentThread.ManagedThreadId}, " +
                                           $"Is Background: {Thread.CurrentThread.IsBackground}, " +
                                           $"Thread State: {Thread.CurrentThread.ThreadState}";
                            CrashLogger.LogTestEvent("SIGSEGV_ThreadInfo", threadInfo);
                        }
                        catch
                        {
                            // 忽略调试信息收集错误
                        }
                    }

                    // 给其他线程一点时间完成日志写入
                    Thread.Sleep(100);

                    // 恢复默认信号处理并重新发送信号
                    signal(signalNumber, null);

                    // 如果是可控制的信号，尝试优雅退出
                    if (signalNumber == SIGTERM || signalNumber == SIGINT || signalNumber == SIGQUIT)
                    {
                        Environment.Exit(128 + signalNumber);
                    }
                    else if (signalNumber == SIGSEGV)
                    {
                        // 对于段错误，立即退出避免进一步损坏
                        Environment.Exit(139); // 128 + 11
                    }
                }
            }
            catch (Exception ex)
            {
                // 信号处理器中不能抛出异常，使用最基本的日志记录
                try
                {
                    // 使用最简单的方式记录错误，避免复杂的日志操作
                    var errorMsg = $"Signal handler error: {ex.Message}";
                    Console.Error.WriteLine($"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {errorMsg}");

                    // 尝试写入文件（如果可能）
                    try
                    {
                        var logPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "signal_error.log");
                        File.AppendAllText(logPath, $"[{DateTime.Now:yyyy-MM-dd HH:mm:ss}] {errorMsg}\n");
                    }
                    catch
                    {
                        // 忽略文件写入错误
                    }
                    CrashLogger.LogApplicationException(null, $"Exception in signal handler for signal {signalNumber}");
                }
                catch
                {
                    // 最后的防线，什么都不做
                }
            }
        }

        /// <summary>
        /// 获取信号名称
        /// </summary>
        private static string GetSignalName(int signal)
        {
            return signal switch
            {
                SIGTERM => "SIGTERM",
                SIGINT => "SIGINT",
                SIGQUIT => "SIGQUIT",
                SIGSEGV => "SIGSEGV",
                SIGABRT => "SIGABRT",
                SIGFPE => "SIGFPE",
                SIGILL => "SIGILL",
                _ => $"UNKNOWN({signal})"
            };
        }

        /// <summary>
        /// 判断是否为致命信号
        /// </summary>
        private static bool IsFatalSignal(int signal)
        {
            return signal switch
            {
                SIGSEGV or SIGABRT or SIGFPE or SIGILL => true,
                SIGTERM or SIGINT or SIGQUIT => true,
                _ => false
            };
        }

        /// <summary>
        /// 获取信号上下文信息
        /// </summary>
        private static string GetSignalContext()
        {
            try
            {
                return $"PID: {getpid()}, " +
                       $"Thread: {Thread.CurrentThread.ManagedThreadId}, " +
                       $"IsBackground: {Thread.CurrentThread.IsBackground}, " +
                       $"ThreadState: {Thread.CurrentThread.ThreadState}, " +
                       $"Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss.fff}";
            }
            catch
            {
                return "Unable to get signal context";
            }
        }

        /// <summary>
        /// 手动触发进程退出记录（用于测试）
        /// </summary>
        public static void LogManualExit(string reason)
        {
            if (_isInitialized)
            {
                CrashLogger.LogProcessExit($"Manual exit: {reason}");
            }
        }
    }
}
