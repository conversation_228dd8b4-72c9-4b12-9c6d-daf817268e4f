﻿using Avalonia.Metadata;

[assembly: XmlnsDefinition("https://github.com/kikipoulet/SukiUI", "SukiUI")]
[assembly: XmlnsDefinition("https://github.com/kikipoulet/SukiUI", "SukiUI.Controls")]
[assembly: XmlnsDefinition("https://github.com/kikipoulet/SukiUI", "SukiUI.Converters")]
[assembly: XmlnsDefinition("https://github.com/kikipoulet/SukiUI", "SukiUI.Theme")]
[assembly: XmlnsDefinition("https://github.com/kikipoulet/SukiUI", "SukiUI.Models")]
[assembly: XmlnsDefinition("https://github.com/kikipoulet/SukiUI", "SukiUI.Content")]
[assembly: XmlnsPrefix("https://github.com/kikipoulet/SukiUI", "suki")]
