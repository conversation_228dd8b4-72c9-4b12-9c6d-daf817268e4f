﻿<Styles xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:suki="https://github.com/kikipoulet/SukiUI"
        xmlns:system="clr-namespace:System;assembly=netstandard">

    <Design.PreviewWith>
        <Panel Background="Red">
            <ProgressBar Width="100"
                         Height="6"
                         Value="50" />
        </Panel>
    </Design.PreviewWith>

    <Style Selector="ProgressBar">
        <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{DynamicResource SukiBorderBrush}" />
        <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />

        <Setter Property="Template">
            <ControlTemplate>
                <DockPanel Name="PART_Dock">
<DockPanel.Resources>
   
</DockPanel.Resources>
                    <TextBlock Name="PART_Text"
                               Width="0"
                               Height="0"
                               Margin="0,0,0,1"
                               HorizontalAlignment="Right"
                               VerticalAlignment="Center"
                               DockPanel.Dock="Right"
                               FontWeight="{DynamicResource DefaultDemiBold}"
                               Foreground="{DynamicResource SukiText}"
                               Text="{Binding Value, RelativeSource={RelativeSource TemplatedParent}, StringFormat={}{0:0}%}"
                               TextAlignment="Right" />

                    <suki:GlassCard Name="PART_RootBorder"
                            MinWidth="{TemplateBinding MinWidth}"
                          Padding="0" Classes="Control"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                            ClipToBounds="True"
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <Panel>
                            <Border Name="PART_Indicator"
                                    Width="8"
                                    Height="8"
                                    Background="{TemplateBinding Foreground}"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    IsVisible="{Binding !IsIndeterminate, RelativeSource={RelativeSource TemplatedParent}}">

                                <Border.Transitions>
                                    <Transitions>
                                        <DoubleTransition Easing="CircularEaseOut"
                                                          Property="Width"
                                                          Duration="0:0:0.6" />
                                        <DoubleTransition Easing="CircularEaseOut"
                                                          Property="Height"
                                                          Duration="0:0:0.6" />
                                        <BrushTransition Property="Background" Duration="0:0:0.5" />
                                    </Transitions>
                                </Border.Transitions>
                            </Border>
                            <Border Name="PART_IndeterminateIndicator"
                                    Height="8"
                                    Background="{TemplateBinding Foreground}"
                                    CornerRadius="{TemplateBinding CornerRadius}"
                                    IsVisible="{Binding IsIndeterminate, RelativeSource={RelativeSource TemplatedParent}}" />
                        </Panel>
                    </suki:GlassCard>


                </DockPanel>
            </ControlTemplate>
        </Setter>
		
	    <Style Selector="^[FlowDirection=RightToLeft] /template/ TextBlock#PART_Text">
			<Setter Property="TextAlignment" Value="Left"/>
	    </Style>    
    </Style>

    <Style Selector="ProgressBar:horizontal /template/ Border#PART_Indicator">
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Stretch" />
    </Style>
    <Style Selector="ProgressBar:vertical /template/ DockPanel#PART_Dock">
        <Setter Property="Width" Value="35" />
    </Style>
    <Style Selector="ProgressBar:vertical /template/ Border#PART_Indicator">
        <Setter Property="HorizontalAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Bottom" />
    </Style>

    <Style Selector="ProgressBar:horizontal">
        <Setter Property="MinWidth" Value="200" />

    </Style>

    <Style Selector="ProgressBar:vertical">

        <Setter Property="MinHeight" Value="200" />
    </Style>

    <Style Selector="ProgressBar:horizontal /template/ TextBlock#PART_Text">
        <Setter Property="DockPanel.Dock" Value="Right" />
        <Setter Property="Height" Value="15" />
    </Style>

    <Style Selector="ProgressBar:vertical /template/ TextBlock#PART_Text">
        <Setter Property="DockPanel.Dock" Value="Bottom" />
        <Setter Property="Margin" Value="10,10,0,0" />
        <Setter Property="TextAlignment" Value="Center" />
        <Setter Property="Width" Value="35" />
    </Style>

    <Style Selector="ProgressBar:horizontal[ShowProgressText=True] /template/ TextBlock#PART_Text">
        <Style.Animations>
            <Animation FillMode="Forward" Duration="0:0:0.35">
                <KeyFrame Cue="0%">
                    <Setter Property="Width" Value="0" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="Width" Value="40" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
    </Style>

    <Style Selector="ProgressBar:horizontal[ShowProgressText=False] /template/ TextBlock#PART_Text">
        <Style.Animations>
            <Animation FillMode="Forward" Duration="0:0:0.35">
                <KeyFrame Cue="0%">
                    <Setter Property="Width" Value="40" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="Width" Value="0" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
    </Style>

    <Style Selector="ProgressBar:vertical[ShowProgressText=True] /template/ TextBlock#PART_Text">
        <Style.Animations>
            <Animation FillMode="Forward" Duration="0:0:0.35">
                <KeyFrame Cue="0%">
                    <Setter Property="Height" Value="0" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="Height" Value="35" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
    </Style>

    <Style Selector="ProgressBar:vertical[ShowProgressText=False] /template/ TextBlock#PART_Text">
        <Style.Animations>
            <Animation FillMode="Forward" Duration="0:0:0.35">
                <KeyFrame Cue="0%">
                    <Setter Property="Height" Value="35" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="Height" Value="0" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
    </Style>

    <Style Selector="ProgressBar:horizontal /template/ suki|GlassCard#PART_RootBorder">
        <Setter Property="MinHeight" Value="8" />
        <Setter Property="Height" Value="8" />
        <Setter Property="MaxHeight" Value="8" />
    </Style>

    <Style Selector="ProgressBar:vertical /template/ suki|GlassCard#PART_RootBorder">
        <Setter Property="MinWidth" Value="8" />
        <Setter Property="Width" Value="8" />
        <Setter Property="MaxWidth" Value="8" />
    </Style>

    <Style Selector="ProgressBar:vertical /template/ LayoutTransformControl#PART_LayoutTransformControl">
        <Setter Property="LayoutTransform">
            <Setter.Value>
                <RotateTransform Angle="90" />
            </Setter.Value>
        </Setter>
    </Style>

    <Style Selector="ProgressBar:horizontal:indeterminate /template/ Border#PART_IndeterminateIndicator">
        <Style.Animations>
            <Animation Easing="LinearEasing"
                       IterationCount="Infinite"
                       Duration="0:0:2">
                <KeyFrame Cue="0%">
                    <Setter Property="TranslateTransform.X"
                            Value="{Binding IndeterminateStartingOffset, RelativeSource={RelativeSource TemplatedParent}}" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="TranslateTransform.X"
                            Value="{Binding IndeterminateEndingOffset, RelativeSource={RelativeSource TemplatedParent}}" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
        <Setter Property="Width"
                Value="{Binding TemplateProperties.ContainerWidth, RelativeSource={RelativeSource TemplatedParent}}" />
    </Style>

    <Style Selector="ProgressBar:vertical:indeterminate /template/ Border#PART_IndeterminateIndicator">
        <Style.Animations>
            <Animation Easing="LinearEasing"
                       IterationCount="Infinite"
                       Duration="0:0:2">
                <KeyFrame Cue="0%">
                    <Setter Property="TranslateTransform.Y"
                            Value="{Binding IndeterminateStartingOffset, RelativeSource={RelativeSource TemplatedParent}}" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="TranslateTransform.Y"
                            Value="{Binding IndeterminateEndingOffset, RelativeSource={RelativeSource TemplatedParent}}" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
        <Setter Property="Height"
                Value="{Binding TemplateProperties.ContainerWidth, RelativeSource={RelativeSource TemplatedParent}}" />
    </Style>

    <Style Selector="ProgressBar.Accent">
        <Setter Property="Foreground" Value="{DynamicResource SukiAccentColor}" />
    </Style>

    <Style Selector="ProgressBar.Light">
        <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor75}" />
    </Style>

</Styles>