﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;


//using System.Drawing;

using Mapsui;
using NetTopologySuite.Geometries;
using SukiUI.Demo.Bll;
using Avalonia.Media;
using Mapsui.Layers;
using Mapsui.Styles;
using Avalonia.Controls.Shapes;
using Color = Mapsui.Styles.Color;
using Mapsui.Nts;


namespace SukiUI.Demo.Bll
{


    public class SharpMapHelper
    {

        private const string XlsConnectionString = "Provider={2};Data Source={0}\\{1};Extended Properties=\"Excel 8.0;HDR=Yes;IMEX=1\"";

        public static Map InitializeMap(MapType tt, float angle)
        {
            Map map = null;
            switch (tt)
            {
                case MapType.RunLine:
                    map = InitializeMapOsmWithXls(angle);
                    break;
                case MapType.MapInfo:
                    map = InitializeMapinfo(angle);
                    break;
                case MapType.ShapeFile:
                    map = InitializeMapOrig(angle);
                    break;
                case MapType.Static:
                    map = InitializeMapOsmWithXls2(angle);
                    break;
                case MapType.Imag:
                    map = InitializeWithImg(angle);
                    break;
                default:
                    map = InitializeMapOsmWithXls(angle);
                    break;
            }
            return map;
        }

       

        public static Map InitializeWithImg(float angle)
        {
            Mapsui.Map map = new Map();
            // 创建一个新的图层对象BackgroundLayer
            // 创建一个新的图层对象 GdiImageLayer
            //var backgroundLayer = new SharpMap.Layers.GdiImageLayer("Background", "BackGround.jpg");


            // 设置 GdiImageLayer 的 Transform 属性，使其填满整个 MapBox
            //外圈 于端点  X = -103.1232  Y = 104.9268  Z = 0.0000
            //于端点 X = 87.5268  Y = 104.9268  Z = 0.0000
            //于端点 X = 87.5268  Y = -65.0948  Z = 0.0000
            //于端点 X = -103.1232  Y = -65.0948  Z = 0.0000
            var listCoord = new List<Coordinate>();
            listCoord.Add(new Coordinate(-103.1232, 104.9268));
            listCoord.Add(new Coordinate(87.5268, 104.9268));
            listCoord.Add(new Coordinate(87.5268, -65.0948));
            listCoord.Add(new Coordinate(-103.1232, -65.0948));
            listCoord.Add(new Coordinate(-103.1232, 104.9268));

            
            return map;



        }

        /// <summary>
        /// MapInfo格式的地图文件
        /// </summary>
        /// <param name="angle"></param>
        /// <returns></returns>
        private static Map InitializeMapinfo(float angle)
        {
          
            Map map = new Map();

        
           



         
            return map;
        }

        /// <summary>
        /// ShapeFile
        /// </summary>
        /// <param name="angle"></param>
        /// <returns></returns>
        private static Map InitializeMapOrig(float angle)
        {
            //Initialize a new map of size 'imagesize'
            Map map = new Map();

          
            return map;
        }

        public static Map InitializeMapXyz(List<Sensor> data, float angle)
        {
            Map map = new Map();       
            map.BackColor = Mapsui.Styles.Color.FromArgb(255,88, 97, 117);
            foreach (var item in data)
            {
                try
                {
                  
                  
                    var list = new List<Coordinate>();
                   
                    foreach (var pF in item.Data)
                    {
                        if (item.Data.Count < 3)
                        {
                            var name = item.Name;
                        }
                        var pd = new Coordinate(pF.X, pF.Y);
                        list.Add(pd);
                    }
                   
                    var linestring = new LineString(list.ToArray());
                    var layer = new MemoryLayer(item.Name);
                    //实线
                    if (item.Name.Contains("-sx-"))
                    {
                       
                      
                        var style =new VectorStyle();
                        style.Fill = null;
                        style.Line.Color=Color.Yellow;
                        style.Line.Width = 1;
                        style.Line.PenStyle=PenStyle.Solid;
                        layer.Style = style;
                    }
                    //虚线
                    else if (item.Name.Contains("-xx-"))
                    {
                        var style = new VectorStyle();
                        style.Fill = null;
                        style.Line.Color = Color.Yellow;
                        style.Line.Width = 1;
                        style.Line.PenStyle = PenStyle.LongDash;
                        layer.Style = style;
                    }
                    //区域
                    else
                    {

                        //NetTopologySuite.Geometries.GeometryFactory geometryFactory = new NetTopologySuite.Geometries.GeometryFactory();
                        //GeometryProvider geometryProvider = new GeometryProvider(geometryFactory.CreatePolygon(list.ToArray()));
                        //layer.DataSource = geometryProvider;
                        //layer.Style.Fill = new SolidBrush(Color.FromArgb(19, 26, 74));
                        //layer.Style.Outline = Pens.Yellow;
                    }


                  
                   layer.Features= new List<IFeature>() { new GeometryFeature(linestring) };
                   map.Layers.Add(layer);
                }
                catch (Exception ex)
                {
                    var name = item.Name;
                    continue;
                }


            }
            map.Navigator.CenterOnAndZoomTo(map.Extent.Centroid,0.1);
          
            return map;
        }

        /// <summary>
        /// 在线显示，圆点显示轨迹
        /// </summary>
        /// <param name="angle"></param>
        /// <returns></returns>
        private static Map InitializeMapOsmWithXls(float angle)
        {
            var map = new Map();

            //var tileLayer = new TileAsyncLayer(
            //    KnownTileSources.Create(KnownTileSource.OpenStreetMap), "TileLayer - OSM with XLS");
            //tileLayer.SRID = 4326;
            //map.BackgroundLayer.Add(tileLayer);

            ////Get data from excel
            //var xlsPath = string.Format(XlsConnectionString, System.IO.Directory.GetCurrentDirectory(), "GeoData\\Cities.xls", Properties.Settings.Default.OleDbProvider);
            //var ds = GetDataFromExcel(xlsPath, "Cities");
            //var ds1 = GetDataFromExcel(xlsPath, "Cities2");
            //var ct = GetCoordinateTransformation();
            //TransCoordinate(ds, ct);
            //TransCoordinate(ds1, ct);
            //string columeName = "Rotation";
            ////Add Rotation Column
            //AddColumeToDataSet(ds, columeName, -angle);
            //AddColumeToDataSet(ds1, columeName, -angle);

            //var xlsLayer = GetLayerFromDataSet(ds, Color.GreenYellow);//Set up provider
            //map.Layers.Add(xlsLayer); //Add layer to map

            //var xlsLayer1 = GetLayerFromDataSet(ds1, Color.Red);
            //map.Layers.Add(xlsLayer1);

            //var xlsLabelLayer = GetLabelLayerByVectorLayer(xlsLayer, "XLSLabel");

            //xlsLabelLayer.Theme = new SharpMap.Rendering.Thematics.FontSizeTheme(xlsLabelLayer, map) { FontSizeScale = 1000f };
            //map.Layers.Add(xlsLabelLayer);
            //map.ZoomToBox(xlsLayer.Envelope.ExpandedBy(xlsLayer1.Envelope));
            return map;
        }

        /// <summary>
        /// 在线显示，图标显示轨迹
        /// </summary>
        /// <param name="angle"></param>
        /// <returns></returns>
        private static Map InitializeMapOsmWithXls2(float angle)
        {
            var map = new Map();

            //var tileLayer = new TileAsyncLayer(
            //    KnownTileSources.Create(KnownTileSource.OpenStreetMap), "TileLayer - OSM with XLS");
            //tileLayer.SRID = 4326;
            //map.BackgroundLayer.Add(tileLayer);

            ////Get data from excel
            //var xlsPath = string.Format(XlsConnectionString, System.IO.Directory.GetCurrentDirectory(), "GeoData\\Henan.xls", Properties.Settings.Default.OleDbProvider);
            //var ds = GetDataFromExcel(xlsPath, "Cities");
            //var ct = GetCoordinateTransformation();

            //TransCoordinate(ds, ct);
            //string columeName = "Rotation";
            ////Add Rotation Column
            //AddColumeToDataSet(ds, columeName, -angle);

            //var xlsLayer = GetLayerFromDataSet2(ds, Color.GreenYellow);//Set up provider
            //map.Layers.Add(xlsLayer); //Add layer to map

            //var xlsLabelLayer = GetLabelLayerByVectorLayer(xlsLayer, "XLSLabel");

            //xlsLabelLayer.Theme = new FontSizeTheme(xlsLabelLayer, map) { FontSizeScale = 1000f };
            //map.Layers.Add(xlsLabelLayer);
            //map.ZoomToBox(xlsLayer.Envelope);
            return map;
        }

        /// <summary>
        /// 从Excel中读取数据
        /// </summary>
   

        /// <summary>
        /// 获取坐标转换对象
        /// </summary>
        /// <returns></returns>
        //private static ICoordinateTransformation GetCoordinateTransformation()
        //{
            ////The SRS for this datasource is EPSG:4326, therefore we need to transfrom it to OSM projection
            //var ctf = new CoordinateTransformationFactory();
            //var cf = new CoordinateSystemFactory();
            //var epsg4326 = cf.CreateFromWkt("GEOGCS[\"WGS 84\",DATUM[\"WGS_1984\",SPHEROID[\"WGS 84\",6378137,298.257223563,AUTHORITY[\"EPSG\",\"7030\"]],AUTHORITY[\"EPSG\",\"6326\"]],PRIMEM[\"Greenwich\",0,AUTHORITY[\"EPSG\",\"8901\"]],UNIT[\"degree\",0.01745329251994328,AUTHORITY[\"EPSG\",\"9122\"]],AUTHORITY[\"EPSG\",\"4326\"]]");
            //var epsg3857 = cf.CreateFromWkt("PROJCS[\"Popular Visualisation CRS / Mercator\", GEOGCS[\"Popular Visualisation CRS\", DATUM[\"Popular Visualisation Datum\", SPHEROID[\"Popular Visualisation Sphere\", 6378137, 0, AUTHORITY[\"EPSG\",\"7059\"]], TOWGS84[0, 0, 0, 0, 0, 0, 0], AUTHORITY[\"EPSG\",\"6055\"]],PRIMEM[\"Greenwich\", 0, AUTHORITY[\"EPSG\", \"8901\"]], UNIT[\"degree\", 0.0174532925199433, AUTHORITY[\"EPSG\", \"9102\"]], AXIS[\"E\", EAST], AXIS[\"N\", NORTH], AUTHORITY[\"EPSG\",\"4055\"]], PROJECTION[\"Mercator\"], PARAMETER[\"False_Easting\", 0], PARAMETER[\"False_Northing\", 0], PARAMETER[\"Central_Meridian\", 0], PARAMETER[\"Latitude_of_origin\", 0], UNIT[\"metre\", 1, AUTHORITY[\"EPSG\", \"9001\"]], AXIS[\"East\", EAST], AXIS[\"North\", NORTH], AUTHORITY[\"EPSG\",\"3857\"]]");
            //var ct = ctf.CreateFromCoordinateSystems(epsg4326, epsg3857);
            //return ct;
        //}

        /// <summary>
        /// 转换地球经纬度到坐标
        /// </summary>
        /// <param name="ds"></param>
        /// <param name="ct"></param>
        //private static void TransCoordinate(DataSet ds, ICoordinateTransformation ct)
        //{
        //    foreach (System.Data.DataRow row in ds.Tables[0].Rows)
        //    {
        //        if (row["X"] == DBNull.Value || row["Y"] == DBNull.Value) continue;
        //        var coords = new[] { Convert.ToDouble(row["X"]), Convert.ToDouble(row["Y"]) };
        //        coords = ct.MathTransform.Transform(coords);
        //        row["X"] = coords[0];
        //        row["Y"] = coords[1];
        //    }
        //}

        /// <summary>
        /// 增加列
        /// </summary>
        /// <param name="ds"></param>
        /// <param name="columeName"></param>
        /// <param name="columeValue"></param>
        //private static void AddColumeToDataSet(DataSet ds, string columeName, float columeValue)
        //{
        //    ds.Tables[0].Columns.Add(columeName, typeof(float));
        //    foreach (System.Data.DataRow row in ds.Tables[0].Rows)
        //    {
        //        row["Rotation"] = -columeValue;
        //    }
        //}

     
       

       
    }

    public enum MapType
    {
        ShapeFile = 0,
        MapInfo = 1,
        RunLine = 2,//运行轨迹
        Static = 3, //定点数据
        Xyz = 4,//特定格式
        Imag=5//图片格式


    }
    public class ComData
    {
        //$GPHPD,1716,288945.600,175.15,-0.18,0.66,40.7924129,111.6924433,1046.506,-6566.873,7641.869,-19.335,-0.004,0.003,-0.004,0.020,-0.007,-0.001,3.322,15,16,4*6C
        //$GPHPD,，（gps周），gps秒，航向，俯仰，横滚，维度，经度，高度，东向距离，北向距离，天向距离，东向速度，北向速度，天向速度，东向速度差，北向速度差，天向速度差，两天线距离，前天线星数量，后天线星数量，差分状态（4可用，其它数字不可用）
        //$cmd,setbasestation,34.41563034,114.1479767,72.69931298*ff
        //$cmd,getbasestation*ff
        //$cmd,save,config*ff

        ///$GPHPD
        private double _gphpd;

        public double Gphpd
        {
            get { return _gphpd; }
            set { _gphpd = value; }
        }
        ///（gps周）
        private double _gpsPerimeter;

        public double GpsPerimeter
        {
            get { return _gpsPerimeter; }
            set { _gpsPerimeter = value; }
        }
        ///gps秒
        private double _gpsSecond;

        public double GpsSecond
        {
            get { return _gpsSecond; }
            set { _gpsSecond = value; }
        }
        ///航向
        private double _course;

        public double Course
        {
            get { return _course; }
            set { _course = value; }
        }
        ///俯仰
        private double _pitch;

        public double Pitch
        {
            get { return _pitch; }
            set { _pitch = value; }
        }
        ///横滚
        private double _roll;

        public double Roll
        {
            get { return _roll; }
            set { _roll = value; }
        }
        ///维度
        private double _lat;

        public double Lat
        {
            get { return _lat; }
            set { _lat = value; }
        }
        ///经度
        private double _longitude;

        public double Longitude
        {
            get { return _longitude; }
            set { _longitude = value; }
        }
        ///高度
        private double _height;

        public double Height
        {
            get { return _height; }
            set { _height = value; }
        }
        ///东向距离
        private double _eastDistance;

        public double EastDistance
        {
            get { return _eastDistance; }
            set { _eastDistance = value; }
        }
        ///北向距离
        private double _northDistance;

        public double NorthDistance
        {
            get { return _northDistance; }
            set { _northDistance = value; }
        }
        ///天向距离
        private double _skyDistance;

        public double SkyDistance
        {
            get { return _skyDistance; }
            set { _skyDistance = value; }
        }
        ///东向速度
        private double _eastSpeed;

        public double EastSpeed
        {
            get { return _eastSpeed; }
            set { _eastSpeed = value; }
        }
        ///北向速度
        private double _northSpeed;

        public double NorthSpeed
        {
            get { return _northSpeed; }
            set { _northSpeed = value; }
        }
        ///天向速度
        private double _skySpeed;

        public double SkySpeed
        {
            get { return _skySpeed; }
            set { _skySpeed = value; }
        }
        ///东向速度差
        private double _eastSpeedD;

        public double EastSpeedD
        {
            get { return _eastSpeedD; }
            set { _eastSpeedD = value; }
        }
        ///北向速度差
        private double _northSpeedD;

        public double NorthSpeedD
        {
            get { return _northSpeedD; }
            set { _northSpeedD = value; }
        }
        ///天向速度差
        private double _skySpeedD;

        public double SkySpeedD
        {
            get { return _skySpeedD; }
            set { _skySpeedD = value; }
        }
        ///两天线距离
        private double _antennaDistance;

        public double AntennaDistance
        {
            get { return _antennaDistance; }
            set { _antennaDistance = value; }
        }
        ///前天线星数量
        private double _agoAntennaCount;

        public double AgoAntennaCount
        {
            get { return _agoAntennaCount; }
            set { _agoAntennaCount = value; }
        }
        ///后天线星数量
        private double _afterAntennaCount;

        public double AfterAntennaCount
        {
            get { return _afterAntennaCount; }
            set { _afterAntennaCount = value; }
        }
        ///差分状态（4可用，其它数字不可用）
        private int _dState;

        public int DState
        {
            get { return _dState; }
            set { _dState = value; }
        }
        ///com数据
        private string _valueStr;

        public string ValueStr
        {
            get { return _valueStr; }
            set { _valueStr = value; }
        }

    }

    public class carModel
    {
        /// <summary>
        /// 车型
        /// </summary>
        private string _cx;

        public string Cx
        {
            get { return _cx; }
            set { _cx = value; }
        }
        /// <summary>
        /// 后天线坐标
        /// </summary>
        private double _htx_x;

        public double Htx_x
        {
            get { return _htx_x; }
            set { _htx_x = value; }
        }
        private double _htx_y;

        public double Htx_y
        {
            get { return _htx_y; }
            set { _htx_y = value; }
        }
        /// <summary>
        /// 车辆航向角
        /// </summary>
        private double _hxj;

        public double Hxj
        {
            get { return _hxj; }
            set { _hxj = value; }
        }
        /// <summary>
        /// 车辆俯仰角
        /// </summary>
        private double _fyj;

        public double Fyj
        {
            get { return _fyj; }
            set { _fyj = value; }
        }
        /// <summary>
        /// 车辆横滚角
        /// </summary>
        private double _hgj;

        public double Hgj
        {
            get { return _hgj; }
            set { _hgj = value; }
        }
        /// <summary>
        /// 车辆底盘高度，单位米
        /// </summary>       
        private double _dpgd;

        public double Dpgd
        {
            get { return _dpgd; }
            set { _dpgd = value; }
        }
        /// <summary>
        /// 后天线高度
        /// </summary>
        private double _htxgd;

        public double Htxgd
        {
            get { return _htxgd; }
            set { _htxgd = value; }
        }
        /// <summary>
        /// 车辆标定数据
        /// </summary>
        private List<clbd> _clbdsj = new List<clbd>();

        public List<clbd> Clbdsj
        {
            get { return _clbdsj; }
            set { _clbdsj = value; }
        }
        /// <summary>
        /// 校准调整参数
        /// </summary>
        private double _qhjl;

        public double Qhjl
        {
            get { return _qhjl; }
            set { _qhjl = value; }
        }
        /// <summary>
        /// 校准调整参数
        /// </summary>
        private double _zyjl;

        public double Zyjl
        {
            get { return _zyjl; }
            set { _zyjl = value; }
        }
        /// <summary>
        /// 校准调整参数
        /// </summary>
        private double _xzjd;

        public double Xzjd
        {
            get { return _xzjd; }
            set { _xzjd = value; }
        }

    }

    /// <summary>
    /// 车辆标定数据类
    /// </summary>
    public class clbd
    {
        /// <summary>
        /// 编号
        /// </summary>
        private int _index;

        public int Index
        {
            get { return _index; }
            set { _index = value; }
        }
        /// <summary>
        /// x坐标
        /// </summary>
        private double _x;

        public double X
        {
            get { return _x; }
            set { _x = value; }
        }
        /// <summary>
        /// y坐标
        /// </summary>
        private double _y;

        public double Y
        {
            get { return _y; }
            set { _y = value; }
        }
        /// <summary>
        /// 角度
        /// </summary>
        private double _jd;

        public double Jd
        {
            get { return _jd; }
            set { _jd = value; }
        }
        /// <summary>
        /// 相对角度
        /// </summary>
        private double _xdjd;

        public double Xdjd
        {
            get { return _xdjd; }
            set { _xdjd = value; }
        }
        /// <summary>
        /// 距离
        /// </summary>
        private double _jl;

        public double Jl
        {
            get { return _jl; }
            set { _jl = value; }
        }


    }

    

    public class DoublePoint
    {
        /// <summary>
        /// double类型坐标
        /// </summary>
        private double _x;

        public double X
        {
            get { return _x; }
            set { _x = value; }
        }

        private double _y;

        public double Y
        {
            get { return _y; }
            set { _y = value; }
        }

        //构造函数
        public DoublePoint()
        {

        }
        public DoublePoint(double x, double y)
        {
            this.X = x;
            this.Y = y;
        }

        //该点到指定点pTarget的距离
        public double DistanceTo(DoublePoint p)
        {
            return Math.Sqrt((p.X - X) * (p.X - X) + (p.Y - Y) * (p.Y - Y));
        }
    }


    public class CarSimple
    {
        /// <summary>
        /// 后天线点坐标
        /// </summary>
        private DoublePoint _dp_Htx;

        public DoublePoint Dp_Htx
        {
            get { return _dp_Htx; }
            set { _dp_Htx = value; }
        }



        /// <summary>
        /// 航向角
        /// </summary>
        private double _courseAngle;

        public double CourseAngle
        {
            get { return _courseAngle; }
            set { _courseAngle = value; }
        }
        /// <summary>
        /// 俯仰角
        /// </summary>
        private double _pitchAngle;

        public double PitchAngle
        {
            get { return _pitchAngle; }
            set { _pitchAngle = value; }
        }
        /// <summary>
        /// 横滚角
        /// </summary>
        private double _rollAngle;

        public double RollAngle
        {
            get { return _rollAngle; }
            set { _rollAngle = value; }
        }

    }

    public class PintHelp
    {


        /// <summary>
        /// 获取移动角度的新坐标(俯仰角)
        /// </summary>
        /// <param name="courseRate">航向</param>
        /// <param name="pitchRate">俯仰</param>
        /// <param name="rollRate">横滚</param>
        /// <param name="htxPoint">后天线</param>
        /// <param name="onePoint">点1</param>
        /// <returns></returns>
        public DoublePoint newPointGet(CarSimple carsimple, carModel car, clbd c)
        {


            DoublePoint pointHtx = new DoublePoint(car.Htx_x, car.Htx_y);//后天线坐标
            double courseHtx = Convert.ToDouble(car.Hxj);//后天线航向
            double pitchHtx = Convert.ToDouble(car.Fyj);//后天线俯仰
            double rollHtx = Convert.ToDouble(car.Hgj);//后天线横滚
            //double rollHtx = Convert.ToDouble(0);//后天线横滚

            double heightHtx = car.Htxgd - car.Dpgd;//后天线高度
            DoublePoint pointOne = new DoublePoint(c.X, c.Y);//点1坐标
            double courseOne = c.Jd;//点1航向
            double distanceOne = c.Jl;//点1距离
            DoublePoint newPointHtx = carsimple.Dp_Htx;//新后天线坐标
            double newCourseHtx = carsimple.CourseAngle;//新后天线航向
            double newPitchHtx = carsimple.PitchAngle;//新后天线俯仰角
            //double newRollHtx = 0;//新后天线横滚
            double newRollHtx = carsimple.RollAngle;//新后天线横滚


            pointOne = getNewPointOne(pointHtx, courseHtx, pitchHtx, rollHtx, heightHtx, pointOne, courseOne, distanceOne, pointHtx, courseHtx);

            DoublePoint newPointOne = getNewPointTwo(pointHtx, courseHtx, heightHtx, pointOne, courseOne, distanceOne, newPointHtx, newCourseHtx, newPitchHtx, newRollHtx);
            return newPointOne;


        }



        #region 最终结果

        /// <summary>
        /// 根据航向计算点坐标
        /// </summary>
        /// <param name="courseAngle"></param>
        /// <param name="pointHtx"></param>
        /// <param name="pointOne"></param>
        /// <returns></returns>
        public DoublePoint GetCoursePoint(double courseAngle, DoublePoint pointHtx, DoublePoint pointOne)
        {
            double newx = Convert.ToDouble(((pointOne.X - pointHtx.X) * Math.Cos(courseAngle) - (pointOne.Y - pointHtx.Y) * Math.Sin(courseAngle)).ToString("0.00000"));
            double newy = Convert.ToDouble(((pointOne.Y - pointHtx.Y) * Math.Cos(courseAngle) + (pointOne.X - pointHtx.X) * Math.Sin(courseAngle)).ToString("0.00000"));
            DoublePoint newpoint = new DoublePoint(pointHtx.X + newx, pointHtx.Y + newy);
            return newpoint;
        }
        /// <summary>
        /// 根据移动距离计算点坐标（相减）
        /// </summary>
        /// <param name="pitchAngle"></param>
        /// <param name="pointOne"></param>
        /// <param name="distanceSkew"></param>
        /// <returns></returns>
        public static DoublePoint GetPitchPointCut(double pitchAngle, DoublePoint pointOne, double distanceSkew)
        {
            double Rage2 = pitchAngle / 180 * Math.PI;
            double newX = pointOne.X - distanceSkew * Math.Sin(Rage2);
            double newY = pointOne.Y - distanceSkew * Math.Cos(Rage2);
            DoublePoint newpoint = new DoublePoint(newX, newY);
            return newpoint;
        }
        /// <summary>
        /// 根据移动距离计算点坐标（相加）
        /// </summary>
        /// <param name="pitchAngle"></param>
        /// <param name="pointOne"></param>
        /// <param name="distanceSkew"></param>
        /// <returns></returns>
        public DoublePoint GetPitchPointAdd(double pitchAngle, DoublePoint pointOne, double distanceSkew)
        {
            double Rage2 = pitchAngle / 180 * Math.PI;
            double newX = pointOne.X + distanceSkew * Math.Sin(Rage2);
            double newY = pointOne.Y + distanceSkew * Math.Cos(Rage2);
            DoublePoint newpoint = new DoublePoint(newX, newY);
            return newpoint;
        }
        public DoublePoint getNewPointOne(DoublePoint pointHtx, double courseHtx, double pitchHtx, double rollHtx, double heightHtx, DoublePoint pointOne, double courseOne, double distanceOne, DoublePoint newPointHtx, double newCourseHtx)
        {
            #region 计算俯仰
            double pitchSkew = (-pitchHtx) / 180 * Math.PI;//俯仰偏移的度数
            double compensateDistance = heightHtx * Math.Tan(pitchSkew / 2);
            double cosPith = Math.Cos((courseHtx - courseOne) / 180 * Math.PI);
            double distanceSkewOne = distanceOne * cosPith + compensateDistance - (distanceOne * cosPith - compensateDistance) * Math.Cos(pitchSkew);//点1的偏移量
            DoublePoint pitchPointOne = GetPitchPointAdd(newCourseHtx, pointOne, distanceSkewOne);//俯仰后的点1坐标
            #endregion
            //if (configModel.IsRoll == 1)
            //{
            //    #region 计算横滚
            //    pitchSkew = (-rollHtx) / 180 * Math.PI;//横滚偏移的度数
            //    compensateDistance = heightHtx * Math.Tan(pitchSkew / 2);
            //    double sinRoll = Math.Sin((courseHtx - courseOne) / 180 * Math.PI);
            //    sinRoll = sinRoll > 0 ? sinRoll : -sinRoll;
            //    distanceSkewOne = distanceOne * sinRoll + compensateDistance - (distanceOne * sinRoll - compensateDistance) * Math.Cos(pitchSkew);//点1的偏移量
            //    pitchPointOne = GetPitchPointCut(newCourseHtx + 90, pitchPointOne, distanceSkewOne);//横滚后的点1坐标
            //    #endregion
            //}
            return pitchPointOne;
        }
        public DoublePoint getNewPointTwo(DoublePoint pointHtx, double courseHtx, double heightHtx, DoublePoint pointOne, double courseOne, double distanceOne, DoublePoint newPointHtx, double newCourseHtx, double newPitchHtx, double newRollHtx)
        {
            double pitchSkew = (newPitchHtx) / 180 * Math.PI;//俯仰偏移的度数
            double rollSkew = (newRollHtx) / 180 * Math.PI;//横滚偏移的度数

            double skewHtx_x = newPointHtx.X - pointHtx.X;//后天线偏移量X
            double skewHtx_y = newPointHtx.Y - pointHtx.Y;//后天线偏移量Y


            DoublePoint newPointOne = new DoublePoint(pointOne.X + skewHtx_x, pointOne.Y + skewHtx_y);//偏移后的点1坐标
            double courseSkew = (courseHtx - newCourseHtx) / 180 * Math.PI;//航向偏移量

            DoublePoint coursePointOne = GetCoursePoint(courseSkew, newPointHtx, newPointOne);//航向后的点1坐标

            double compensateDistance = heightHtx * Math.Tan(pitchSkew / 2);
            double cosPith = Math.Cos((courseHtx - courseOne) / 180 * Math.PI);
            double distanceSkewOne = distanceOne * cosPith - compensateDistance - (distanceOne * cosPith + compensateDistance) * Math.Cos(pitchSkew);//点1的偏移量

            DoublePoint pitchPointOne = GetPitchPointCut(newCourseHtx, coursePointOne, distanceSkewOne);//俯仰后的点1坐标

            double compensateRollDistance = heightHtx * Math.Tan(rollSkew / 2);
            double sinRoll = Math.Sin((courseHtx - courseOne) / 180 * Math.PI);
            sinRoll = sinRoll > 0 ? sinRoll : -sinRoll;
            double distanceRollSkewOne = distanceOne * sinRoll - compensateRollDistance - (distanceOne * sinRoll + compensateRollDistance) * Math.Cos(rollSkew);//点1的偏移量

            DoublePoint rollPointOne = GetPitchPointAdd(newCourseHtx + 90, pitchPointOne, distanceRollSkewOne);//横滚后的点1坐标

            return rollPointOne;
        }
        #endregion

        public double getDistance(DoublePoint x1, DoublePoint x2)
        {
            double distance = Math.Sqrt((x1.X - x2.X) * (x1.X - x2.X) + (x1.Y - x2.Y) * (x1.Y - x2.Y));
            return distance;
        }
    }

}

