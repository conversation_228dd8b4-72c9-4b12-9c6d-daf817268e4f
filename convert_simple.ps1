# Simple encoding conversion
$filePath = "SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs"

Write-Host "Converting file encoding..." -ForegroundColor Green

if (-not (Test-Path $filePath)) {
    Write-Host "File not found" -ForegroundColor Red
    exit 1
}

# Create backup
$backupPath = "$filePath.backup"
Copy-Item $filePath $backupPath
Write-Host "Backup created" -ForegroundColor Yellow

try {
    # Read content
    $content = Get-Content $filePath -Raw -Encoding UTF8
    
    # Save as ANSI (which is typically GB2312 on Chinese systems)
    $content | Out-File $filePath -Encoding Default
    
    Write-Host "File converted successfully" -ForegroundColor Green
    
} catch {
    Write-Host "Error: $($_.Exception.Message)" -ForegroundColor Red
    Copy-Item $backupPath $filePath -Force
    Write-Host "Backup restored" -ForegroundColor Yellow
}
