using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using Serilog;

namespace SukiUI.Demo.Infrastructure
{
    /// <summary>
    /// SDK诊断工具
    /// 提供全面的SDK状态检查、性能监控和问题诊断功能
    /// </summary>
    public static class SDKDiagnostics
    {
        /// <summary>
        /// 执行完整的SDK诊断
        /// </summary>
        /// <returns>诊断报告</returns>
        public static SDKDiagnosticReport RunFullDiagnostics()
        {
            var report = new SDKDiagnosticReport
            {
                DiagnosticTime = DateTime.Now,
                Platform = RuntimeInformation.OSDescription
            };

            try
            {
                Log.Information("SDKDiagnostics: 开始执行完整SDK诊断");

                // 1. 系统环境检查
                report.SystemInfo = CollectSystemInfo();

                // 2. 库文件完整性检查
                report.LibraryStatus = CheckLibraryIntegrity();

                // 3. SDK初始化状态检查
                report.SDKStatus = CheckSDKStatus();

                // 4. 资源使用情况检查
                report.ResourceUsage = CheckResourceUsage();

                // 5. 性能指标检查
                report.PerformanceMetrics = CollectPerformanceMetrics();

                // 6. 配置验证
                report.ConfigurationValidation = ValidateConfiguration();

                // 7. 生成建议
                report.Recommendations = GenerateRecommendations(report);

                // 8. 计算总体健康度
                report.OverallHealth = CalculateOverallHealth(report);

                Log.Information($"SDKDiagnostics: 诊断完成，总体健康度: {report.OverallHealth}%");
                return report;
            }
            catch (Exception ex)
            {
                Log.Error(ex, "SDKDiagnostics: 执行诊断时发生异常");
                report.ErrorMessage = ex.Message;
                report.OverallHealth = 0;
                return report;
            }
        }

        /// <summary>
        /// 收集系统信息
        /// </summary>
        /// <returns>系统信息</returns>
        private static SystemInfo CollectSystemInfo()
        {
            var info = new SystemInfo();

            try
            {
                info.OSVersion = Environment.OSVersion.ToString();
                info.ProcessorCount = Environment.ProcessorCount;
                info.WorkingSet = Environment.WorkingSet;
                info.Is64BitProcess = Environment.Is64BitProcess;
                info.Is64BitOperatingSystem = Environment.Is64BitOperatingSystem;
                info.CLRVersion = Environment.Version.ToString();
                info.MachineName = Environment.MachineName;
                info.UserName = Environment.UserName;
                info.CurrentDirectory = Environment.CurrentDirectory;
                info.SystemDirectory = Environment.SystemDirectory;

                // 获取进程信息
                var process = Process.GetCurrentProcess();
                info.ProcessId = process.Id;
                info.ProcessName = process.ProcessName;
                info.StartTime = process.StartTime;
                info.TotalProcessorTime = process.TotalProcessorTime;
                info.PrivateMemorySize = process.PrivateMemorySize64;
                info.VirtualMemorySize = process.VirtualMemorySize64;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "收集系统信息时发生异常");
                info.ErrorMessage = ex.Message;
            }

            return info;
        }

        /// <summary>
        /// 检查库文件完整性
        /// </summary>
        /// <returns>库状态</returns>
        private static LibraryStatus CheckLibraryIntegrity()
        {
            var status = new LibraryStatus();

            try
            {
                var checkResult = NativeLibraryManager.CheckAllLibraryFiles();
                status.TotalLibraries = checkResult.TotalFiles;
                status.ExistingLibraries = checkResult.ExistingFiles;
                status.MissingLibraries = checkResult.MissingLibraries.ToList();
                status.ComponentLibraryExists = checkResult.ComponentLibraryExists;
                status.IsComplete = checkResult.IsAllLibrariesPresent;

                // 检查库文件大小和修改时间
                status.LibraryDetails = new List<LibraryFileInfo>();
                var baseDir = AppDomain.CurrentDomain.BaseDirectory;
                
                foreach (var detail in checkResult.LibraryDetails)
                {
                    var fileInfo = new LibraryFileInfo
                    {
                        FileName = detail.Name,
                        Exists = detail.Exists,
                        IsRequired = detail.IsRequired
                    };

                    if (detail.Exists)
                    {
                        var filePath = Path.Combine(baseDir, "bin", detail.Name);
                        if (File.Exists(filePath))
                        {
                            var fi = new FileInfo(filePath);
                            fileInfo.FileSize = fi.Length;
                            fileInfo.LastModified = fi.LastWriteTime;
                        }
                    }

                    status.LibraryDetails.Add(fileInfo);
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "检查库文件完整性时发生异常");
                status.ErrorMessage = ex.Message;
            }

            return status;
        }

        /// <summary>
        /// 检查SDK状态
        /// </summary>
        /// <returns>SDK状态</returns>
        private static SDKStatus CheckSDKStatus()
        {
            var status = new SDKStatus();

            try
            {
                status.IsNativeLibraryManagerInitialized = NativeLibraryManager.IsInitialized;
                
                var sdkManager = SDKResourceManager.Instance;
                var stats = sdkManager.GetConnectionStatistics();
                
                status.IsSDKInitialized = stats.IsSDKInitialized;
                status.ActiveConnections = stats.ActiveConnections;
                status.TotalConnections = stats.TotalConnections;
                status.AllocatedMemoryBlocks = stats.AllocatedMemoryBlocks;

                // 获取SDK版本信息
                if (status.IsSDKInitialized)
                {
                    try
                    {
                        status.SDKVersion = SukiUI.Demo.Bll.CHCNetSDK.NET_DVR_GetSDKVersion().ToString();
                    }
                    catch (Exception ex)
                    {
                        Log.Warning(ex, "获取SDK版本失败");
                        status.SDKVersionError = ex.Message;
                    }
                }

                // Linux平台特定检查
                if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                {
                    var linuxPaths = NativeLibraryManager.GetLinuxSDKPaths();
                    status.IsLinuxConfigured = linuxPaths?.IsConfigured ?? false;
                    status.LinuxConfigurationDetails = linuxPaths;
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "检查SDK状态时发生异常");
                status.ErrorMessage = ex.Message;
            }

            return status;
        }

        /// <summary>
        /// 检查资源使用情况
        /// </summary>
        /// <returns>资源使用情况</returns>
        private static ResourceUsage CheckResourceUsage()
        {
            var usage = new ResourceUsage();

            try
            {
                var process = Process.GetCurrentProcess();
                
                usage.MemoryUsage = process.WorkingSet64;
                usage.VirtualMemoryUsage = process.VirtualMemorySize64;
                usage.PrivateMemoryUsage = process.PrivateMemorySize64;
                usage.HandleCount = process.HandleCount;
                usage.ThreadCount = process.Threads.Count;

                // GC信息
                usage.Gen0Collections = GC.CollectionCount(0);
                usage.Gen1Collections = GC.CollectionCount(1);
                usage.Gen2Collections = GC.CollectionCount(2);
                usage.TotalMemory = GC.GetTotalMemory(false);

                // CPU使用率（需要一段时间来计算）
                var startTime = DateTime.UtcNow;
                var startCpuUsage = process.TotalProcessorTime;
                System.Threading.Thread.Sleep(100);
                var endTime = DateTime.UtcNow;
                var endCpuUsage = process.TotalProcessorTime;

                var cpuUsedMs = (endCpuUsage - startCpuUsage).TotalMilliseconds;
                var totalMsPassed = (endTime - startTime).TotalMilliseconds;
                var cpuUsageTotal = cpuUsedMs / (Environment.ProcessorCount * totalMsPassed);
                usage.CPUUsagePercent = cpuUsageTotal * 100;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "检查资源使用情况时发生异常");
                usage.ErrorMessage = ex.Message;
            }

            return usage;
        }

        /// <summary>
        /// 收集性能指标
        /// </summary>
        /// <returns>性能指标</returns>
        private static PerformanceMetrics CollectPerformanceMetrics()
        {
            var metrics = new PerformanceMetrics();

            try
            {
                var stopwatch = Stopwatch.StartNew();

                // 测试库加载性能
                var libraryCheckStart = Stopwatch.StartNew();
                NativeLibraryManager.CheckAllLibraryFiles();
                libraryCheckStart.Stop();
                metrics.LibraryCheckTime = libraryCheckStart.ElapsedMilliseconds;

                // 测试SDK配置验证性能
                var configValidationStart = Stopwatch.StartNew();
                NativeLibraryManager.ValidateSDKConfiguration();
                configValidationStart.Stop();
                metrics.ConfigValidationTime = configValidationStart.ElapsedMilliseconds;

                stopwatch.Stop();
                metrics.TotalDiagnosticTime = stopwatch.ElapsedMilliseconds;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "收集性能指标时发生异常");
                metrics.ErrorMessage = ex.Message;
            }

            return metrics;
        }

        /// <summary>
        /// 验证配置
        /// </summary>
        /// <returns>配置验证结果</returns>
        private static ConfigurationValidation ValidateConfiguration()
        {
            var validation = new ConfigurationValidation();

            try
            {
                var sdkValidation = NativeLibraryManager.ValidateSDKConfiguration();
                validation.IsValid = sdkValidation.IsValid;
                validation.Issues = sdkValidation.Issues.ToList();
                validation.Severity = sdkValidation.Severity;
                validation.MissingLibraries = sdkValidation.MissingLibraries.ToList();
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "验证配置时发生异常");
                validation.ErrorMessage = ex.Message;
                validation.IsValid = false;
            }

            return validation;
        }

        /// <summary>
        /// 生成建议
        /// </summary>
        /// <param name="report">诊断报告</param>
        /// <returns>建议列表</returns>
        private static List<string> GenerateRecommendations(SDKDiagnosticReport report)
        {
            var recommendations = new List<string>();

            try
            {
                // 基于库状态的建议
                if (report.LibraryStatus != null && !report.LibraryStatus.IsComplete)
                {
                    recommendations.Add("检测到缺失的库文件，建议重新安装SDK或检查文件权限");
                    
                    if (report.LibraryStatus.MissingLibraries.Count > 0)
                    {
                        recommendations.Add($"缺失的库文件: {string.Join(", ", report.LibraryStatus.MissingLibraries)}");
                    }
                }

                // 基于SDK状态的建议
                if (report.SDKStatus != null)
                {
                    if (!report.SDKStatus.IsNativeLibraryManagerInitialized)
                    {
                        recommendations.Add("原生库管理器未初始化，建议检查初始化流程");
                    }

                    if (!report.SDKStatus.IsSDKInitialized)
                    {
                        recommendations.Add("SDK未初始化，建议检查依赖库和配置");
                    }

                    if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux) && report.SDKStatus.IsLinuxConfigured == false)
                    {
                        recommendations.Add("Linux平台SDK路径未配置，建议检查HCNetSDKCom和SSL库路径");
                    }
                }

                // 基于资源使用的建议
                if (report.ResourceUsage != null)
                {
                    if (report.ResourceUsage.MemoryUsage > 500 * 1024 * 1024) // 500MB
                    {
                        recommendations.Add("内存使用量较高，建议监控内存泄漏");
                    }

                    if (report.ResourceUsage.CPUUsagePercent > 80)
                    {
                        recommendations.Add("CPU使用率较高，建议优化性能");
                    }
                }

                // 基于配置验证的建议
                if (report.ConfigurationValidation != null && !report.ConfigurationValidation.IsValid)
                {
                    recommendations.Add("配置验证失败，建议检查SDK配置完整性");
                    recommendations.AddRange(report.ConfigurationValidation.Issues);
                }

                if (recommendations.Count == 0)
                {
                    recommendations.Add("系统运行正常，无特殊建议");
                }
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "生成建议时发生异常");
                recommendations.Add($"生成建议时发生异常: {ex.Message}");
            }

            return recommendations;
        }

        /// <summary>
        /// 计算总体健康度
        /// </summary>
        /// <param name="report">诊断报告</param>
        /// <returns>健康度百分比 (0-100)</returns>
        private static int CalculateOverallHealth(SDKDiagnosticReport report)
        {
            try
            {
                int totalScore = 0;
                int maxScore = 0;

                // 库文件完整性 (30分)
                maxScore += 30;
                if (report.LibraryStatus?.IsComplete == true)
                {
                    totalScore += 30;
                }
                else if (report.LibraryStatus?.ExistingLibraries > 0)
                {
                    totalScore += (int)(30.0 * report.LibraryStatus.ExistingLibraries / Math.Max(1, report.LibraryStatus.TotalLibraries));
                }

                // SDK初始化状态 (25分)
                maxScore += 25;
                if (report.SDKStatus?.IsSDKInitialized == true)
                {
                    totalScore += 25;
                }
                else if (report.SDKStatus?.IsNativeLibraryManagerInitialized == true)
                {
                    totalScore += 10;
                }

                // 配置验证 (20分)
                maxScore += 20;
                if (report.ConfigurationValidation?.IsValid == true)
                {
                    totalScore += 20;
                }
                else if (report.ConfigurationValidation?.Severity == "警告")
                {
                    totalScore += 10;
                }

                // 资源使用合理性 (15分)
                maxScore += 15;
                if (report.ResourceUsage != null)
                {
                    if (report.ResourceUsage.MemoryUsage < 200 * 1024 * 1024) // < 200MB
                        totalScore += 8;
                    else if (report.ResourceUsage.MemoryUsage < 500 * 1024 * 1024) // < 500MB
                        totalScore += 5;

                    if (report.ResourceUsage.CPUUsagePercent < 50)
                        totalScore += 7;
                    else if (report.ResourceUsage.CPUUsagePercent < 80)
                        totalScore += 3;
                }

                // 性能指标 (10分)
                maxScore += 10;
                if (report.PerformanceMetrics != null)
                {
                    if (report.PerformanceMetrics.LibraryCheckTime < 100) // < 100ms
                        totalScore += 5;
                    else if (report.PerformanceMetrics.LibraryCheckTime < 500) // < 500ms
                        totalScore += 3;

                    if (report.PerformanceMetrics.ConfigValidationTime < 50) // < 50ms
                        totalScore += 5;
                    else if (report.PerformanceMetrics.ConfigValidationTime < 200) // < 200ms
                        totalScore += 3;
                }

                return maxScore > 0 ? (int)((double)totalScore / maxScore * 100) : 0;
            }
            catch (Exception ex)
            {
                Log.Warning(ex, "计算总体健康度时发生异常");
                return 0;
            }
        }
    }

    /// <summary>
    /// SDK诊断报告
    /// </summary>
    public class SDKDiagnosticReport
    {
        public DateTime DiagnosticTime { get; set; } = DateTime.Now;
        public string Platform { get; set; } = "";
        public int OverallHealth { get; set; } = 0;
        public string ErrorMessage { get; set; } = "";

        public SystemInfo? SystemInfo { get; set; }
        public LibraryStatus? LibraryStatus { get; set; }
        public SDKStatus? SDKStatus { get; set; }
        public ResourceUsage? ResourceUsage { get; set; }
        public PerformanceMetrics? PerformanceMetrics { get; set; }
        public ConfigurationValidation? ConfigurationValidation { get; set; }
        public List<string> Recommendations { get; set; } = new List<string>();

        /// <summary>
        /// 生成完整的诊断报告
        /// </summary>
        /// <returns>报告字符串</returns>
        public string GenerateFullReport()
        {
            var report = new StringBuilder();

            report.AppendLine("=== SDK 完整诊断报告 ===");
            report.AppendLine($"诊断时间: {DiagnosticTime:yyyy-MM-dd HH:mm:ss}");
            report.AppendLine($"平台: {Platform}");
            report.AppendLine($"总体健康度: {OverallHealth}%");

            if (!string.IsNullOrEmpty(ErrorMessage))
            {
                report.AppendLine($"错误信息: {ErrorMessage}");
            }

            report.AppendLine();

            // 系统信息
            if (SystemInfo != null)
            {
                report.AppendLine("=== 系统信息 ===");
                report.AppendLine($"操作系统: {SystemInfo.OSVersion}");
                report.AppendLine($"处理器数量: {SystemInfo.ProcessorCount}");
                report.AppendLine($"64位进程: {SystemInfo.Is64BitProcess}");
                report.AppendLine($"64位操作系统: {SystemInfo.Is64BitOperatingSystem}");
                report.AppendLine($"CLR版本: {SystemInfo.CLRVersion}");
                report.AppendLine($"机器名: {SystemInfo.MachineName}");
                report.AppendLine($"进程ID: {SystemInfo.ProcessId}");
                report.AppendLine($"进程名: {SystemInfo.ProcessName}");
                report.AppendLine($"启动时间: {SystemInfo.StartTime:yyyy-MM-dd HH:mm:ss}");
                report.AppendLine($"私有内存: {SystemInfo.PrivateMemorySize / 1024 / 1024:F2} MB");
                report.AppendLine($"虚拟内存: {SystemInfo.VirtualMemorySize / 1024 / 1024:F2} MB");
                report.AppendLine();
            }

            // 库状态
            if (LibraryStatus != null)
            {
                report.AppendLine("=== 库文件状态 ===");
                report.AppendLine($"库文件完整: {(LibraryStatus.IsComplete ? "是" : "否")}");
                report.AppendLine($"总库文件数: {LibraryStatus.TotalLibraries}");
                report.AppendLine($"存在库文件数: {LibraryStatus.ExistingLibraries}");
                report.AppendLine($"组件库存在: {(LibraryStatus.ComponentLibraryExists ? "是" : "否")}");

                if (LibraryStatus.MissingLibraries.Count > 0)
                {
                    report.AppendLine("缺失的库文件:");
                    foreach (var missing in LibraryStatus.MissingLibraries)
                    {
                        report.AppendLine($"  • {missing}");
                    }
                }
                report.AppendLine();
            }

            // SDK状态
            if (SDKStatus != null)
            {
                report.AppendLine("=== SDK状态 ===");
                report.AppendLine($"库管理器已初始化: {(SDKStatus.IsNativeLibraryManagerInitialized ? "是" : "否")}");
                report.AppendLine($"SDK已初始化: {(SDKStatus.IsSDKInitialized ? "是" : "否")}");
                report.AppendLine($"活动连接数: {SDKStatus.ActiveConnections}");
                report.AppendLine($"总连接数: {SDKStatus.TotalConnections}");
                report.AppendLine($"分配内存块数: {SDKStatus.AllocatedMemoryBlocks}");

                if (!string.IsNullOrEmpty(SDKStatus.SDKVersion))
                {
                    report.AppendLine($"SDK版本: {SDKStatus.SDKVersion}");
                }

                if (SDKStatus.IsLinuxConfigured.HasValue)
                {
                    report.AppendLine($"Linux配置状态: {(SDKStatus.IsLinuxConfigured.Value ? "已配置" : "未配置")}");
                }
                report.AppendLine();
            }

            // 资源使用
            if (ResourceUsage != null)
            {
                report.AppendLine("=== 资源使用情况 ===");
                report.AppendLine($"内存使用: {ResourceUsage.MemoryUsage / 1024 / 1024:F2} MB");
                report.AppendLine($"虚拟内存: {ResourceUsage.VirtualMemoryUsage / 1024 / 1024:F2} MB");
                report.AppendLine($"私有内存: {ResourceUsage.PrivateMemoryUsage / 1024 / 1024:F2} MB");
                report.AppendLine($"句柄数: {ResourceUsage.HandleCount}");
                report.AppendLine($"线程数: {ResourceUsage.ThreadCount}");
                report.AppendLine($"CPU使用率: {ResourceUsage.CPUUsagePercent:F2}%");
                report.AppendLine($"GC总内存: {ResourceUsage.TotalMemory / 1024 / 1024:F2} MB");
                report.AppendLine($"GC回收次数 (Gen0/Gen1/Gen2): {ResourceUsage.Gen0Collections}/{ResourceUsage.Gen1Collections}/{ResourceUsage.Gen2Collections}");
                report.AppendLine();
            }

            // 性能指标
            if (PerformanceMetrics != null)
            {
                report.AppendLine("=== 性能指标 ===");
                report.AppendLine($"库检查耗时: {PerformanceMetrics.LibraryCheckTime} ms");
                report.AppendLine($"配置验证耗时: {PerformanceMetrics.ConfigValidationTime} ms");
                report.AppendLine($"总诊断耗时: {PerformanceMetrics.TotalDiagnosticTime} ms");
                report.AppendLine();
            }

            // 配置验证
            if (ConfigurationValidation != null)
            {
                report.AppendLine("=== 配置验证 ===");
                report.AppendLine($"配置有效: {(ConfigurationValidation.IsValid ? "是" : "否")}");
                report.AppendLine($"严重程度: {ConfigurationValidation.Severity}");

                if (ConfigurationValidation.Issues.Count > 0)
                {
                    report.AppendLine("发现的问题:");
                    foreach (var issue in ConfigurationValidation.Issues)
                    {
                        report.AppendLine($"  • {issue}");
                    }
                }
                report.AppendLine();
            }

            // 建议
            if (Recommendations.Count > 0)
            {
                report.AppendLine("=== 建议 ===");
                foreach (var recommendation in Recommendations)
                {
                    report.AppendLine($"• {recommendation}");
                }
                report.AppendLine();
            }

            report.AppendLine("=== 诊断报告结束 ===");
            return report.ToString();
        }
    }

    /// <summary>
    /// 系统信息
    /// </summary>
    public class SystemInfo
    {
        public string OSVersion { get; set; } = "";
        public int ProcessorCount { get; set; } = 0;
        public long WorkingSet { get; set; } = 0;
        public bool Is64BitProcess { get; set; } = false;
        public bool Is64BitOperatingSystem { get; set; } = false;
        public string CLRVersion { get; set; } = "";
        public string MachineName { get; set; } = "";
        public string UserName { get; set; } = "";
        public string CurrentDirectory { get; set; } = "";
        public string SystemDirectory { get; set; } = "";
        public int ProcessId { get; set; } = 0;
        public string ProcessName { get; set; } = "";
        public DateTime StartTime { get; set; } = DateTime.Now;
        public TimeSpan TotalProcessorTime { get; set; } = TimeSpan.Zero;
        public long PrivateMemorySize { get; set; } = 0;
        public long VirtualMemorySize { get; set; } = 0;
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 库状态
    /// </summary>
    public class LibraryStatus
    {
        public int TotalLibraries { get; set; } = 0;
        public int ExistingLibraries { get; set; } = 0;
        public List<string> MissingLibraries { get; set; } = new List<string>();
        public bool ComponentLibraryExists { get; set; } = false;
        public bool IsComplete { get; set; } = false;
        public List<LibraryFileInfo> LibraryDetails { get; set; } = new List<LibraryFileInfo>();
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 库文件信息
    /// </summary>
    public class LibraryFileInfo
    {
        public string FileName { get; set; } = "";
        public bool Exists { get; set; } = false;
        public bool IsRequired { get; set; } = false;
        public long FileSize { get; set; } = 0;
        public DateTime LastModified { get; set; } = DateTime.MinValue;
    }

    /// <summary>
    /// SDK状态
    /// </summary>
    public class SDKStatus
    {
        public bool IsNativeLibraryManagerInitialized { get; set; } = false;
        public bool IsSDKInitialized { get; set; } = false;
        public int ActiveConnections { get; set; } = 0;
        public int TotalConnections { get; set; } = 0;
        public int AllocatedMemoryBlocks { get; set; } = 0;
        public string SDKVersion { get; set; } = "";
        public string SDKVersionError { get; set; } = "";
        public bool? IsLinuxConfigured { get; set; }
        public LinuxSDKPaths? LinuxConfigurationDetails { get; set; }
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 资源使用情况
    /// </summary>
    public class ResourceUsage
    {
        public long MemoryUsage { get; set; } = 0;
        public long VirtualMemoryUsage { get; set; } = 0;
        public long PrivateMemoryUsage { get; set; } = 0;
        public int HandleCount { get; set; } = 0;
        public int ThreadCount { get; set; } = 0;
        public double CPUUsagePercent { get; set; } = 0;
        public long TotalMemory { get; set; } = 0;
        public int Gen0Collections { get; set; } = 0;
        public int Gen1Collections { get; set; } = 0;
        public int Gen2Collections { get; set; } = 0;
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 性能指标
    /// </summary>
    public class PerformanceMetrics
    {
        public long LibraryCheckTime { get; set; } = 0;
        public long ConfigValidationTime { get; set; } = 0;
        public long TotalDiagnosticTime { get; set; } = 0;
        public string ErrorMessage { get; set; } = "";
    }

    /// <summary>
    /// 配置验证
    /// </summary>
    public class ConfigurationValidation
    {
        public bool IsValid { get; set; } = false;
        public string Severity { get; set; } = "";
        public List<string> Issues { get; set; } = new List<string>();
        public List<string> MissingLibraries { get; set; } = new List<string>();
        public string ErrorMessage { get; set; } = "";
    }
}
