<UserControl x:Class="SukiUI.Demo.Features.CarControl.CarListView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:Maps="clr-namespace:Mapsui.UI.Avalonia;assembly=Mapsui.UI.Avalonia"
             xmlns:carControl="clr-namespace:SukiUI.Demo.Features.CarControl"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             d:DesignHeight="480"
             d:DesignWidth="960"
             x:DataType="carControl:CarListViewModel"
             mc:Ignorable="d">
    <ScrollViewer>
        <ItemsControl ItemsSource="{Binding CarControls}">
            <ItemsControl.ItemTemplate>
                <DataTemplate>
                    <StackPanel>

                        <carControl:CarControlView x:Name="{Binding CarNo}" />

                    </StackPanel>
                </DataTemplate>
            </ItemsControl.ItemTemplate>
        </ItemsControl>
    </ScrollViewer>
</UserControl>
