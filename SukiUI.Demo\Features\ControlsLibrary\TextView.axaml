<UserControl x:Class="SukiUI.Demo.Features.ControlsLibrary.TextView"
             xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:showMeTheXaml="clr-namespace:ShowMeTheXaml;assembly=ShowMeTheXaml.Avalonia"
             xmlns:avalonia="clr-namespace:Material.Icons.Avalonia;assembly=Material.Icons.Avalonia"
             d:DesignHeight="450"
             d:DesignWidth="800"
             x:DataType="controlsLibrary:TextViewModel"
             mc:Ignorable="d">
    <UserControl.Styles>
        <Style Selector="showMeTheXaml|XamlDisplay &gt; TextBox">
            <Setter Property="Text" Value="{Binding TextBoxValue}" />
        </Style>
        <Style Selector="TextBlock">
            <Setter Property="Text" Value="{Binding TextBlockValue}" />
            <Setter Property="TextWrapping" Value="Wrap" />
        </Style>
    </UserControl.Styles>
    <ScrollViewer HorizontalScrollBarVisibility="Disabled">
        <WrapPanel Classes="PageContainer">
            <suki:GlassCard>
                <suki:GroupBox Header="Text Styles">
                    <StackPanel Spacing="15">
                        <StackPanel Classes="HeaderContent">
                            <TextBlock>
                                SukiUI defines some basic text classes that you can use in your application.
                            </TextBlock>
                            <TextBlock>
                                The h and color styles can be combined.
                            </TextBlock>
                        </StackPanel>
                        <showMeTheXaml:XamlDisplay UniqueId="TextHyperlink">
                            <TextBlock xml:space="preserve">Text With A <HyperlinkButton Command="{Binding HyperlinkClickedCommand}" IsVisited="{Binding HyperlinkVisited}" Content="Hyperlink" /> Inside.</TextBlock>
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock1">
                            <TextBlock Classes="h1" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock2">
                            <TextBlock Classes="h2" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock3">
                            <TextBlock Classes="h3" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock4">
                            <TextBlock Classes="h4" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock5">
                            <TextBlock Classes="Caption" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock6">
                            <TextBlock Classes="Primary" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock7">
                            <TextBlock Classes="Accent" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBlock8">
                            <TextBlock />
                        </showMeTheXaml:XamlDisplay>
                        <TextBox Text="{Binding TextBlockValue}" />
                    </StackPanel>
                </suki:GroupBox>
            </suki:GlassCard>
            <suki:GlassCard>
                <suki:GroupBox Header="Text Input">
                    <StackPanel Classes="HeaderContent">
                       
                        <TextBlock>
                            There are a few helpers available to make textboxes simpler and more powerful to use.
                        </TextBlock>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBox1">
                            <TextBox  />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBox1Search">
                            <TextBox  >
                                <TextBox.InnerLeftContent>
                                    <avalonia:MaterialIcon Kind="Search" Foreground="{DynamicResource SukiLowText}" Margin="5,0" Height="17" Width="17" VerticalAlignment="Center"/>
                                </TextBox.InnerLeftContent>
                                <TextBox.InnerRightContent>
                                    <avalonia:MaterialIcon Kind="Check" Foreground="{DynamicResource SukiLowText}" Margin="5,0" Height="17" Width="17" VerticalAlignment="Center"/>
                                </TextBox.InnerRightContent>
                            </TextBox>
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBox2">
                            <TextBox Text="" Watermark="Watermark" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBox3">
                            <TextBox suki:TextBoxExtensions.Prefix="https://" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBox4">
                            <TextBox PasswordChar="*" />
                        </showMeTheXaml:XamlDisplay>
                        <showMeTheXaml:XamlDisplay UniqueId="TextBox5">
                            <TextBox suki:TextBoxExtensions.AddDeleteButton="True" />
                        </showMeTheXaml:XamlDisplay>
                    </StackPanel>
                </suki:GroupBox>
            </suki:GlassCard>
        </WrapPanel>
    </ScrollViewer>
</UserControl>