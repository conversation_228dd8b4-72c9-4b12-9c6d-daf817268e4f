<ResourceDictionary xmlns="https://github.com/avaloniaui"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:content="clr-namespace:SukiUI.Content"
                    xmlns:suki="clr-namespace:SukiUI.Controls">
    <Design.PreviewWith>
        <suki:SukiSideMenu>
            <suki:SukiSideMenuItem Header="Header">
                <suki:SukiSideMenuItem.Icon>
                    <PathIcon Width="13"
                              Height="13"
                              Data="{x:Static content:Icons.Cross}" />
                </suki:SukiSideMenuItem.Icon>

            </suki:SukiSideMenuItem>
            <suki:SukiSideMenuItem Header="Header">
                <suki:SukiSideMenuItem.Icon>
                    <PathIcon Width="13"
                              Height="13"
                              Data="{x:Static content:Icons.Cross}" />
                </suki:SukiSideMenuItem.Icon>
                <Grid />
            </suki:SukiSideMenuItem>
        </suki:SukiSideMenu>
    </Design.PreviewWith>
    <ControlTheme x:Key="SukiSideMenuItemStyle" TargetType="suki:SukiSideMenuItem">
        <Setter Property="Template">
            <ControlTemplate>
                <Panel>
                <Border Name="PART_Border"
                         MinWidth="85"
                         Margin="15,2"
                         Padding="30,15,15,15"
                         Background="{DynamicResource SukiPrimaryColor0}"
                         CornerRadius="{DynamicResource MediumCornerRadius}">
                   
                    <Border.Transitions>
                        <Transitions>
                            <DoubleTransition Property="MaxHeight" Duration="0:0:1">
                                <DoubleTransition.Easing>CubicEaseInOut</DoubleTransition.Easing>
                            </DoubleTransition>
                            <BrushTransition Property="Background" Duration="0:0:0.35" />
                            <ThicknessTransition Property="Padding" Duration="0:0:0.35">
                                <ThicknessTransition.Easing>CubicEaseInOut</ThicknessTransition.Easing>
                            </ThicknessTransition>
                        </Transitions>
                    </Border.Transitions>
                    <Panel>
                        <DockPanel Name="PART_NormalDisplay" LastChildFill="True">
                            <DockPanel.Resources />

                            <ContentControl Name="PART_Icon"
                                            Width="24"
                                            Height="24"
                                            Content="{TemplateBinding Icon}"
                                            DockPanel.Dock="Left">
                                <ContentControl.Transitions>
                                    <Transitions>
                                        <TransformOperationsTransition Property="RenderTransform" Duration="0:0:0.2" />
                                        <DoubleTransition Property="Opacity" Duration="0:0:0.2" />
                                    </Transitions>
                                </ContentControl.Transitions>
                            </ContentControl>
                            <Viewbox HorizontalAlignment="Left" StretchDirection="DownOnly">
                                <TextBlock Name="PART_Header"
                                           Margin="15,0"
                                           VerticalAlignment="Center"
                                           FontSize="{TemplateBinding FontSize}"
                                           IsVisible="{Binding IsMenuExpanded, RelativeSource={RelativeSource Mode=FindAncestor, AncestorType={x:Type suki:SukiSideMenu}}}"
                                           Text="{TemplateBinding Header}" />
                            </Viewbox>
                        </DockPanel>
                        <ContentPresenter Name="PART_AltDisplay"
                                          Content="{TemplateBinding Content}"
                                          ContentTemplate="{TemplateBinding ContentTemplate}" />

                    </Panel>
                </Border>
                </Panel>
            </ControlTemplate>
        </Setter>
        <Style Selector="^.Compact /template/ Border#PART_Border">
            <Setter Property="Padding" Value="30,10,15,10" />
            <Setter Property="CornerRadius" Value="12" />
        </Style>
        <Style Selector="^:pointerover">
            <Style Selector="^ /template/ Border#PART_Border">
                <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor5}" />
                <Setter Property="Cursor" Value="Hand" />
            </Style>
        </Style>
        <Style Selector="^[IsSelected=True]">
            <Style Selector="^ /template/ Border#PART_Border">
                <Setter Property="Background" Value="{DynamicResource SukiPrimaryColor10}" />
            </Style>
            <Style Selector="^ /template/ TextBlock#PART_Header">
                <Setter Property="FontWeight" Value="Bold" />
                <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
            </Style>
            <Style Selector="^[IsContentMovable=True]">
                <Style Selector="^[IsTopMenuExpanded=True] /template/ Border#PART_Border">
                    <Setter Property="Padding" Value="-10,15,15,15" />
                </Style>
                <Style Selector="^.Compact[IsTopMenuExpanded=True] /template/ Border#PART_Border">
                    <Setter Property="Padding" Value="-10,10,15,10" />
                </Style>
                <Style Selector="^[IsTopMenuExpanded=True] /template/ ContentControl#PART_Icon">
                    <Setter Property="Opacity" Value="0" />
                </Style>
                <Style Selector="^[IsTopMenuExpanded=False] /template/ ContentControl#PART_Icon">
                    <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
                    <Setter Property="RenderTransform">
                        <ScaleTransform ScaleX="1.12" ScaleY="1.12" />
                    </Setter>
                </Style>
            </Style>
            <Style Selector="^[IsContentMovable=False]">
                <Style Selector="^ /template/ ContentControl#PART_Icon">
                    <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
                </Style>
            </Style>
        </Style>
        <Setter Property="FontSize" Value="14" />
    </ControlTheme>
    <ControlTheme x:Key="{x:Type suki:SukiSideMenuItem}"
                  BasedOn="{StaticResource SukiSideMenuItemStyle}"
                  TargetType="suki:SukiSideMenuItem" />
</ResourceDictionary>
