﻿using System;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using UdpClient = NetCoreServer.UdpClient;


namespace SukiUI.Demo.Bll
{

   public class SiHeYiUdpClient : UdpClient
    {
        public Action<string> DataReceived;
        public SiHeYiUdpClient(string address, int port) : base(address, port) { }

        public void DisconnectAndStop()
        {
            _stop = true;
            Disconnect();
            while (IsConnected)
                Thread.Yield();
        }

        protected override void OnConnected()
        {
            Console.WriteLine($"Echo UDP client connected a new session with Id {Id}");

            // Start receive datagrams
            ReceiveAsync();
        }

        protected override void OnDisconnected()
        {
            Console.WriteLine($"Echo UDP client disconnected a session with Id {Id}");

            // Wait for a while...
            Thread.Sleep(1000);

            // Try to connect again
            if (!_stop)
                Connect();
        }

        protected override void OnReceived(EndPoint endpoint, byte[] buffer, long offset, long size)
        {
            //Console.WriteLine("Incoming: " + Encoding.UTF8.GetString(buffer, (int)offset, (int)size));
            var message = Encoding.UTF8.GetString(buffer, (int)offset, (int)size);
            DataReceived?.Invoke(message);
            // Continue receive datagrams
            ReceiveAsync();
        }

        protected override void OnError(SocketError error)
        {
            Console.WriteLine($"Echo UDP client caught an error with code {error}");
        }

        private bool _stop;
    }

    //class Program
    //{
    //    static void Main(string[] args)
    //    {
    //        // UDP server address
    //        string address = "127.0.0.1";
    //        if (args.Length > 0)
    //            address = args[0];

    //        // UDP server port
    //        int port = 3333;
    //        if (args.Length > 1)
    //            port = int.Parse(args[1]);

    //        Console.WriteLine($"UDP server address: {address}");
    //        Console.WriteLine($"UDP server port: {port}");

    //        Console.WriteLine();

    //        // Create a new TCP chat client
    //        var client = new EchoClient(address, port);

    //        // Connect the client
    //        Console.Write("Client connecting...");
    //        client.Connect();
    //        Console.WriteLine("Done!");

    //        Console.WriteLine("Press Enter to stop the client or '!' to reconnect the client...");

    //        // Perform text input
    //        for (; ; )
    //        {
    //            string line = Console.ReadLine();
    //            if (string.IsNullOrEmpty(line))
    //                break;

    //            // Disconnect the client
    //            if (line == "!")
    //            {
    //                Console.Write("Client disconnecting...");
    //                client.Disconnect();
    //                Console.WriteLine("Done!");
    //                continue;
    //            }

    //            // Send the entered text to the chat server
    //            client.Send(line);
    //        }

    //        // Disconnect the client
    //        Console.Write("Client disconnecting...");
    //        client.DisconnectAndStop();
    //        Console.WriteLine("Done!");
    //    }
    //}
}

