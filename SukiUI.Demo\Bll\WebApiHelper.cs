﻿using RestSharp;


namespace SukiUI.Demo.Bll
{
    public class WebApiHelper
    {
        private static readonly string BaseURL = AppStaticData.config["ApplicationSettings:Url"]??"";
        public static T PostData<T>(string resuorce, object PostObj, bool isPost = false, string token = "") where T : new()
        {
            //只上传乖乖的数据
            //if (AppStaticData.yzsf != "1") return new T();
            var options = new RestClientOptions(BaseURL)
            {
                //设置超时时间
                Timeout = TimeSpan.FromSeconds(30)
            };
            var client = new RestClient(options);
            //var client = new RestClient(AppStaticData.ycjkConfig.jkurl);
            //类转换为Json时首字母小写
            //SimpleJson.CurrentJsonSerializerStrategy = new CamelCaseSerializerStrategy();
            //client.Authenticator = new HttpBasicAuthenticator(username, password);
            var request = new RestRequest(resuorce, Method.Get);
            if (isPost)
            {
                request = new RestRequest(resuorce, Method.Post);
            }
            //request.Parameters.clear();
            //request.JsonSerializer = new Share.Serializers.JsonSerializer();
            request.RequestFormat = DataFormat.Json;
            request.AddHeader("Content-Type", "application/json");
            request.AddJsonBody(PostObj);
            //string TokenId = AppStaticData.Token;
            //MessageBox.Show(TokenId);
            if (!string.IsNullOrEmpty(token))
            {
                request.AddHeader("token", token);
            }

            //返回字符串
            //return client.Execute(request).Content;
            //返回对像

            RestResponse<T> response = client.Execute<T>(request);
            if (response.IsSuccessful )
            {
                return response.Data?? new T();
                //MessageBox.Show(response.ErrorException.Message);
              
            }
            return new T();
        }
        public static string PostData(string resuorce, object PostObj, bool isPost = false, string token = "")
        {
            //只上传乖乖的数据
            //if (AppStaticData.yzsf != "1") return new T();
            //var client = new RestClient(Properties.Settings.Default.BaseURL);            
            var options = new RestClientOptions(BaseURL)
            {
                //设置超时时间
                Timeout = TimeSpan.FromSeconds(30)


            };
            var client = new RestClient(options);
            
           
            //类转换为Json时首字母小写
            //SimpleJson.CurrentJsonSerializerStrategy = new CamelCaseSerializerStrategy();
            //client.Authenticator = new HttpBasicAuthenticator(username, password);
            var request = new RestRequest(resuorce, Method.Get);
            if (isPost)
            {
                request = new RestRequest(resuorce, Method.Post);
            }

            //request.Parameters;
            //request.JsonSerializer = new Share.Serializers.JsonSerializer();
            request.RequestFormat = DataFormat.Json;
            request.AddHeader("Content-Type", "application/json");
            request.AddJsonBody(PostObj);
            //MessageBox.Show(TokenId);
            if (!string.IsNullOrEmpty(token))
            {
                request.AddHeader("token", token);
            }
            //返回字符串
            var response = client.Execute(request);
            if (response.IsSuccessful)
            {
                return response.Content??"";

            }
            return "";


        }

    }
}
