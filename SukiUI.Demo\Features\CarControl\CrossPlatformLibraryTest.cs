using System;
using System.IO;
using System.Runtime.InteropServices;
using Serilog;
using SukiUI.Demo.Bll;
using SukiUI.Demo.Infrastructure;

namespace SukiUI.Demo.Features.CarControl
{
    /// <summary>
    /// 跨平台库加载测试类
    /// </summary>
    public static class CrossPlatformLibraryTest
    {

        /// <summary>
        /// 测试跨平台库加载功能
        /// </summary>
        /// <returns>测试结果信息</returns>
        public static string TestLibraryLoading()
        {
            try
            {
                Log.Information("开始测试跨平台库加载功能...");

                var results = new System.Text.StringBuilder();
                results.AppendLine("=== 跨平台库加载测试报告 ===");
                results.AppendLine($"测试时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                results.AppendLine();

                // 1. 平台信息检测
                results.AppendLine("1. 平台信息检测:");
                var platform = GetCurrentPlatform();
                results.AppendLine($"   当前平台: {platform}");
                results.AppendLine($"   操作系统: {RuntimeInformation.OSDescription}");
                results.AppendLine($"   架构: {RuntimeInformation.OSArchitecture}");
                results.AppendLine();

                // 2. 库文件完整性检查
                results.AppendLine("2. 库文件完整性检查:");
                var libraryCheckResult = NativeLibraryManager.CheckAllLibraryFiles();
                results.AppendLine($"   检查结果: {(libraryCheckResult.IsAllLibrariesPresent ? "✓ 通过" : "✗ 失败")}");
                results.AppendLine($"   平台: {libraryCheckResult.Platform}");
                results.AppendLine($"   总文件: {libraryCheckResult.TotalFiles}, 存在: {libraryCheckResult.ExistingFiles}, 缺失: {libraryCheckResult.MissingFiles}");

                if (libraryCheckResult.MissingFiles > 0)
                {
                    results.AppendLine($"   缺失文件: {string.Join(", ", libraryCheckResult.MissingLibraries.Take(5))}");
                    if (libraryCheckResult.MissingLibraries.Count > 5)
                    {
                        results.AppendLine($"   ... 还有 {libraryCheckResult.MissingLibraries.Count - 5} 个文件缺失");
                    }
                }

                if (libraryCheckResult.ComponentLibraryExists)
                {
                    results.AppendLine($"   HCNetSDKCom组件库: ✓ 存在 ({libraryCheckResult.ComponentLibraryCount} 个文件)");
                }
                else
                {
                    results.AppendLine($"   HCNetSDKCom组件库: ✗ 缺失");
                }
                results.AppendLine();

                // 3. NativeLibraryManager库信息
                results.AppendLine("3. NativeLibraryManager库信息:");
                try
                {
                    var managerInfo = NativeLibraryManager.GetLibraryInfo();
                    results.AppendLine($"   {managerInfo}");
                }
                catch (Exception ex)
                {
                    results.AppendLine($"   获取NativeLibraryManager库信息失败: {ex.Message}");
                }
                results.AppendLine();

                // 4. 基本SDK功能测试
                results.AppendLine("4. 基本SDK功能测试:");
                var sdkTest = TestBasicSDKFunctions();
                results.AppendLine($"   SDK初始化测试: {sdkTest}");
                results.AppendLine();

                // 5. 总结
                results.AppendLine("5. 测试总结:");
                var overallResult = libraryCheckResult.IsAllLibrariesPresent && sdkTest.Contains("成功") ? "✓ 通过" : "✗ 部分失败";
                results.AppendLine($"   总体测试结果: {overallResult}");

                if (!libraryCheckResult.IsAllLibrariesPresent)
                {
                    results.AppendLine("   建议: 请确保所有必需的库文件存在于正确的目录中");
                    results.AppendLine($"   当前平台: {libraryCheckResult.Platform}");
                    if (libraryCheckResult.MissingFiles > 0)
                    {
                        results.AppendLine($"   缺失 {libraryCheckResult.MissingFiles} 个库文件，请检查SDK安装包");
                    }
                    if (!libraryCheckResult.ComponentLibraryExists)
                    {
                        results.AppendLine($"   HCNetSDKCom组件库目录缺失，这可能影响某些功能");
                    }
                }

                results.AppendLine();
                results.AppendLine("=== 测试报告结束 ===");

                var finalResult = results.ToString();
                Log.Information("跨平台库加载测试完成");
                return finalResult;
            }
            catch (Exception ex)
            {
                var errorMsg = $"测试过程中发生异常: {ex.Message}\n{ex.StackTrace}";
                Log.Error(ex, "跨平台库加载测试失败");
                return errorMsg;
            }
        }

        /// <summary>
        /// 获取当前平台名称
        /// </summary>
        /// <returns>平台名称</returns>
        private static string GetCurrentPlatform()
        {
            if (RuntimeInformation.IsOSPlatform(OSPlatform.Windows))
                return "Windows";
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.Linux))
                return "Linux";
            else if (RuntimeInformation.IsOSPlatform(OSPlatform.OSX))
                return "macOS";
            else
                return "Unknown";
        }

        /// <summary>
        /// 检查库文件是否存在
        /// </summary>
        /// <returns>库文件是否存在</returns>
        private static bool CheckLibraryFiles()
        {
            return NativeLibraryManager.CheckLibraryExists();
        }

        /// <summary>
        /// 生成详细的库文件检查报告
        /// </summary>
        /// <returns>详细检查报告</returns>
        public static string GenerateDetailedLibraryReport()
        {
            try
            {
                Log.Information("开始生成详细库文件检查报告...");

                var checkResult = NativeLibraryManager.CheckAllLibraryFiles();
                var report = checkResult.GenerateReport();

                Log.Information("详细库文件检查报告生成完成");
                return report;
            }
            catch (Exception ex)
            {
                var errorReport = $"生成库文件检查报告时发生异常: {ex.Message}\n{ex.StackTrace}";
                Log.Error(ex, "生成库文件检查报告失败");
                return errorReport;
            }
        }

        /// <summary>
        /// 获取库文件诊断信息
        /// </summary>
        /// <returns>诊断信息</returns>
        public static string GetLibraryDiagnostics()
        {
            try
            {
                var diagnostics = new System.Text.StringBuilder();

                diagnostics.AppendLine("=== 库文件诊断信息 ===");
                diagnostics.AppendLine($"诊断时间: {DateTime.Now:yyyy-MM-dd HH:mm:ss}");
                diagnostics.AppendLine();

                // 基本系统信息
                diagnostics.AppendLine("系统信息:");
                diagnostics.AppendLine($"  操作系统: {RuntimeInformation.OSDescription}");
                diagnostics.AppendLine($"  架构: {RuntimeInformation.ProcessArchitecture}");
                diagnostics.AppendLine($"  运行时: {RuntimeInformation.FrameworkDescription}");
                diagnostics.AppendLine($"  应用目录: {AppDomain.CurrentDomain.BaseDirectory}");
                diagnostics.AppendLine();

                // 库文件检查结果
                var checkResult = NativeLibraryManager.CheckAllLibraryFiles();
                diagnostics.AppendLine("库文件状态:");
                diagnostics.AppendLine($"  平台: {checkResult.Platform}");
                diagnostics.AppendLine($"  总体状态: {(checkResult.IsAllLibrariesPresent ? "正常" : "异常")}");
                diagnostics.AppendLine($"  文件统计: {checkResult.ExistingFiles}/{checkResult.TotalFiles}");
                diagnostics.AppendLine();

                // NativeLibraryManager状态
                var managerInfo = NativeLibraryManager.GetLibraryInfo();
                diagnostics.AppendLine("库管理器状态:");
                diagnostics.AppendLine($"  {managerInfo}");
                diagnostics.AppendLine();

                // 详细的库文件列表
                if (checkResult.LibraryDetails.Count > 0)
                {
                    diagnostics.AppendLine("详细库文件状态:");
                    foreach (var lib in checkResult.LibraryDetails.OrderBy(l => l.Name))
                    {
                        var status = lib.Exists ? "✓" : "✗";
                        var required = lib.IsRequired ? "[必需]" : "[可选]";
                        diagnostics.AppendLine($"  {status} {lib.Name} {required} - {lib.Description}");
                        if (!lib.Exists)
                        {
                            diagnostics.AppendLine($"    预期路径: {lib.ExpectedPath}");
                        }
                    }
                    diagnostics.AppendLine();
                }

                // 错误信息
                if (!string.IsNullOrEmpty(checkResult.ErrorMessage))
                {
                    diagnostics.AppendLine($"错误信息: {checkResult.ErrorMessage}");
                    diagnostics.AppendLine();
                }

                // 建议和下一步
                if (checkResult.MissingFiles > 0)
                {
                    diagnostics.AppendLine("建议操作:");
                    diagnostics.AppendLine("1. 从官方SDK包中复制缺失的库文件");
                    diagnostics.AppendLine("2. 确保库文件版本与应用程序兼容");
                    diagnostics.AppendLine("3. 检查文件权限（Linux/macOS）");
                    diagnostics.AppendLine("4. 重新运行测试验证修复结果");
                }

                diagnostics.AppendLine("=== 诊断信息结束 ===");
                return diagnostics.ToString();
            }
            catch (Exception ex)
            {
                return $"获取诊断信息时发生异常: {ex.Message}";
            }
        }

        /// <summary>
        /// 测试基本SDK功能
        /// </summary>
        /// <returns>测试结果</returns>
        private static string TestBasicSDKFunctions()
        {
            try
            {
                // 尝试调用SDK初始化函数
                var initResult = CHCNetSDK.NET_DVR_Init();
                
                if (initResult)
                {
                    // 获取SDK版本信息
                    var version = CHCNetSDK.NET_DVR_GetSDKVersion();
                    
                    // 清理SDK
                    CHCNetSDK.NET_DVR_Cleanup();
                    
                    return $"成功 - SDK版本: {version}";
                }
                else
                {
                    var errorCode = CHCNetSDK.NET_DVR_GetLastError();
                    return $"失败 - 错误代码: {errorCode}";
                }
            }
            catch (Exception ex)
            {
                return $"异常 - {ex.Message}";
            }
        }

        /// <summary>
        /// 获取详细的系统和库信息
        /// </summary>
        /// <returns>详细信息字符串</returns>
        public static string GetDetailedSystemInfo()
        {
            var info = new System.Text.StringBuilder();

            info.AppendLine("=== 详细系统信息 ===");
            info.AppendLine($"操作系统: {Environment.OSVersion}");
            info.AppendLine($"CLR版本: {Environment.Version}");
            info.AppendLine($"机器名: {Environment.MachineName}");
            info.AppendLine($"用户名: {Environment.UserName}");
            info.AppendLine($"工作目录: {Environment.CurrentDirectory}");
            info.AppendLine($"应用程序目录: {AppDomain.CurrentDomain.BaseDirectory}");
            info.AppendLine($"处理器数量: {Environment.ProcessorCount}");
            info.AppendLine($"系统架构: {RuntimeInformation.ProcessArchitecture}");
            info.AppendLine($"运行时标识符: {RuntimeInformation.RuntimeIdentifier}");
            info.AppendLine();

            // 添加NativeLibraryManager的详细信息
            info.AppendLine(NativeLibraryManager.GetDetailedSystemInfo());

            return info.ToString();
        }
    }
}
