<suki:SukiWindow x:Class="SukiUI.Demo.Features.ControlsLibrary.Toasts.ToastWindowDemo"
                 xmlns="https://github.com/avaloniaui"
                 xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:suki="https://github.com/kikipoulet/SukiUI"
                 Title="Toast Window Demo"
                 Width="720"
                 Height="400">
    <suki:SukiWindow.Hosts>
        <suki:SukiToastHost Name="ToastHost"/>
    </suki:SukiWindow.Hosts>
    <Grid RowDefinitions="Auto,*">
        <suki:GlassCard Classes="HeaderCard">
            <suki:GroupBox Header="Toasts">
                <StackPanel Classes="HeaderContent">
                    <TextBlock>
                        Demos the ability for individual windows (or the main window) to be addressed by the toast system.
                    </TextBlock>
                </StackPanel>
            </suki:GroupBox>
        </suki:GlassCard>
        <ScrollViewer Grid.Row="1">
            <WrapPanel Classes="PageContainer">
                <suki:GlassCard>
                    <suki:GroupBox Header="Show In This Window">
                        <Button Margin="15,10,15,0"
                                Click="ShowToastInThisWindowClicked"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Show In MainWindow">
                        <Button Margin="15,10,15,0"
                                Click="ShowToastInMainWindowClicked"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
                <suki:GlassCard>
                    <suki:GroupBox Header="Show In Both Windows">
                        <Button Margin="15,10,15,0"
                                Click="ShowToastInBothWindowsClicked"
                                Content="Show Toast" />
                    </suki:GroupBox>
                </suki:GlassCard>
            </WrapPanel>
        </ScrollViewer>
    </Grid>
</suki:SukiWindow>
