﻿<Styles xmlns="https://github.com/avaloniaui"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:glassMorphism="clr-namespace:SukiUI.Controls.GlassMorphism"
        xmlns:theme="clr-namespace:Suki<PERSON>.Theme"
        xmlns:suki="https://github.com/kikipoulet/SukiUI">

    <Design.PreviewWith>
        <Border Background="Transparent" Padding="20">
            <StackPanel Background="AliceBlue" Orientation="Horizontal" Spacing="10">
                <TextBox Text="TEst"></TextBox>
                <ComboBox Margin="0,0,50,0" PlaceholderText="Select an item">
                    <ComboBoxItem>Item 1</ComboBoxItem>
                    <ComboBoxItem>Item 2</ComboBoxItem>
                </ComboBox>
                <ComboBox Width="200"
                          HorizontalContentAlignment="Center"
                          IsEnabled="False"
                          SelectedIndex="1">
                    <ComboBoxItem>Item 1</ComboBoxItem>
                    <ComboBoxItem>Item 2</ComboBoxItem>
                </ComboBox>
            </StackPanel>
        </Border>
    </Design.PreviewWith>

    <Style Selector="ComboBox">
        <Setter Property="BorderThickness" Value="1,1,1,1" />
        <Setter Property="BorderBrush" Value="{DynamicResource SukiControlBorderBrush}" />
        <Setter Property="CornerRadius" Value="{DynamicResource SmallCornerRadius}" />
        <Setter Property="Background" Value="{DynamicResource SukiBackground}" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="6,6,-6,6" />
        <Setter Property="MinHeight" Value="45" />
        <Setter Property="PlaceholderForeground" Value="{DynamicResource SukiLowText}" />
        <Setter Property="ScrollViewer.HorizontalScrollBarVisibility" Value="Disabled" />
        <Setter Property="Template">
            <ControlTemplate>
                <Border Margin="0,0,0,0" Padding="5">
                    <Border.Styles>
                               
                        

                                <Style Selector="ContentControl.ghost TextBlock">
                                    <Setter Property="Foreground" Value="Transparent" />
                                </Style>
                                <Style Selector="ToggleButton:pointerover">
                                    <Setter Property="Background" Value="Transparent" />
                                </Style>
                                <Style Selector="ToggleButton:checked">
                                    <Setter Property="Background" Value="Transparent" />
                                </Style>
                            </Border.Styles>
                    <suki:GlassCard Name="border"
                            Classes="Discrete" Padding="{TemplateBinding Padding}"
                            BorderBrush="{TemplateBinding BorderBrush}"
                            BorderThickness="{TemplateBinding BorderThickness}"
                           
                            CornerRadius="{TemplateBinding CornerRadius}">
                        <suki:GlassCard.Transitions>
                            <Transitions>
                                <BrushTransition Property="BorderBrush" Duration="0:0:0.2" />
                            </Transitions>
                        </suki:GlassCard.Transitions>
                        <Grid ColumnDefinitions="*,Auto">
                            
                            <Grid.Resources>
                                <theme:BiggestItemConverter x:Key="BigItem" />
                            </Grid.Resources>
                            <TextBlock Name="PlaceholderTextBlock"
                                       Grid.Column="0"
                                       Margin="7,1,0,0"
                                       HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}"
                                       VerticalAlignment="Center"
                                       Foreground="{TemplateBinding PlaceholderForeground}"
                                       IsVisible="{TemplateBinding SelectionBoxItem,
                                                                   Converter={x:Static ObjectConverters.IsNull}}"
                                       Text="{TemplateBinding PlaceholderText}" />
                            <ContentControl Margin="7,1,0,0"
                                            HorizontalContentAlignment="Left"
                                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Classes="ghost"
                                            Content="{TemplateBinding ItemsSource,
                                                                      Converter={StaticResource BigItem}}"
                                            ContentTemplate="{TemplateBinding ItemTemplate}"
                                            Foreground="Transparent" />
                            <ContentControl Margin="7,1,0,0"
                                            HorizontalContentAlignment="Left"
                                            VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                            Content="{TemplateBinding SelectionBoxItem}"
                                            ContentTemplate="{TemplateBinding ItemTemplate}"
                                            Foreground="{DynamicResource SukiText}" />
                            <ToggleButton Name="toggle"
                                          Grid.Column="1"
                                          Margin="10,0,0,0"
                                          Background="Transparent"
                                          BorderThickness="0"
                                          ClickMode="Press"
                                          CornerRadius="{DynamicResource SmallCornerRadius}"
                                          Focusable="False"
                                          IsChecked="{TemplateBinding IsDropDownOpen,
                                                                      Mode=TwoWay}">
                                <Path Width="8"
                                      Height="4"
                                      HorizontalAlignment="Center"
                                      VerticalAlignment="Center"
                                      Data="F1 M 301.14,-189.041L 311.57,-189.041L 306.355,-182.942L 301.14,-189.041 Z"
                                      Fill="{DynamicResource SukiText}"
                                      Stretch="Uniform" />
                            </ToggleButton>
                            <Popup Name="PART_Popup"
                                   MaxHeight="{TemplateBinding MaxDropDownHeight}"  MinWidth="{Binding Bounds.Width, RelativeSource={RelativeSource TemplatedParent}}"
                                   HorizontalOffset="-5"
                                   IsLightDismissEnabled="True"
                                   IsOpen="{TemplateBinding IsDropDownOpen,
                                                            Mode=TwoWay}"
                                   PlacementTarget="{TemplateBinding}">
                                <LayoutTransformControl Name="PART_LayoutTransform" RenderTransformOrigin="50%, 0%">

                                <Border Name="B1" Padding="5,10,5,5">
                                    <Panel>
                                        <Border Margin="10,-10,0,0"
                                                Background="{DynamicResource SukiPopupBackground}"
                                                BorderBrush="{DynamicResource SukiMenuBorderBrush}"
                                                BorderThickness="1,1,1,1"
                                                BoxShadow="{DynamicResource SukiSmallPopupShadow}"
                                                CornerRadius="{DynamicResource SmallCornerRadius}">
                                            <Panel Background="{DynamicResource PopupGradientBrush}">
                                            <suki:GlassCard Margin="-1" CornerRadius="{DynamicResource SmallCornerRadius}"></suki:GlassCard>
                                            <ScrollViewer  HorizontalScrollBarVisibility="{TemplateBinding ScrollViewer.HorizontalScrollBarVisibility}" VerticalScrollBarVisibility="{TemplateBinding ScrollViewer.VerticalScrollBarVisibility}">
                                               
                                                <ItemsPresenter Name="PART_ItemsPresenter"
                                                                Margin="0,3,0,0"
                                                                ItemsPanel="{TemplateBinding ItemsPanel}" />
                                            </ScrollViewer>
                                            </Panel>
                                        </Border>
                                    </Panel>
                                </Border>
                                    </LayoutTransformControl>
                            </Popup>
                        </Grid>
                    </suki:GlassCard>
                </Border>
            </ControlTemplate>
        </Setter>
    </Style>
    
    <Style Selector="ComboBox[IsDropDownOpen=true] /template/ LayoutTransformControl#PART_LayoutTransform">
        <Style.Resources>
            <theme:PlusNineConverter x:Key="NineConverter" />
        </Style.Resources>
        <Style.Animations>
            <Animation Easing="SineEaseOut"
                       FillMode="Forward"
                       Duration="0:0:0.3">
                <KeyFrame Cue="0%">
                    <Setter Property="ScaleTransform.ScaleY" Value="0.92" />
                    <Setter Property="ScaleTransform.ScaleX" Value="0.95" />
                    <Setter Property="Opacity" Value="0" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="ScaleTransform.ScaleY" Value="1" />
                    <Setter Property="ScaleTransform.ScaleX" Value="1" />
                    <Setter Property="Opacity" Value="1" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
    </Style>
    
    <Style Selector="ComboBox[IsDropDownOpen=false] /template/ LayoutTransformControl#PART_LayoutTransform">
        <Style.Resources>
            <theme:PlusNineConverter x:Key="NineConverter" />
        </Style.Resources>
        <Style.Animations>
            <Animation Easing="CircularEaseOut"
                       FillMode="Forward"
                       Duration="0:0:0.3">
                <KeyFrame Cue="0%">
                    <Setter Property="ScaleTransform.ScaleY" Value="1" />
                    <Setter Property="ScaleTransform.ScaleX" Value="1" />
                    <Setter Property="Opacity" Value="1" />
                </KeyFrame>
                <KeyFrame Cue="100%">
                    <Setter Property="ScaleTransform.ScaleY" Value="0.9" />
                    <Setter Property="ScaleTransform.ScaleX" Value="0.9" />
                    <Setter Property="Opacity" Value="0" />
                </KeyFrame>
            </Animation>
        </Style.Animations>
    </Style>
    
    <Style Selector="ComboBox:pointerover /template/ suki|GlassCard#border">
        <Setter Property="BorderBrush" Value="{DynamicResource SukiLightBorderBrush}" />
    </Style>
    <Style Selector="ComboBox:disabled /template/ suki|GlassCard#border">
        <Setter Property="Opacity" Value="{DynamicResource ThemeDisabledOpacity}" />
    </Style>

</Styles>