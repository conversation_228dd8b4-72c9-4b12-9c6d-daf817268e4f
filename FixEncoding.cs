using System;
using System.IO;
using System.Text;
using System.Collections.Generic;

class FixEncoding
{
    static void Main(string[] args)
    {
        string filePath = @"SukiUI.Demo\Features\CarControl\CarControlView.axaml.cs";
        
        if (!File.Exists(filePath))
        {
            Console.WriteLine($"文件不存在: {filePath}");
            return;
        }

        // 备份原文件
        string backupPath = filePath + ".backup";
        File.Copy(filePath, backupPath, true);
        Console.WriteLine($"已备份到: {backupPath}");

        // 定义替换映射
        var replacements = new Dictionary<string, string>
        {
            {"????????????", "初始化车辆模型"},
            {"????????", "初始化地图"},
            {"???????????????", "初始化时也要调用一次"},
            {"??????", "初始化信号"},
            {"?????", "上传图片"},
            {"???????????", "界面初始化成功"},
            {"???????ModelB?????:", "收到来自ModelB的消息:"},
            {"????????????{mapFilePath}", "没有找到地图文件{mapFilePath}"},
            {"Dialog-Exit????????????{mapFilePath}", "Dialog-Exit没有找到地图文件{mapFilePath}"},
            {"???????????????????", "是否允许鼠标滚轮缩放"},
            {"????????,", "先执行一次,"},
            {"??????????????????????????????????????????????????", "暂时没有找到地图的变化事件，所以使用地图刷新事件来代替实现。"},
            {"???????????????????", "添加事件,但是这个会导致地图卡顿严重"},
            {"??????????????????", "监听地图的缩放注册图片"},
            {"????????A2????", "获取A2车辆"},
            {"=1??????", "=1表示虚线传感器"},
            {"????????", "否则为实线"},
            {"????????????????", "将中文的横线替换为英文"},
            {"pointstr???", "pointstr为空"},
            {"????????", "车辆（无初始值）"},
            {"Linux??????", "Linux下不可用"},
            {"????????????????????????????????????", "这里是厘米计算，车辆定位是按照厘米标定的"},
            {"????????????{ex.Message}{ex.StackTrace}", "加载车模型出错{ex.Message}{ex.StackTrace}"},
            {"???", "调试"},
            {"????", "正式"},
            {"????UPD????", "记录UPD数据"},
            {"??????????????????????????????????", "会导致从串口读多个数据时，无法保证数据是哪个时刻的数据"},
            {"??????????????????2???????????????", "这里是考试科目，科目2和科目3可以共用数据"},
            {"????????", "可能考试项目序号"},
            {"????????", "车辆编码"},
            {"????????", "车辆状态"},
            {"???????????", "身份证号码"},
            {"??????", "考试员"},
            {"????????", "考试科目"},
            {"????????????", "设备信号状态"},
            {"????????", "当前速度"},
            {"????????????", "当前累计里程"},
            {"????????", "发动机转速"},
            {"GPS ???????", "GPS 东向距离"},
            {"GPS ???????", "GPS 北向距离"},
            {"GPS ???????", "GPS 天向距离"},
            {"?????", "航向角"},
            {"??????", "俯仰角"},
            {"?????", "横滚角"},
            {"????????", "项目状态"},
            {"???????????", "设备编号"},
            {"????????", "扣分分值"},
            {"????????", "扣分数量"},
            {"????????", "扣分项目"},
            {"????????", "实时成绩"},
            {"????????????", "设备信号信息"},
            {"????????????????????????????{xhstr}??{ex.Message}{ex.StackTrace}", "移动车辆数据出错{xhstr}：{ex.Message}{ex.StackTrace}"},
            {"????????????????????????????", "航向角（相对于真北的角度）"},
            {"20220413 ???????????????", "20220413 这里不计算俯仰角，横滚角"},
            {"????????????cd[0]????????cd[3]", "这里应该使用cd[0]，而不应该是cd[3]"},
            {"??A2????", "A2车头"},
            {"??A2??", "A2挂车"},
            {"??????????????????", "将中心点设为车头的中心点"},
            {"????", "绘制"},
            {"????A2????", "绘制A2轨迹"},
            {"????????", "绘制地图"},
            {"????A2????", "绘制A2轨迹"},
            {"????????", "车辆复选框"},
            {"????????", "绘制复选框"},
            {"????????????Layer??????Null??????????????????", "如果设置这个Layer的样式为Null，则不显示点，而是显示一个外环。"},
            {"????????????", "先不计算转向角度"},
            {"????????", "绘制图片层"},
            {"????????", "绘制轮廓"},
            {"1. ????????", "1. 目标坐标范围"},
            {"2. ????????", "2. 获取图片"},
            {"4. ???????? Style", "4. 创建自定义 Style"},
            {"??????Mapsui???????", "使用Mapsui清除重叠"},
            {"????????", "绘制图片"},
            {"????????", "绘制车头"},
            {"????a2", "绘制a2"},
            {"????A2????????", "绘制A2轨迹失败"},
            {"????????????????????????", "只启动一个线程"},
            {"logRunning ????????????????", "logRunning 长时间运行线程不会停"},
            {"??????????????????????????????????", "当前的科目不正确，暂时不能上传图片"},
            {"????????????{ex.Message}", "上传轨迹文件失败:{ex.Message}"},
            {"????????????", "使用其他的PanelCar绘制图，没有什么内容，只有空白背景，所以不建议原因"},
            {"????????{ex.Message}", "删除文件失败:{ex.Message}"},
            {"????????????????????????????????????", "当地方配置文件没有配置相应的值时，会导致线程一直刷新，使用默认值"},
            {"?? ??????", "检查考试"},
            {"??????????????????", "考试成绩图片所在的图片窗口"},
            {"????????????2????", "轨迹图片默认为窗口序列中的2窗口"},
            {"??????(???)??1????????+1????????+2???????", "窗口号(组合)：1字节设备号+1字节保留号+2字节窗口号"},
            {"????????????2????", "采集图片默认为窗口序列中的2窗口"},
            {"????????????{ex.Message}", "上传删除文件失败:{ex.Message}"},
            {"????????????{ex.Message}", "生成采集图片失败:{ex.Message}"},
            {"???????????????", "设置轨迹上传失败信息"},
            {"???????????????", "设置图片上传失败信息"}
        };

        try
        {
            // 读取文件内容
            string content = File.ReadAllText(filePath, Encoding.GetEncoding("GB2312"));
            
            // 执行替换
            foreach (var replacement in replacements)
            {
                content = content.Replace(replacement.Key, replacement.Value);
            }
            
            // 保存文件
            File.WriteAllText(filePath, content, Encoding.GetEncoding("GB2312"));
            
            Console.WriteLine("修复完成！");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"修复失败: {ex.Message}");
        }
        
        Console.WriteLine("按任意键退出...");
        Console.ReadKey();
    }
}
