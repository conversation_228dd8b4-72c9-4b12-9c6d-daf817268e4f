﻿using Mapsui.Layers;
using Mapsui.Styles;
using Mapsui;
using SkiaSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static Mapsui.Providers.Wfs.Utilities.WfsFeatureTypeInfo;
using Mapsui.Extensions;
using NetTopologySuite.Geometries;
using BruTile.Tms;

namespace SukiUI.Demo.Bll
{



    public class StretchBitmapStyle : Style,IStyle
    {
        public List<Coordinate> DataList { get; set; }
        public Polygon Myolygon { get; set; }
        //public StretchBitmapStyle(List<Coordinate> dataList, NetTopologySuite.Geometries.Polygon polygon)
        //{
        //    this.DataList = dataList;
        //    Myolygon = polygon;
        //}
        //public StretchBitmapStyle()
        //{
            
        //}
        public byte[] ImageBytes { get; set; } = new byte[0]; // 默认值
        public BoundingBox TargetBBox { get; set; } = new BoundingBox(); // 默认值// 目标地理矩形
        public double Rotation { get; set; } = 0;   // 旋转角度（度）
        //double IStyle.MinVisible {  }
        //double IStyle.MaxVisible { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        //bool IStyle.Enabled { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        //float IStyle.Opacity { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

        //public double MinVisible { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        //public double MaxVisible { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        //public bool Enabled { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }
        //public float Opacity { get => throw new NotImplementedException(); set => throw new NotImplementedException(); }

        public void Render(SKCanvas canvas, Viewport viewport)
        {
            if (ImageBytes == null || TargetBBox == null) return;

            using var bitmap = SKBitmap.Decode(ImageBytes);

            // 计算地理矩形四角的屏幕坐标
            //var min = viewport.WorldToScreenXY(new Point(TargetBBox.Min.X, TargetBBox.Min.Y));
            //var max = viewport.WorldToScreenXY(new Point(TargetBBox.Max.X, TargetBBox.Max.Y));

            //float left = (float)Math.Min(min.X, max.X);
            //float top = (float)Math.Min(min.Y, max.Y);
            //float right = (float)Math.Max(min.X, max.X);
            //float bottom = (float)Math.Max(min.Y, max.Y);
            var envelope = Myolygon.EnvelopeInternal;
            var mrect = new MRect(envelope.MinX, envelope.MinY, envelope.MaxX, envelope.MaxY);
            var temp = viewport.WorldToScreen(mrect);
            
            var destRect = new SKRect((float)temp.Left, (float)temp.Top, (float)temp.Right, (float)temp.Bottom);
            var center = viewport.ScreenToWorldXY(envelope.Centre.X, envelope.Centre.Y);
            canvas.Save();
            if (Rotation != 0)
            {
                // 以中心点旋转
                //float cx = (left + right) / 2;
                //float cy = (top + bottom) / 2;
                float cx = (float)center.worldX;
                float cy = (float)center.worldY;


                canvas.Translate(cx, cy);
                canvas.RotateDegrees((float)Rotation);
                canvas.Translate(-cx, -cy);
            }
            canvas.DrawBitmap(bitmap, destRect);
            canvas.Restore();
        }
    }





}
