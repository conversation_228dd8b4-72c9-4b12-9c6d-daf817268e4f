<ResourceDictionary xmlns="https://github.com/avaloniaui" xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    <ControlTheme x:Key="SukiHyperlinkButtonStyle" TargetType="HyperlinkButton">
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BackgroundSizing" Value="OuterBorderEdge" />
        <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor}" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="BorderThickness" Value="0" />
        <Setter Property="CornerRadius" Value="0" />
        <Setter Property="Padding" Value="0" />
        <Setter Property="HorizontalAlignment" Value="Left" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="RenderTransform" Value="none" />
        <Setter Property="TextBlock.TextDecorations" Value="Underline" />
        <Setter Property="Transitions">
            <Transitions>
                <TransformOperationsTransition Property="RenderTransform" Duration="0:0:.075" />
            </Transitions>
        </Setter>
        <Setter Property="Template">
            <ControlTemplate>
                <ContentPresenter x:Name="PART_ContentPresenter"
                                  Padding="{TemplateBinding Padding}"
                                  HorizontalContentAlignment="{TemplateBinding HorizontalContentAlignment}"
                                  VerticalContentAlignment="{TemplateBinding VerticalContentAlignment}"
                                  Background="{TemplateBinding Background}"
                                  BackgroundSizing="{TemplateBinding BackgroundSizing}"
                                  BorderBrush="{TemplateBinding BorderBrush}"
                                  BorderThickness="{TemplateBinding BorderThickness}"
                                  Content="{TemplateBinding Content}"
                                  ContentTemplate="{TemplateBinding ContentTemplate}"
                                  CornerRadius="{TemplateBinding CornerRadius}"
                                  RecognizesAccessKey="True" />
            </ControlTemplate>
        </Setter>

        <Style Selector="^ /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Transitions">
                <Transitions>
                    <BrushTransition Property="Foreground" Duration="{StaticResource ShortAnimationDuration}" />
                </Transitions>
            </Setter>
        </Style>

        <Style Selector="^:pointerover /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor50}" />
        </Style>

        <Style Selector="^:pressed">
            <Setter Property="RenderTransform" Value="scale(0.95)" />
        </Style>

        <Style Selector="^:pressed /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="{DynamicResource SukiPrimaryColor75}" />
        </Style>

        <Style Selector="^:disabled /template/ ContentPresenter#PART_ContentPresenter">
            <Setter Property="Foreground" Value="{DynamicResource SukiDisabledText}" />
        </Style>

        <Style Selector="^:visited">
            <Style Selector="^ /template/ ContentPresenter#PART_ContentPresenter">
                <Setter Property="Foreground" Value="{DynamicResource SukiAccentColor}" />
            </Style>
            <Style Selector="^:pointerover /template/ ContentPresenter#PART_ContentPresenter">
                <Setter Property="Foreground" Value="{DynamicResource SukiAccentDarkColor}" />
            </Style>
            <Style Selector="^:pressed /template/ ContentPresenter#PART_ContentPresenter">
                <Setter Property="Foreground" Value="{DynamicResource SukiAccentColor75}" />
            </Style>
        </Style>
    </ControlTheme>
    <ControlTheme x:Key="{x:Type HyperlinkButton}"
                  BasedOn="{StaticResource SukiHyperlinkButtonStyle}"
                  TargetType="HyperlinkButton" />
</ResourceDictionary>