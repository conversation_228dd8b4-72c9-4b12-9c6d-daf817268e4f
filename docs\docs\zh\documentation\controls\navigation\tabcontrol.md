# Tab Control

`TabControl` 可以通过切换 `TabItem` 以展现多种内容

::: tip
`TabStripPlacement` 属性是可用的
:::

## 展示

<img src="/controls/layout/tabcontrol.webp"/>

## 示例

```xml
<TabControl>
    <TabItem Header="Tab 1">
        <!-- Content -->
    </TabItem>
    <TabItem Header="Tab 2">
        <!-- Content -->
    </TabItem>
</TabControl>
```

## 参阅

[Demo: SukiUI.Demo/Features/ControlsLibrary/TabControl/TabControlView.axaml](https://github.com/kikipoulet/SukiUI/blob/main/SukiUI.Demo/Features/ControlsLibrary/TabControl/TabControlView.axaml)