using System;
using System.Threading;
using System.Threading.Tasks;
using SukiUI.Demo.Infrastructure;

namespace SukiUI.Demo
{
    /// <summary>
    /// 崩溃检测测试程序
    /// </summary>
    public static class TestCrashDetection
    {
        /// <summary>
        /// 运行崩溃检测测试
        /// </summary>
        public static void RunTests()
        {
            // 初始化崩溃检测系统
            try
            {
                CrashLogger.Initialize();
                ProcessMonitor.StartMonitoring();
                Console.WriteLine("崩溃检测系统初始化成功");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"崩溃检测系统初始化失败: {ex.Message}");
                return;
            }

            Console.WriteLine("=== 崩溃检测系统测试 ===");
            Console.WriteLine("1. 测试未处理异常");
            Console.WriteLine("2. 测试应用程序域异常");
            Console.WriteLine("3. 测试任务异常");
            Console.WriteLine("4. 测试正常退出");
            Console.WriteLine("0. 退出测试");
            
            while (true)
            {
                Console.Write("请选择测试项目 (0-4): ");
                var input = Console.ReadLine();
                
                switch (input)
                {
                    case "1":
                        TestUnhandledException();
                        break;
                    case "2":
                        TestAppDomainException();
                        break;
                    case "3":
                        TestTaskException();
                        break;
                    case "4":
                        TestNormalExit();
                        return;
                    case "0":
                        return;
                    default:
                        Console.WriteLine("无效选择，请重试");
                        break;
                }
                
                Console.WriteLine("等待3秒后继续...");
                Thread.Sleep(3000);
            }
        }

        /// <summary>
        /// 测试未处理异常
        /// </summary>
        private static void TestUnhandledException()
        {
            Console.WriteLine("触发未处理异常测试...");
            CrashLogger.LogTestEvent("UnhandledException", "Starting unhandled exception test");
            
            // 在后台线程中抛出异常
            var thread = new Thread(() =>
            {
                Thread.Sleep(1000);
                throw new InvalidOperationException("这是一个测试用的未处理异常");
            });
            
            thread.Start();
            thread.Join();
        }

        /// <summary>
        /// 测试应用程序域异常
        /// </summary>
        private static void TestAppDomainException()
        {
            Console.WriteLine("触发应用程序域异常测试...");
            CrashLogger.LogTestEvent("AppDomainException", "Starting AppDomain exception test");
            
            // 直接抛出异常
            throw new ArgumentException("这是一个测试用的应用程序域异常");
        }

        /// <summary>
        /// 测试任务异常
        /// </summary>
        private static void TestTaskException()
        {
            Console.WriteLine("触发任务异常测试...");
            CrashLogger.LogTestEvent("TaskException", "Starting Task exception test");
            
            // 创建一个会抛出异常的任务
            var task = Task.Run(() =>
            {
                Thread.Sleep(1000);
                throw new NotImplementedException("这是一个测试用的任务异常");
            });
            
            try
            {
                task.Wait();
            }
            catch (AggregateException ex)
            {
                // 手动记录异常
                CrashLogger.LogUnhandledException(ex.InnerException ?? ex, "Task Exception Test");
                Console.WriteLine($"捕获到任务异常: {ex.InnerException?.Message}");
            }
        }

        /// <summary>
        /// 测试正常退出
        /// </summary>
        private static void TestNormalExit()
        {
            Console.WriteLine("测试正常退出...");
            CrashLogger.LogTestEvent("NormalExit", "Testing normal exit");
            ProcessMonitor.LogCurrentSnapshot("Normal Exit Test");
            CrashLogger.LogProcessExit("Normal exit test completed");
            Console.WriteLine("正常退出测试完成");
        }

        /// <summary>
        /// 测试内存泄漏检测
        /// </summary>
        public static void TestMemoryLeak()
        {
            Console.WriteLine("开始内存泄漏测试...");
            CrashLogger.LogTestEvent("MemoryLeak", "Starting memory leak test");
            
            var list = new System.Collections.Generic.List<byte[]>();
            
            for (int i = 0; i < 10; i++)
            {
                // 每次分配10MB
                var data = new byte[10 * 1024 * 1024];
                list.Add(data);
                
                Console.WriteLine($"已分配 {(i + 1) * 10} MB 内存");
                ProcessMonitor.LogCurrentSnapshot($"Memory allocation step {i + 1}");
                
                Thread.Sleep(1000);
            }
            
            Console.WriteLine("内存泄漏测试完成");
        }

        /// <summary>
        /// 测试线程泄漏检测
        /// </summary>
        public static void TestThreadLeak()
        {
            Console.WriteLine("开始线程泄漏测试...");
            CrashLogger.LogTestEvent("ThreadLeak", "Starting thread leak test");
            
            for (int i = 0; i < 20; i++)
            {
                var thread = new Thread(() =>
                {
                    // 线程保持活跃
                    while (true)
                    {
                        Thread.Sleep(1000);
                    }
                });
                
                thread.IsBackground = true;
                thread.Start();
                
                Console.WriteLine($"已创建 {i + 1} 个线程");
                ProcessMonitor.LogCurrentSnapshot($"Thread creation step {i + 1}");
                
                Thread.Sleep(500);
            }
            
            Console.WriteLine("线程泄漏测试完成");
        }
    }
}
