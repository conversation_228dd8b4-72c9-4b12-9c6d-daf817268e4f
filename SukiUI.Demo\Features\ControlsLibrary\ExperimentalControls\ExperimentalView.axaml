﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:experimental="clr-namespace:SukiUI.Controls.Experimental;assembly=SukiUI"
             xmlns:suki="https://github.com/kikipoulet/SukiUI"
             xmlns:controlsLibrary="clr-namespace:SukiUI.Demo.Features.ControlsLibrary"
             xmlns:desktopEnvironment="clr-namespace:SukiUI.Controls.Experimental.DesktopEnvironment;assembly=SukiUI"
             xmlns:objectModel="clr-namespace:System.Collections.ObjectModel;assembly=System.ObjectModel"
             xmlns:apps="clr-namespace:SukiUI.Demo.Features.ControlsLibrary.ExperimentalControls.Apps"
             x:DataType="controlsLibrary:ExperimentalViewModel"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="SukiUI.Demo.Features.ControlsLibrary.ExperimentalView">
    

        <TabControl Margin="25">
            <TabItem Header="Chat UI">
                <ScrollViewer>
                <WrapPanel  Orientation="Horizontal">
                <suki:GlassCard Margin="25" HorizontalAlignment="Left" VerticalAlignment="Top">
                    <suki:GroupBox Header="ChatView">
                        <experimental:ChatUI Margin="0,15,0,0" Messages="{Binding Messages}"  FriendImageSource="/Assets/cat-modified.png" UserImageSource="/Assets/OIG.N5o-removebg-preview.png" Height="450" Width="600" />
                    </suki:GroupBox>
                </suki:GlassCard>
                    <StackPanel>
                        <Button Margin="30,25,30,10" Classes="Flat" VerticalAlignment="Top" Command="{Binding AddFriendMessageCommand}">
                            Receive Friend Message
                        </Button>
                    
                    <Button Margin="30,10" Classes="Flat" VerticalAlignment="Top" Command="{Binding AddFriendFileCommand}">
                        Receive Friend File
                    </Button>
                    </StackPanel>
                </WrapPanel>
                </ScrollViewer>
            </TabItem>
            <TabItem Header="Fake Desktop Environment">
                <Panel>
                <Border  BoxShadow="0 0 20 5 #66f5222d" Margin="25">
                    <desktopEnvironment:SukiDesktopEnvironment  DesktopBackgroundImageSource="/Assets/desktopbackground.jpg" HomeImageSource="/Assets/OIG.N5o-removebg-preview.png" >
                        <desktopEnvironment:SukiDesktopEnvironment.Softwares>
                        <objectModel:ObservableCollection x:TypeArguments="desktopEnvironment:SDESoftware">
                            <desktopEnvironment:SDESoftware Icon="/Assets/Icons/notes.png" Name="Notes" Content="apps:Notes" ></desktopEnvironment:SDESoftware>
                            <desktopEnvironment:SDESoftware Icon="/Assets/Icons/mail.png" Name="Mail" Content="apps:MailApp" ></desktopEnvironment:SDESoftware>
                        </objectModel:ObservableCollection>
                        </desktopEnvironment:SukiDesktopEnvironment.Softwares>
                    </desktopEnvironment:SukiDesktopEnvironment>
                </Border>
                    </Panel>
            </TabItem>
        </TabControl>

</UserControl>
