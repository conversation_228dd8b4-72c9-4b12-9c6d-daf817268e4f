﻿<UserControl xmlns="https://github.com/avaloniaui"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
             xmlns:dockControls="clr-namespace:SukiUI.Demo.Features.ControlsLibrary.DockControls"
             mc:Ignorable="d" d:DesignWidth="800" d:DesignHeight="450"
             x:Class="SukiUI.Demo.Features.ControlsLibrary.DockView">
  
    
    <Grid RowDefinitions="Auto,*" Margin="15">
<!--
    <Menu Grid.Row="0">
      <MenuItem Header="_File">
        <MenuItem x:Name="FileOpenLayout" Header="_Open layout..." Click="FileOpenLayout_OnClick" />
        <Separator/>
        <MenuItem x:Name="FileSaveLayout" Header="_Save layout..." Click="FileSaveLayout_OnClick" />
        <Separator/>
        <MenuItem x:Name="FileCloseLayout" Header="_Close layout" Click="FileCloseLayout_OnClick" />
      </MenuItem>
    </Menu>
-->
    <DockControl x:Name="Dock" Grid.Row="1" InitializeLayout="True" InitializeFactory="True">
      <DockControl.Factory>
        <Factory />
      </DockControl.Factory>

      <RootDock x:Name="Root" Id="Root" IsCollapsable="False" DefaultDockable="{Binding #MainLayout}">

        <!-- Windows -->

        <RootDock.Windows>
          <!--<DockWindow x:Name="DockWindow" Id="DockWindow" X="281" Y="233" Width="250" Height="400" Topmost="True">
            <RootDock ActiveDockable="{Binding #ToolDock}" Window="{Binding #DockWindow}">
              <ToolDock x:Name="ToolDock">
                <Tool x:Name="ToolWindow" Id="ToolWindow" Title="Tool Window" x:DataType="Tool">
                  <TextBlock Text="{Binding Title}"/>
                </Tool>
              </ToolDock>
            </RootDock>
          </DockWindow>-->
        </RootDock.Windows>

        <ProportionalDock x:Name="MainLayout" Id="MainLayout" Orientation="Horizontal">

          <!-- Left Pane -->

          <ToolDock x:Name="LeftPane" Id="LeftPane" Proportion="0.2" Alignment="Left">
            <Tool x:Name="SolutionExplorer" Id="SolutionExplorer" Title="Solution Explorer" x:DataType="Tool">
             <dockControls:SolutionExplore Margin="0,15,0,0"></dockControls:SolutionExplore>
            </Tool>
          </ToolDock>

          <ProportionalDockSplitter x:Name="LeftSplitter" Id="LeftSplitter" />

          <!-- Top Pane -->

          <ProportionalDock x:Name="TopPane" Id="TopPane" Orientation="Vertical">

            <!-- Right Pane -->

            <ProportionalDock x:Name="RightPane" Id="RightPane" Orientation="Horizontal">

              <!-- Documents Pane -->

              <DocumentDock x:Name="DocumentsPane" Id="DocumentsPane" CanCreateDocument="True" ActiveDockable="Document1">
                <DocumentDock.DocumentTemplate>
                  <DocumentTemplate>
                    <StackPanel x:DataType="Document">
                      <TextBlock Text="Title"/>
                      <TextBox Text="{Binding Title}"/>
                      <TextBlock Text="Context"/>
                      <TextBox Text="{Binding Context}"/>
                    </StackPanel>
                  </DocumentTemplate>
                </DocumentDock.DocumentTemplate>
                <Document x:Name="Document1" Id="Document1" Title="Program.cs" x:DataType="Document">
                <dockControls:DocumentText></dockControls:DocumentText>
                </Document>
                <Document x:Name="Document2" Id="Document2" Title="App.axaml" x:DataType="Document">
                  <dockControls:DocumentText></dockControls:DocumentText>
                </Document>
              </DocumentDock>

              <ProportionalDockSplitter x:Name="RightSplitter" Id="RightSplitter" />

              <!-- Properties Pane -->
              <ToolDock x:Name="PropertiesPane" Id="PropertiesPane" Proportion="0.25" Alignment="Right">
                <Tool x:Name="Properties" Id="Properties" Title="Properties" x:DataType="Tool">
                 <dockControls:PropertiesView></dockControls:PropertiesView>
                </Tool>
              </ToolDock>

            </ProportionalDock>

            <ProportionalDockSplitter x:Name="BottomSplitter" Id="BottomSplitter" />

            <!-- Bottom Pane -->

            <ToolDock x:Name="BottomPane" Id="BottomPane" Proportion="0.3" Alignment="Bottom" ActiveDockable="Output">
              <Tool x:Name="ErrorList" Id="ErrorList" Title="Error List" x:DataType="Tool">
                <dockControls:ErrorList></dockControls:ErrorList>
              </Tool>
              <Tool x:Name="Output" Id="Output" Title="Output" x:DataType="Tool">
                <TextBlock HorizontalAlignment="Left" Margin="15,12" FontSize="13" Foreground="{DynamicResource SukiLowText}" >
                  Build: Dock.Model.Avalonia.Controls.Tool, Id='SolutionExplorer'<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run>SolutionExplorer, Dock.Avalonia.Controls.ToolContentControl<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run> Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers+PointerDeferredContent`1[Avalonia.Controls.Control], DockXamlSample.SolutionExplore<LineBreak></LineBreak>
                  Build: Dock.Model.Avalonia.Controls.Document, Id='Document1'<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run> Document1, Dock.Avalonia.Controls.DocumentContentControl<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run> Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers+PointerDeferredContent`1[Avalonia.Controls.Control], Avalonia.Controls.TextBlock<LineBreak></LineBreak>
                  Build: Dock.Model.Avalonia.Controls.Tool, Id='Properties'<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run> Properties, Dock.Avalonia.Controls.ToolContentControl<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run> Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers+PointerDeferredContent`1[Avalonia.Controls.Control], DockXamlSample.propertiesview<LineBreak></LineBreak>
                  Build: Dock.Model.Avalonia.Controls.Tool, Id='Output'<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run> Output, Dock.Avalonia.Controls.ToolContentControl<LineBreak></LineBreak>
                  <Run Foreground="Green"> [Added] </Run> Avalonia.Markup.Xaml.XamlIl.Runtime.XamlIlRuntimeHelpers+PointerDeferredContent`1[Avalonia.Controls.Control], Avalonia.Controls.TextBlock<LineBreak></LineBreak>
                </TextBlock>
              </Tool>
            </ToolDock>

          </ProportionalDock>

        </ProportionalDock>
      </RootDock>

    </DockControl>

  </Grid>

    
</UserControl>
